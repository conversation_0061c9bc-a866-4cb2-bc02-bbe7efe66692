<!DOCTYPE html>
<html>
<head>
  <title>Admin <PERSON></title>
  <style>
    body {
      width: 350px;
      padding: 15px;
      font-family: Arial, sans-serif;
    }
    button {
      margin-top: 10px;
      padding: 8px 12px;
      width: 100%;
      cursor: pointer;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
    }
    button:hover {
      background-color: #3367d6;
    }
    .hidden {
      display: none;
    }
    .status {
      margin-top: 10px;
      padding: 10px;
      border-radius: 4px;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    input {
      width: 95%;
      padding: 8px;
      margin: 5px 0;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    h2 {
      color: #4285f4;
      margin-top: 0;
    }
    .site-item {
      padding: 8px;
      margin: 5px 0;
      background-color: #f1f3f4;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <h2>Admin Login</h2>

  <!-- Login Form -->
  <div id="loginForm">
    <button id="discordLoginBtn" style="background-color: #5865F2;">Login with Discord</button>
    <div id="loginStatus" class="status hidden"></div>
  </div>

  <!-- Cookie Management -->
  <div id="cookieManager" class="hidden">
    <p>Current Website: <span id="currentSite"></span></p>

    <div style="margin-bottom: 10px;">
      <label for="serverSelect">Select Server:</label>
      <select id="serverSelect" style="width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ccc; border-radius: 4px;">
        <option value="1">Server 1</option>
        <option value="2">Server 2</option>
        <option value="3">Server 3</option>
      </select>
    </div>

    <button id="extractBtn">Extract Cookies from Current Site</button>
    <button id="listSitesBtn">List Managed Sites</button>
    <button id="checkValidityBtn">Check Cookies Validity</button>
    <button id="logoutBtn">Logout</button>

    <div id="actionStatus" class="status hidden"></div>

    <div id="sitesList" class="hidden">
      <h3>Managed Sites</h3>
      <div id="sitesListItems"></div>
    </div>

    <div id="validityReport" class="hidden">
      <h3>Cookies Validity Report</h3>
      <div id="validityItems"></div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
