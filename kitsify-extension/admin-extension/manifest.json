{"manifest_version": 3, "name": "Multi-Site Admin Login Manager", "version": "1.0", "description": "Save cookies from multiple websites after logging in and send them to the server", "permissions": ["cookies", "storage", "activeTab", "tabs", "webRequest"], "host_permissions": ["<all_urls>"], "action": {"default_popup": "popup.html", "default_icon": {"16": "images/icon16.svg", "48": "images/icon48.svg", "128": "images/icon128.svg"}}, "background": {"service_worker": "background.js"}}