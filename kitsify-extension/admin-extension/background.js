// Admin Extension Background Script
// Storage for admin authentication token
let authToken = null;

// Server base URL
const SERVER_URL = "http://localhost:3000";

// Encryption utilities for cookie data
function encryptCookies(cookies) {
  // Convert cookies array to JSON string
  return JSON.stringify(cookies);
}

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case "discordLogin":
      discordLogin(sendResponse);
      return true;

    case "logout":
      logout(sendResponse);
      return true;

    case "extractCookies":
      extractCookies(request.tabId, request.serverId, sendResponse);
      return true;

    case "getStatus":
      sendResponse({
        isLoggedIn: authToken !== null,
        token: authToken
      });
      return true;

    case "checkServerLoginStatus":
      checkServerLoginStatus(sendResponse);
      return true;

    case "checkCookiesValidity":
      checkCookiesValidity(sendResponse);
      return true;
  }
});

// Login function removed - using Discord login only

// Discord login function
async function discordLogin(sendResponse) {
  try {
    // Open Discord auth window
    const discordAuthUrl = `${SERVER_URL}/auth/discord`;
    const authWindow = await chrome.windows.create({
      url: discordAuthUrl,
      type: 'popup',
      width: 600,
      height: 800
    });

    // Set up listener for auth completion
    chrome.tabs.onUpdated.addListener(async function authListener(_, changeInfo, tab) {
      // Check if this is our auth window and if it's completed loading
      if (tab.windowId === authWindow.id && changeInfo.status === 'complete') {
        // Check if we're on the callback page
        if (tab.url.includes('/auth/discord/callback')) {
          // Get cookies from the auth domain
          const cookies = await chrome.cookies.getAll({ url: SERVER_URL });
          const accessTokenCookie = cookies.find(cookie => cookie.name === 'access_token');

          if (accessTokenCookie) {
            // Store the token
            authToken = accessTokenCookie.value;

            // Close the auth window
            chrome.windows.remove(authWindow.id);

            // Remove this listener
            chrome.tabs.onUpdated.removeListener(authListener);

            // Notify popup of success with the token
            chrome.runtime.sendMessage({ type: 'DISCORD_AUTH_SUCCESS', token: authToken });
            sendResponse({ success: true, token: authToken });
          } else {
            // Auth failed
            chrome.windows.remove(authWindow.id);
            chrome.tabs.onUpdated.removeListener(authListener);
            chrome.runtime.sendMessage({ type: 'DISCORD_AUTH_FAILED' });
            sendResponse({ success: false, error: 'Failed to get authentication token' });
          }
        }
      }
    });

    sendResponse({ success: true });
  } catch (error) {
    console.error('Discord login error:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Logout function
function logout(sendResponse) {
  authToken = null;
  sendResponse({ success: true });
}

// Extract cookies from current tab
async function extractCookies(tabId, serverId, sendResponse) {
  if (!authToken) {
    sendResponse({ success: false, error: "Not authenticated" });
    return;
  }

  try {
    // Get current tab if tabId not provided
    if (!tabId) {
      const tabs = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });
      tabId = tabs[0].id;
    }

    // Get tab URL
    const tab = await chrome.tabs.get(tabId);
    const url = new URL(tab.url);
    const domain = url.hostname;

    // Get all cookies for this domain
    const cookies = await chrome.cookies.getAll({ domain: domain });

    // Encrypt cookies before sending to server
    const encryptedCookies = encryptCookies(cookies);

    // Format cookies for storage according to server's expected format
    const cookieData = {
      website_url: domain,
      cookie_data: encryptedCookies,
      server_id: serverId
    };

    console.log('cookieData: ', cookieData);

    // Send cookies to server
    const response = await fetch(`${SERVER_URL}/cookies/save`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify(cookieData),
    });

    const data = await response.json();

    if (response.ok) {
      sendResponse({
        success: true,
        website: domain,
        cookieCount: cookies.length,
      });
    } else {
      // Check specifically for 401 status code
      if (response.status === 401) {
        // Reset auth token since it's invalid
        authToken = null;
        // Send response with specific unauthorized flag
        sendResponse({
          success: false,
          error: data.message || "Authentication failed",
          statusCode: 401
        });
        // Notify popup to show login form
        chrome.runtime.sendMessage({ type: 'AUTH_UNAUTHORIZED' });
      } else {
        sendResponse({
          success: false,
          error: data.message || "Failed to store cookies",
        });
      }
    }
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

// Check if admin is already logged in on the server
async function checkServerLoginStatus(sendResponse) {
  try {
    console.log('Checking server login status...');

    // Get cookies from the server domain
    const cookies = await chrome.cookies.getAll({ url: SERVER_URL });
    console.log('Cookies found:', cookies);

    const accessTokenCookie = cookies.find(cookie => cookie.name === 'access_token');
    console.log('Access token cookie:', accessTokenCookie);

    if (accessTokenCookie) {
      // Check if the token is valid by making a request to the server
      const response = await fetch(`${SERVER_URL}/auth/status`, {
        method: "GET",
        credentials: "include",
        headers: {
          "Content-Type": "application/json"
        }
      });

      const data = await response.json();
      console.log('Auth status response:', data);

      if (response.ok && data.isAuthenticated && data.user && data.user.role === 'admin') {
        console.log('Admin is authenticated, storing token');
        // Store the token in memory
        authToken = accessTokenCookie.value;

        // Notify popup of success
        chrome.runtime.sendMessage({ type: 'ADMIN_ALREADY_LOGGED_IN' });

        sendResponse({ success: true, isLoggedIn: true });
      } else {
        console.log('Not authenticated as admin');
        sendResponse({ success: true, isLoggedIn: false });
      }
    } else {
      console.log('No access token cookie found');
      sendResponse({ success: true, isLoggedIn: false });
    }
  } catch (error) {
    console.error('Error checking server login status:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Check validity of stored cookies
async function checkCookiesValidity(sendResponse) {
  if (!authToken) {
    sendResponse({ success: false, error: "Not authenticated" });
    return;
  }

  try {
    const response = await fetch(`${SERVER_URL}/cookies/validity`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify({ website_url: "all" }),
    });

    const data = await response.json();

    if (response.ok) {
      sendResponse({
        success: true,
        validCookies: data.validCookies,
        expiredCookies: data.expiredCookies,
      });
    } else {
      // Check specifically for 401 status code
      if (response.status === 401) {
        // Reset auth token since it's invalid
        authToken = null;
        // Send response with specific unauthorized flag
        sendResponse({
          success: false,
          error: data.message || "Authentication failed",
          statusCode: 401
        });
        // Notify popup to show login form
        chrome.runtime.sendMessage({ type: 'AUTH_UNAUTHORIZED' });
      } else {
        sendResponse({
          success: false,
          error: data.message || "Failed to check cookies validity",
        });
      }
    }
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}
