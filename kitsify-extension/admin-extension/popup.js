// DOM elements
const loginForm = document.getElementById("loginForm");
const cookieManager = document.getElementById("cookieManager");
const discordLoginBtn = document.getElementById("discordLoginBtn");
const logoutBtn = document.getElementById("logoutBtn");
const extractBtn = document.getElementById("extractBtn");
const listSitesBtn = document.getElementById("listSitesBtn");
const checkValidityBtn = document.getElementById("checkValidityBtn");
const loginStatus = document.getElementById("loginStatus");
const actionStatus = document.getElementById("actionStatus");
const currentSite = document.getElementById("currentSite");
const sitesList = document.getElementById("sitesList");
const sitesListItems = document.getElementById("sitesListItems");
const validityReport = document.getElementById("validityReport");
const validityItems = document.getElementById("validityItems");
const serverSelect = document.getElementById("serverSelect");

// Check login status when popup opens
document.addEventListener("DOMContentLoaded", async () => {

  // First check if we have a token in memory
  chrome.runtime.sendMessage({ action: "getStatus" }, (response) => {
    if (response.isLoggedIn) {
      showCookieManager();
      updateCurrentSite();
    } else {
      // If no token in memory, check if user is logged in on the server
      chrome.runtime.sendMessage({ action: "checkServerLoginStatus" }, (response) => {
        if (response.success && response.isLoggedIn) {
          // User is logged in on the server, show cookie manager
          showCookieManager();
          updateCurrentSite();
        } else {
          // User is not logged in, show login form
          showLoginForm();
        }
      });
    }
  });
});

// Login with username/password removed - using Discord login only

// Discord login button click handler
discordLoginBtn.addEventListener("click", () => {
  chrome.runtime.sendMessage({ action: "discordLogin" }, (response) => {
    if (response.success) {
      // Discord login window opened, wait for auth completion
      // No need to do anything here, we'll handle the success message in the listener below
    } else {
      showStatus(loginStatus, response.error || "Failed to open Discord login", "error");
    }
  });
});

// Listen for messages from background script
chrome.runtime.onMessage.addListener((message) => {
  if (message.type === "DISCORD_AUTH_SUCCESS" || message.type === "ADMIN_ALREADY_LOGGED_IN") {
    // Discord login successful or admin already logged in, show cookie manager immediately
    showCookieManager();
    updateCurrentSite();
    clearStatus(loginStatus);
  } else if (message.type === "DISCORD_AUTH_FAILED") {
    showStatus(loginStatus, "Discord authentication failed", "error");
  } else if (message.type === "AUTH_UNAUTHORIZED") {
    // Authentication is invalid or expired, show login form
    showLoginForm();
    showStatus(loginStatus, "Session expired. Please login again.", "error");
  }
});

// Logout button click handler
logoutBtn.addEventListener("click", () => {
  chrome.runtime.sendMessage({ action: "logout" }, (response) => {
    if (response.success) {
      showLoginForm();
      clearStatus(loginStatus);
    }
  });
});

// Extract cookies button click handler
extractBtn.addEventListener("click", () => {
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    if (tabs.length === 0) {
      showStatus(actionStatus, "No active tab found", "error");
      return;
    }

    const tabId = tabs[0].id;
    const serverId = parseInt(serverSelect.value);

    chrome.runtime.sendMessage(
      { action: "extractCookies", tabId, serverId },
      (response) => {
        if (response.success) {
          showStatus(
            actionStatus,
            `Successfully extracted ${response.cookieCount} cookies from ${response.website} for Server ${serverId}`,
            "success"
          );
        } else {
          // Check if this is a 401 unauthorized error
          if (response.statusCode === 401) {
            // Show login form
            showLoginForm();
            showStatus(
              loginStatus,
              "Session expired. Please login again.",
              "error"
            );
          } else {
            showStatus(
              actionStatus,
              response.error || "Failed to extract cookies",
              "error"
            );
          }
        }
      }
    );
  });
});

// List sites button click handler
listSitesBtn.addEventListener("click", async () => {
  try {
    // Get the auth token from background script
    const tokenResponse = await new Promise(resolve => {
      chrome.runtime.sendMessage({ action: "getStatus" }, resolve);
    });

    const response = await fetch(`http://localhost:3000/api/cookies`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": tokenResponse.isLoggedIn ? `Bearer ${tokenResponse.token}` : "",
      },
    });

    const data = await response.json();

    if (response.ok) {
      if (data.sites && data.sites.length > 0) {
        sitesListItems.innerHTML = "";

        data.sites.forEach((site) => {
          const siteItem = document.createElement("div");
          siteItem.className = "site-item";
          siteItem.textContent = `${site.website_url} (Last updated: ${new Date(
            site.last_updated
          ).toLocaleString()})`;
          sitesListItems.appendChild(siteItem);
        });

        sitesList.classList.remove("hidden");
        validityReport.classList.add("hidden");
      } else {
        sitesListItems.innerHTML = "<p>No sites found</p>";
        sitesList.classList.remove("hidden");
        validityReport.classList.add("hidden");
      }
    } else {
      // Check if this is a 401 unauthorized error
      if (response.status === 401) {
        // Show login form
        showLoginForm();
        showStatus(
          loginStatus,
          "Session expired. Please login again.",
          "error"
        );
      } else {
        showStatus(
          actionStatus,
          data.message || "Failed to fetch sites",
          "error"
        );
      }
    }
  } catch (error) {
    showStatus(actionStatus, error.message, "error");
  }
});

// Check validity button click handler
checkValidityBtn.addEventListener("click", () => {
  chrome.runtime.sendMessage({ action: "checkCookiesValidity" }, (response) => {
    if (response.success) {
      validityItems.innerHTML = "";

      const validHeader = document.createElement("h4");
      validHeader.textContent = "Valid Cookies:";
      validityItems.appendChild(validHeader);

      if (response.validCookies.length > 0) {
        response.validCookies.forEach((site) => {
          const siteItem = document.createElement("div");
          siteItem.className = "site-item";
          siteItem.textContent = site.website_url;
          validityItems.appendChild(siteItem);
        });
      } else {
        const noValid = document.createElement("p");
        noValid.textContent = "No valid cookies found";
        validityItems.appendChild(noValid);
      }

      const expiredHeader = document.createElement("h4");
      expiredHeader.textContent = "Expired Cookies:";
      validityItems.appendChild(expiredHeader);

      if (response.expiredCookies.length > 0) {
        response.expiredCookies.forEach((site) => {
          const siteItem = document.createElement("div");
          siteItem.className = "site-item";
          siteItem.style.backgroundColor = "#fff3cd";
          siteItem.textContent = `${site.website_url} (Expired: ${new Date(
            site.expiry
          ).toLocaleString()})`;
          validityItems.appendChild(siteItem);
        });
      } else {
        const noExpired = document.createElement("p");
        noExpired.textContent = "No expired cookies found";
        validityItems.appendChild(noExpired);
      }

      validityReport.classList.remove("hidden");
      sitesList.classList.add("hidden");
    } else {
      // Check if this is a 401 unauthorized error
      if (response.statusCode === 401) {
        // Show login form
        showLoginForm();
        showStatus(
          loginStatus,
          "Session expired. Please login again.",
          "error"
        );
      } else {
        showStatus(
          actionStatus,
          response.error || "Failed to check cookies validity",
          "error"
        );
      }
    }
  });
});

// Helper functions
function showLoginForm() {
  loginForm.classList.remove("hidden");
  cookieManager.classList.add("hidden");
}

function showCookieManager() {
  loginForm.classList.add("hidden");
  cookieManager.classList.remove("hidden");
}

function showStatus(element, message, type) {
  element.textContent = message;
  element.className = `status ${type}`;
  element.classList.remove("hidden");
}

function clearStatus(element) {
  element.textContent = "";
  element.className = "status hidden";
}

function updateCurrentSite() {
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    if (tabs.length > 0) {
      const url = new URL(tabs[0].url);
      currentSite.textContent = url.hostname;
    } else {
      currentSite.textContent = "Unknown";
    }
  });
}
