# Installation Guide

This guide explains how to set up and install the Multi-Website Cookie Storage Solution.

## Server Setup

1. Navigate to the server directory:
   ```
   cd server
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Configure environment variables:
   Create a `.env` file in the server directory with the following variables:
   ```
   PORT=3000
   MONGO_URI=mongodb://localhost:27017/cookie-manager
   JWT_SECRET=your-secret-key
   COOKIE_ENCRYPTION_KEY=your-encryption-key
   ```

4. Start the server:
   ```
   npm start
   ```

## Admin Extension Setup

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable Developer Mode
3. Click 'Load unpacked' and select the 'admin-extension' directory
4. The extension icon should appear in your browser toolbar

## Member Extension Setup

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable Developer Mode
3. Click 'Load unpacked' and select the 'member-extension' directory
4. The extension icon should appear in your browser toolbar
