// Script to generate placeholder icons for extensions
const fs = require("fs");
const path = require("path");

// Create directories if they don't exist
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Generate an SVG icon with text and color
function generateSvgIcon(size, text, color) {
  const svg = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="${color}" />
    <text x="50%" y="50%" font-family="Arial" font-size="${size / 3}" 
          text-anchor="middle" dy=".3em" fill="white">${text}</text>
  </svg>`;

  return svg;
}

// Main function to generate icons
function generateIcons() {
  const sizes = [16, 48, 128];
  const extensions = {
    admin: { text: "A", color: "#4285f4" }, // Blue
    member: { text: "M", color: "#0f9d58" }, // Green
  };

  for (const [extType, config] of Object.entries(extensions)) {
    const imagesDir = path.join(__dirname, "..", `${extType}-extension/images`);
    ensureDirectoryExists(imagesDir);

    for (const size of sizes) {
      const svg = generateSvgIcon(size, config.text, config.color);
      const filePath = path.join(imagesDir, `icon${size}.png`);

      // For simplicity, we're just writing SVGs with .png extension
      // In a real project, you would use a library to convert SVG to PNG
      fs.writeFileSync(filePath.replace(".png", ".svg"), svg);
      console.log(`Generated ${filePath.replace(".png", ".svg")}`);
    }
  }

  console.log("Icon generation complete!");
  console.log(
    "NOTE: In a real project, you should convert these SVGs to PNGs."
  );
}

// Run the function
generateIcons();
