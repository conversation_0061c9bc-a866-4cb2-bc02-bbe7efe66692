// Cookie Model
const mongoose = require('mongoose');

// Cookie Schema Definition
const CookieSchema = new mongoose.Schema({
  website_url: {
    type: String,
    required: true,
    unique: true
  },
  encrypted_cookie: {
    type: String,
    required: true
  },
  allowed_members: {
    type: [mongoose.Schema.Types.ObjectId],
    ref: 'User',
    default: []
  },
  last_updated: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('<PERSON>ie', CookieSchema);
