// API Routes for Cookie Management
const express = require("express");
const router = express.Router();
const Cookie = require("./models/Cookie");
const User = require("./models/User");
const { encryptCookies, decryptCookies } = require("./utils/encryption");
const {
  generateToken,
  verifyPassword,
  hashPassword,
  authMiddleware,
  adminMiddleware,
} = require("./utils/auth");

// Create initial admin user if none exists
async function createInitialAdminIfNeeded() {
  try {
    const adminExists = await User.findOne({ role: "admin" });
    if (!adminExists) {
      const hashedPassword = await hashPassword("admin123"); // Default password
      await User.create({
        username: "admin",
        password: hashedPassword,
        role: "admin",
      });
      console.log("Initial admin user created");
    }
  } catch (error) {
    console.error("Error creating initial admin:", error);
  }
}

// Create initial member user if none exists
async function createInitialMemberIfNeeded() {
  try {
    const memberExists = await User.findOne({ role: "member" });
    if (!memberExists) {
      const hashedPassword = await hashPassword("member123"); // Default password
      await User.create({
        username: "member",
        password: hashedPassword,
        role: "member",
      });
      console.log("Initial member user created");
    }
  } catch (error) {
    console.error("Error creating initial member:", error);
  }
}

// Create initial users on server start
createInitialAdminIfNeeded();
createInitialMemberIfNeeded();

// ====== ADMIN ROUTES ======

// Login route for admins
router.post("/admin/login", async (req, res) => {
  try {
    const { username, password } = req.body;

    // Check if user exists
    const user = await User.findOne({ username });
    if (!user) {
      return res.status(400).json({ message: "Invalid credentials" });
    }

    // Check if user is admin
    if (user.role !== "admin") {
      return res
        .status(403)
        .json({ message: "Access denied. Admin role required." });
    }

    // Verify password
    const isMatch = await verifyPassword(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: "Invalid credentials" });
    }

    // Generate token
    const token = generateToken(user);

    res.json({ token });
  } catch (error) {
    console.error("Admin login error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Route to save cookies
router.post("/cookies", adminMiddleware, async (req, res) => {
  try {
    const { website_url, cookies, url } = req.body;

    if (!website_url || !cookies || !Array.isArray(cookies)) {
      return res.status(400).json({ message: "Invalid request data" });
    }

    // Encrypt cookies before storing
    const encrypted_cookie = encryptCookies(cookies);

    // Find and update or create new cookie entry
    const cookieEntry = await Cookie.findOneAndUpdate(
      { website_url },
      {
        encrypted_cookie,
        last_updated: Date.now(),
      },
      { new: true, upsert: true }
    );

    res.json({
      success: true,
      website_url,
      last_updated: cookieEntry.last_updated,
    });
  } catch (error) {
    console.error("Cookie save error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Route to get all cookies (admin only)
router.get("/cookies", adminMiddleware, async (req, res) => {
  try {
    const cookies = await Cookie.find({}, "website_url last_updated");
    res.json({ sites: cookies });
  } catch (error) {
    console.error("Get cookies error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Route to check cookies validity
router.get("/cookies/validity", adminMiddleware, async (req, res) => {
  try {
    const cookies = await Cookie.find({});
    const validCookies = [];
    const expiredCookies = [];

    for (const cookie of cookies) {
      try {
        // Decrypt cookies to check expiry
        const cookieData = decryptCookies(cookie.encrypted_cookie);

        // Check if any cookies are expired
        const now = Date.now() / 1000; // Convert to seconds
        const expired = cookieData.filter(
          (c) => c.expirationDate && c.expirationDate < now
        );

        if (expired.length > 0) {
          expiredCookies.push({
            website_url: cookie.website_url,
            expiry: Math.min(...expired.map((c) => c.expirationDate)) * 1000, // Convert back to ms
          });
        } else {
          validCookies.push({
            website_url: cookie.website_url,
            last_updated: cookie.last_updated,
          });
        }
      } catch (err) {
        // If decryption fails, consider it expired
        expiredCookies.push({
          website_url: cookie.website_url,
          expiry: cookie.last_updated,
        });
      }
    }

    res.json({ validCookies, expiredCookies });
  } catch (error) {
    console.error("Check validity error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Route to delete cookies for a website
router.delete("/cookies/:website", adminMiddleware, async (req, res) => {
  try {
    const website = req.params.website;
    await Cookie.findOneAndDelete({ website_url: website });
    res.json({ success: true });
  } catch (error) {
    console.error("Delete cookies error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Route to update allowed members for a website
router.put("/cookies/:website/access", adminMiddleware, async (req, res) => {
  try {
    const website = req.params.website;
    const { memberIds } = req.body;

    if (!Array.isArray(memberIds)) {
      return res.status(400).json({ message: "memberIds must be an array" });
    }

    // Validate that all memberIds exist and are members
    const members = await User.find({
      _id: { $in: memberIds },
      role: "member",
    });

    if (members.length !== memberIds.length) {
      return res.status(400).json({ message: "Some memberIds are invalid" });
    }

    await Cookie.findOneAndUpdate(
      { website_url: website },
      { allowed_members: memberIds }
    );

    res.json({ success: true });
  } catch (error) {
    console.error("Update access error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// ====== MEMBER ROUTES ======

// Login route for members
router.post("/member/login", async (req, res) => {
  try {
    const { username, password } = req.body;

    // Check if user exists
    const user = await User.findOne({ username });
    if (!user) {
      return res.status(400).json({ message: "Invalid credentials" });
    }

    // Verify password
    const isMatch = await verifyPassword(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: "Invalid credentials" });
    }

    // Generate token
    const token = generateToken(user);

    res.json({ token });
  } catch (error) {
    console.error("Member login error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Route to get all accessible sites for a member
router.get("/member/sites", authMiddleware, async (req, res) => {
  try {
    // Find all cookies where this member is allowed
    const cookies = await Cookie.find(
      {
        allowed_members: req.user.id,
      },
      "website_url last_updated"
    );

    res.json({ sites: cookies });
  } catch (error) {
    console.error("Get member sites error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Route to check if cookies exist for a specific domain
router.get("/cookies/check/:domain", authMiddleware, async (req, res) => {
  try {
    const domain = req.params.domain;

    // Find cookies that match this domain or its parent domains
    const cookies = await Cookie.findOne({
      website_url: domain,
      allowed_members: req.user.id,
    });

    res.json({ hasCookies: !!cookies });
  } catch (error) {
    console.error("Check cookies error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Route to get cookies for a specific website
router.get("/cookies/:website", authMiddleware, async (req, res) => {
  try {
    const website = req.params.website;

    // Find cookies for this website
    const cookie = await Cookie.findOne({
      website_url: website,
      allowed_members: req.user.id,
    });

    if (!cookie) {
      return res
        .status(404)
        .json({ message: "Cookies not found or access denied" });
    }

    // Decrypt cookies before sending
    const decryptedCookies = decryptCookies(cookie.encrypted_cookie);

    res.json({ cookies: decryptedCookies });
  } catch (error) {
    console.error("Get website cookies error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

module.exports = router;
