// Encryption utilities for cookie data
const crypto = require("crypto");
const config = require("config");

// Get encryption key from config (should be 32 bytes for AES-256)
const encryptionKey = config.get("cookieEncryptionKey");
const IV_LENGTH = 16; // For AES, this is always 16

/**
 * Encrypt data using AES-256-CBC
 * @param {string} data - The data to encrypt (JSON string)
 * @returns {string} - The encrypted data as a hex string with IV prepended
 */
function encrypt(data) {
  // Create a random initialization vector
  const iv = crypto.randomBytes(IV_LENGTH);

  // Create cipher using the encryption key and IV
  const cipher = crypto.createCipheriv(
    "aes-256-cbc",
    Buffer.from(encryptionKey),
    iv
  );

  // Encrypt the data
  let encrypted = cipher.update(data, "utf8", "hex");
  encrypted += cipher.final("hex");

  // Prepend the IV to the encrypted data (IV is needed for decryption)
  return iv.toString("hex") + ":" + encrypted;
}

/**
 * Decrypt data using AES-256-CBC
 * @param {string} encryptedData - The data to decrypt (IV:encryptedData format)
 * @returns {string} - The decrypted data as a string
 */
function decrypt(encryptedData) {
  // Split the encrypted data to get the IV and encrypted parts
  const parts = encryptedData.split(":");

  if (parts.length !== 2) {
    throw new Error("Invalid encrypted data format");
  }

  const iv = Buffer.from(parts[0], "hex");
  const encryptedText = parts[1];

  // Create decipher using the encryption key and IV
  const decipher = crypto.createDecipheriv(
    "aes-256-cbc",
    Buffer.from(encryptionKey),
    iv
  );

  // Decrypt the data
  let decrypted = decipher.update(encryptedText, "hex", "utf8");
  decrypted += decipher.final("utf8");

  return decrypted;
}

/**
 * Encrypt cookie data before storing in database
 * @param {Array} cookies - Array of cookie objects
 * @returns {string} - Encrypted cookie data
 */
function encryptCookies(cookies) {
  // Convert cookies array to JSON string
  const cookiesJSON = JSON.stringify(cookies);

  // Encrypt the JSON string
  return encrypt(cookiesJSON);
}

/**
 * Decrypt cookie data from database
 * @param {string} encryptedCookies - Encrypted cookie data
 * @returns {Array} - Array of cookie objects
 */
function decryptCookies(encryptedCookies) {
  // Decrypt the data
  const decryptedJSON = decrypt(encryptedCookies);

  // Parse the JSON string back to an array
  return JSON.parse(decryptedJSON);
}

module.exports = {
  encrypt,
  decrypt,
  encryptCookies,
  decryptCookies,
};
