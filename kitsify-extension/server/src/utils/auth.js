// Authentication utilities
const jwt = require("jsonwebtoken");
const bcrypt = require("bcrypt");
const config = require("config");
const User = require("../models/User");

// Get JWT secret from config
const jwtSecret = config.get("jwtSecret");

/**
 * Generate a JWT token for user authentication
 * @param {Object} user - User object with _id and role
 * @returns {string} - JWT token
 */
function generateToken(user) {
  const payload = {
    user: {
      id: user._id,
      role: user.role,
    },
  };

  // Token expires in 24 hours
  return jwt.sign(payload, jwtSecret, { expiresIn: "24h" });
}

/**
 * Verify password against stored hash
 * @param {string} password - Plain text password
 * @param {string} hash - Hashed password from database
 * @returns {Promise<boolean>} - True if password matches
 */
async function verifyPassword(password, hash) {
  return await bcrypt.compare(password, hash);
}

/**
 * Hash a password
 * @param {string} password - Plain text password
 * @returns {Promise<string>} - Hashed password
 */
async function hashPassword(password) {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
}

/**
 * Authentication middleware for protected routes
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function authMiddleware(req, res, next) {
  // Get token from header
  const token = req.header("Authorization");

  // Check if no token
  if (!token || !token.startsWith("Bearer ")) {
    return res.status(401).json({ message: "No token, authorization denied" });
  }

  // Verify token
  try {
    const decoded = jwt.verify(token.replace("Bearer ", ""), jwtSecret);
    req.user = decoded.user;
    next();
  } catch (err) {
    res.status(401).json({ message: "Token is not valid" });
  }
}

/**
 * Admin role check middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function adminMiddleware(req, res, next) {
  // First run the auth middleware
  authMiddleware(req, res, () => {
    // Check if user is admin
    if (req.user.role !== "admin") {
      return res
        .status(403)
        .json({ message: "Access denied. Admin role required." });
    }
    next();
  });
}

module.exports = {
  generateToken,
  verifyPassword,
  hashPassword,
  authMiddleware,
  adminMiddleware,
};
