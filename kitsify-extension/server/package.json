{"name": "cookie-storage-server", "version": "1.0.0", "description": "Server for multi-website cookie storage solution", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^6.10.0", "jsonwebtoken": "^9.0.0", "bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "crypto": "^1.0.1"}, "devDependencies": {"nodemon": "^2.0.22"}}