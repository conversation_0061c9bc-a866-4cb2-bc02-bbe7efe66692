# Multi-Website Cookie Storage Solution

A browser extension system that allows administrators to save cookies from multiple websites and share them with authorized members.

## Project Overview

This project provides a complete solution for organizations to manage website access through cookies. It consists of three main components:

1. **Admin Extension**: Browser extension for administrators to extract cookies from websites after logging in
2. **Member Extension**: Browser extension for members to use saved cookies for automatic login to websites
3. **Server**: Backend for securely storing cookies with encryption and managing access controls

## Key Features

- **Secure Cookie Management**: Cookies are encrypted before storage and decrypted when needed
- **Role-Based Access Control**: Administrators can control which members have access to which websites
- **Automatic Website Detection**: Member extension detects when a user visits a supported website
- **JWT Authentication**: Secure authentication system for both admin and member interactions
- **Cookie Validation**: System checks for cookie expiration and alerts administrators

## Project Structure

- `admin-extension/`: Browser extension for administrators to save cookies

  - `background.js`: Background script for cookie extraction and server communication
  - `popup.html/js`: User interface for admin functionality
  - `manifest.json`: Extension configuration

- `member-extension/`: Browser extension for members to access websites using saved cookies

  - `background.js`: Background script for cookie injection and server communication
  - `popup.html/js`: User interface for member functionality
  - `manifest.json`: Extension configuration

- `server/`: Backend server for cookie storage and management
  - `src/models/`: Database schemas for cookies and users
  - `src/utils/`: Utility functions for authentication and encryption
  - `src/routes.js`: API endpoints for both extensions
  - `src/server.js`: Main server file

## Getting Started

Please refer to the [Installation Guide](INSTALLATION.md) for detailed setup instructions.

## Usage

### Administrator Flow

1. Install the admin extension
2. Log in with admin credentials (default: admin/admin123)
3. Browse to a website and log in normally
4. Click "Extract Cookies" to save the cookies to the server
5. Manage which members have access to which websites

### Member Flow

1. Install the member extension
2. Log in with member credentials
3. Browse to a supported website - cookies will be automatically injected
4. Alternatively, use the extension popup to view and access available websites

## Security Considerations

- All cookie data is encrypted using AES-256-CBC before storage
- JWT tokens are used for secure authentication
- Passwords are hashed using bcrypt
- Access control ensures members can only access cookies they're authorized for
- HTTPS is required for all communication between extensions and server

## Development

To run the server in development mode:

```
cd server
npm install
npm run dev
```

## License

This project is proprietary and confidential.

## Credits

Developed by AI Cursor.
