// Content script to listen for messages from the website

// Utility function to send messages safely to background script
function sendMessageToBackground(message, callback) {
  try {
    chrome.runtime.sendMessage(message, (response) => {
      if (chrome.runtime.lastError) {
        console.error("Content script message error:", chrome.runtime.lastError);
        if (callback) {
          callback({ success: false, error: chrome.runtime.lastError.message });
        }
      } else {
        if (callback) {
          callback(response);
        }
      }
    });
  } catch (error) {
    console.error("Content script exception:", error);
    if (callback) {
      callback({ success: false, error: error.message });
    }
  }
}

// Listen for messages from the website via window.postMessage
window.addEventListener('message', (event) => {
  // Only process messages from kitsify-tool website
  if (event.source === window && event.data) {
    if (event.data.type === 'KITSIFY_CHECK_EXTENSION') {
      // Respond to extension ready check
      sendMessageToBackground({ action: "healthCheck" }, (response) => {
        window.postMessage({
          type: 'KITSIFY_EXTENSION_READY',
          connected: response && response.success && response.ready,
          timestamp: new Date().toISOString()
        }, window.location.origin);
      });

    } else if (event.data.type === 'KITSIFY_AUTH_SYNC') {
      // Forward message to background script
      sendMessageToBackground({
        action: event.data.action
      }, (response) => {
        console.log("Auth sync response:", response);
        if (response && response.success) {
          // Notify the web page about successful sync
          window.postMessage({
            type: 'KITSIFY_AUTH_SYNC_RESPONSE',
            success: true,
            alreadyLoggedIn: response.alreadyLoggedIn
          }, window.location.origin);
        } else {
          // Notify the web page about failed sync
          window.postMessage({
            type: 'KITSIFY_AUTH_SYNC_RESPONSE',
            success: false,
            error: response?.error,
            details: response?.details,
            debugInfo: response?.debugInfo
          }, window.location.origin);
        }
      });

    } else if (event.data.type === 'KITSIFY_DOMAIN_ACTION') {
      // Forward message to background script
      sendMessageToBackground({
        action: event.data.action,
        domain: event.data.domain
      }, (response) => {
        if (response && response.success) {
          console.log(`✅ Domain action ${event.data.action} completed for ${event.data.domain}`);
        } else {
          console.error(`❌ Domain action ${event.data.action} failed for ${event.data.domain}:`, response?.error);
        }
      });
    }
  }
});

// Check connection with background script
function checkBackgroundConnection() {
  sendMessageToBackground({ action: "getStatus" }, (response) => {
    if (response && response.success !== undefined) {
      console.log("Content script connected to background script");
      // Notify the website that extension is ready
      window.postMessage({
        type: 'KITSIFY_EXTENSION_READY',
        connected: true
      }, window.location.origin);
    } else {
      console.error("Content script failed to connect to background script");
      // Notify the website that extension has issues
      window.postMessage({
        type: 'KITSIFY_EXTENSION_READY',
        connected: false,
        error: "Background script not responding"
      }, window.location.origin);
    }
  });
}

// Check connection when content script is loaded
checkBackgroundConnection();
