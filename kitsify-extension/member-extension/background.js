// Member Extension Background Script
// Authentication token stored in memory (cleared on browser restart)
let authToken = null;

// Production server URL
const SERVER_URL = "http://localhost:3000";

// Extension version for logging
const EXTENSION_VERSION = "1.0.2";

// Initialize error tracking
let errorLog = [];
const MAX_ERROR_LOG_SIZE = 50;

// Background script health check
let isBackgroundReady = false;

// Initialize background script
function initializeBackground() {
  try {
    isBackgroundReady = true;
    console.log("Background script initialized successfully");

    // Check if we have stored auth token (but don't send messages during init)
    checkWebsiteLoginStatus().catch(error => {
      console.error("Error during initial login check:", error);
    });
  } catch (error) {
    logError("initialization", "Failed to initialize background script", error);
  }
}

// Call initialization
initializeBackground();

// Helper function to safely send messages to popup
function sendMessageToPopupSafely(message) {
  try {
    chrome.runtime.sendMessage(message, () => {
      if (chrome.runtime.lastError) {
        // This is normal - popup might not be open
        console.log("Popup not available for message:", chrome.runtime.lastError.message);
      }
    });
  } catch (error) {
    // Ignore errors - popup might not be open
    console.log("Failed to send message to popup:", error.message);
  }
}

/**
 * Log errors with timestamp for debugging
 * @param {string} source - Source of the error
 * @param {string} message - Error message
 * @param {Error|null} error - Error object if available
 */
function logError(source, message, error = null) {
  const timestamp = new Date().toISOString();
  const errorEntry = {
    timestamp,
    source,
    message,
    stack: error?.stack || null
  };

  // Add to beginning of array (most recent first)
  errorLog.unshift(errorEntry);

  // Limit log size
  if (errorLog.length > MAX_ERROR_LOG_SIZE) {
    errorLog.pop();
  }

  // Log to console for development
  console.error(`[${timestamp}] [${source}] ${message}`, error || '');
}

// Handle messages from popup and website
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  // Validate request
  if (!request || typeof request !== 'object') {
    sendResponse({ success: false, error: "Invalid request" });
    return true;
  }

  try {
    console.log(`Handling message: ${request.action}`);

    switch (request.action) {
      case "getStatus":
        sendResponse({ isLoggedIn: authToken !== null });
        return true;

      case "getAvailableSites":
        getAvailableSites(sendResponse);
        return true;

      case "injectCookies":
        injectCookies(request.website, sendResponse, request.serverId);
        return true;

      case "forceInject":
        // Remove domain from injected list to allow re-injection
        if (request.domain && injectedDomains.has(request.domain)) {
          injectedDomains.delete(request.domain);
        }
        sendResponse({ success: true });
        return true;

      case "syncLogin":
        syncLoginWithWebsite(sendResponse);
        return true;

      case "forceInjectForDomain":
        forceInjectForDomain(request.domain, sendResponse);
        return true;

      case "getErrorLog":
        sendResponse({ success: true, errorLog });
        return true;

      case "clearErrorLog":
        errorLog = [];
        sendResponse({ success: true });
        return true;

      case "getVersion":
        sendResponse({ success: true, version: EXTENSION_VERSION });
        return true;

      case "healthCheck":
        sendResponse({
          success: true,
          ready: isBackgroundReady,
          hasAuthToken: authToken !== null,
          timestamp: new Date().toISOString()
        });
        return true;

      default:
        logError("message-handler", `Unknown action: ${request.action}`, null);
        sendResponse({ success: false, error: "Unknown action" });
        return true;
    }
  } catch (error) {
    logError("message-handler", `Error handling message: ${request.action}`, error);
    sendResponse({ success: false, error: "Internal extension error" });
    return true;
  }
});

// Note: External message listener removed - using content script communication only

// Listen for tab update events to check login status on Kitsify websites
chrome.tabs.onUpdated.addListener((_tabId, changeInfo, tab) => {
  try {
    if (changeInfo.status === "complete" && tab.url && tab.url.includes("localhost")) {
      // Check login status when Kitsify tool page is loaded
      checkWebsiteLoginStatus();
    }
  } catch (error) {
    logError("tab-updated", "Error checking website login status", error);
  }
});

// Store domains that have already had cookies injected in current session
let injectedDomains = new Set();

// Auto-inject cookies only once per domain when page loads for the first time
// Use webNavigation.onCommitted to detect initial navigation to a page
chrome.webNavigation.onCommitted.addListener((details) => {
  // Only process main frame navigations (not iframes, etc.)
  if (details.frameId === 0 && details.url && authToken) {
    try {
      const domain = new URL(details.url).hostname;

      // Check if this is a page refresh by examining transition type
      const isRefresh = details.transitionType === 'reload' ||
                        details.transitionQualifiers.includes('from_address_bar');

      // Only inject if this domain hasn't been processed yet AND it's not a refresh
      if (!injectedDomains.has(domain) && !isRefresh) {
        // Get tab info to pass to checkAndInjectCookies
        chrome.tabs.get(details.tabId, (tab) => {
          if (chrome.runtime.lastError) {
            logError("navigation", `Error getting tab info: ${chrome.runtime.lastError.message}`);
            return;
          }

          checkAndInjectCookies(tab);
          injectedDomains.add(domain);

          // Log successful domain processing
          console.log(`Processing navigation to domain: ${domain}`);
        });
      }
    } catch (error) {
      // Log invalid URLs or other errors
      logError("navigation", `Error processing navigation to: ${details.url}`, error);
    }
  }
});

// Reset injected domains list on extension startup
chrome.runtime.onStartup.addListener(() => {
  injectedDomains = new Set();
});

// Reset injected domains on login/logout
function resetInjectedDomains() {
  injectedDomains = new Set();
}

// Discord login function removed - using website login only

// Logout function removed - not needed

async function syncLoginWithWebsite(sendResponse) {
  try {
    console.log("Syncing login with website, current authToken:", authToken ? "exists" : "none");

    if (authToken) {
      console.log("Already logged in, resetting injected domains for fresh session");
      resetInjectedDomains();
      sendResponse({ success: true, alreadyLoggedIn: true });
      return;
    }

    const loginStatus = await checkWebsiteLoginStatus();

    if (loginStatus.success) {
      console.log("Login sync successful, new token acquired");
      sendResponse({ success: true });
    } else {
      console.log("Login sync failed:", loginStatus.error);
      // Provide more detailed error information
      sendResponse({
        success: false,
        error: "Failed to sync login",
        details: loginStatus.error,
        debugInfo: {
          hasAuthToken: authToken !== null,
          timestamp: new Date().toISOString()
        }
      });
    }
  } catch (error) {
    console.error("Error syncing login:", error);
    logError("syncLogin", "Error in syncLoginWithWebsite", error);
    sendResponse({
      success: false,
      error: error.message,
      debugInfo: {
        hasAuthToken: authToken !== null,
        timestamp: new Date().toISOString()
      }
    });
  }
}

// Force inject cookies for a specific domain
async function forceInjectForDomain(domain, sendResponse) {
  try {
    if (!authToken) {
      const loginStatus = await checkWebsiteLoginStatus();
      if (!loginStatus.success) {
        sendResponse({ success: false, error: "Not authenticated" });
        return;
      }
    }

    if (!domain) {
      sendResponse({ success: false, error: "Domain is required" });
      return;
    }

    // Remove domain from injected list to allow re-injection
    if (injectedDomains.has(domain)) {
      injectedDomains.delete(domain);
    }

    // Check if server has cookies for this domain
    const response = await fetch(`${SERVER_URL}/cookies/check/${domain}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    const text = await response.text();
    let data = {};

    try {
      data = JSON.parse(text);
    } catch (jsonError) {
      sendResponse({ success: false, error: "Invalid response format" });
      return;
    }

    const hasCookies = response.ok && (data.hasCookies === true);

    if (hasCookies) {
      // Inject cookies for this domain
      injectCookies(domain, (result) => {
        if (result.success) {
          // Mark domain as injected
          injectedDomains.add(domain);
          sendResponse({ success: true, message: `Cookies injected for ${domain}` });
        } else {
          sendResponse({ success: false, error: result.error || "Failed to inject cookies" });
        }
      }, 1); // Default to server ID 1
    } else {
      sendResponse({ success: false, error: "No cookies available for this domain" });
    }
  } catch (error) {
    console.error("Error in forceInjectForDomain:", error);
    sendResponse({ success: false, error: error.message });
  }
}

async function checkWebsiteLoginStatus() {
  try {
    // Try to get cookies from kitsify.com first
    let cookies = await chrome.cookies.getAll({ domain: 'localhost' });
    let accessTokenCookie = cookies.find(cookie => cookie.name === 'access_token');

    // If not found, try without the subdomain (fallback for different cookie settings)
    if (!accessTokenCookie) {
      const fallbackCookies = await chrome.cookies.getAll({ domain: '.localhost' });
      accessTokenCookie = fallbackCookies.find(cookie => cookie.name === 'access_token');
    }

    // If still not found, try kitsify.com
    if (!accessTokenCookie) {
      const mainDomainCookies = await chrome.cookies.getAll({ domain: 'localhost' });
      accessTokenCookie = mainDomainCookies.find(cookie => cookie.name === 'access_token');
    }

    if (accessTokenCookie) {
      authToken = accessTokenCookie.value;

      // Reset injected domains for new session
      resetInjectedDomains();

      sendMessageToPopupSafely({ type: 'AUTH_SUCCESS', token: authToken });

      console.log("Auto login successful from website");
      return { success: true, token: authToken };
    }

    return { success: false, error: "No auth token found in any domain" };
  } catch (error) {
    console.error("Error checking website login status:", error);
    return { success: false, error: error.message };
  }
}

// Fetch all websites the member has access to
async function getAvailableSites(sendResponse) {
  if (!authToken) {
    sendResponse({ success: false, error: "Not authenticated" });
    return;
  }

  try {
    const response = await fetch(`${SERVER_URL}/member/sites`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    const data = await response.json();

    if (response.ok) {
      sendResponse({
        success: true,
        sites: data.sites || [],
      });
    } else {
      sendResponse({
        success: false,
        error: data.message || "Failed to get available sites",
      });
    }
  } catch (error) {
    sendResponse({ success: false, error: "Network error" });
  }
}

// Inject cookies for a specific website
async function injectCookies(website, sendResponse, serverId = 1) {
  if (!authToken) {
    sendResponse({ success: false, error: "Not authenticated" });
    return;
  }

  try {
    // Fetch cookies from server with server ID
    const response = await fetch(`${SERVER_URL}/cookies/${website}?server_id=${serverId}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    // Get response as text first to handle potential JSON parsing errors
    const text = await response.text();
    let data = {};

    try {
      data = JSON.parse(text);
    } catch (jsonError) {
      sendResponse({ success: false, error: "Invalid response format" });
      return;
    }

    // Parse cookieData string to get cookies array
    let cookies = [];
    if (response.ok && data.cookieData) {
      try {
        cookies = JSON.parse(data.cookieData);
      } catch (parseError) {
        sendResponse({ success: false, error: "Invalid cookie data format" });
        return;
      }
    }

    if (response.ok && cookies.length > 0) {
      let injectedCount = 0;

      // Set each cookie in the browser
      for (const cookie of cookies) {
        try {
          // Handle special cookie requirements
          const needsSecure = cookie.name.startsWith('__Host-') || cookie.name.startsWith('__Secure-');

          // Handle domains with leading dot (.example.com)
          let cookieDomain = cookie.domain;
          let urlDomain = cookie.domain;

          if (urlDomain.startsWith('.')) {
            urlDomain = urlDomain.substring(1);
          }

          // Build URL based on domain and path
          const url = (needsSecure || cookie.secure) ?
            `https://${urlDomain}${cookie.path || '/'}` :
            `http://${urlDomain}${cookie.path || '/'}`;

          // Ensure cookie has valid path
          const path = cookie.path || '/';

          // Ensure valid sameSite value
          let sameSite = cookie.sameSite;
          if (!['strict', 'lax', 'none'].includes(sameSite?.toLowerCase())) {
            sameSite = 'lax';
          }

          // Ensure secure=true if sameSite=none
          const secure = needsSecure || cookie.secure || sameSite?.toLowerCase() === 'none';

          // Special handling for __Host- cookies
          if (cookie.name.startsWith('__Host-')) {
            cookieDomain = undefined; // __Host- cookies must not have domain set
          }

          const cookieParams = {
            url: url,
            name: cookie.name,
            value: cookie.value,
            path: path,
            secure: secure,
            httpOnly: cookie.httpOnly || false,
            sameSite: sameSite,
            expirationDate: cookie.expirationDate
          };

          if (cookieDomain !== undefined) {
            cookieParams.domain = cookieDomain;
          }

          await chrome.cookies.set(cookieParams);
          injectedCount++;
        } catch (err) {
          // Continue with next cookie if one fails
          console.error(`Failed to inject cookie: ${err}`);
        }
      }

      // Reload any open tabs with this domain
      const tabs = await chrome.tabs.query({});
      for (const tab of tabs) {
        if (tab.url && tab.url.includes(website)) {
          chrome.tabs.reload(tab.id);
        }
      }

      sendResponse({
        success: true,
        message: `Successfully injected ${injectedCount} cookies for ${website}`,
      });
    } else {
      sendResponse({
        success: false,
        error: data.message || "Failed to get cookies",
      });
    }
  } catch (error) {
    sendResponse({ success: false, error: "Network error" });
  }
}

// Check and inject cookies when visiting a site for the first time
async function checkAndInjectCookies(tab) {
  if (!authToken) return;

  try {
    const domain = new URL(tab.url).hostname;

    // Check if server has cookies for this domain
    const response = await fetch(`${SERVER_URL}/cookies/check/${domain}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    // Parse response
    const text = await response.text();
    let data = {};

    try {
      data = JSON.parse(text);
    } catch (jsonError) {
      console.error("Invalid JSON response:", jsonError);
      return; // Invalid JSON response
    }

    // Check if cookies are available
    const hasCookies = response.ok && (data.hasCookies === true);

    if (hasCookies) {
      // Default to server ID 1 for automatic injection
      const defaultServerId = 1;

      // Inject cookies automatically
      injectCookies(domain, (result) => {
        if (result.success) {
          // Notify popup about successful injection (if popup is open)
          sendMessageToPopupSafely({
            type: 'COOKIES_INJECTED',
            domain: domain,
            message: `Cookies injected for ${domain}`
          });

          // Reload the specific tab where navigation occurred
          chrome.tabs.reload(tab.id);
        }
      }, defaultServerId);
    }
  } catch (error) {
    // Log errors for debugging
    console.error("Error in checkAndInjectCookies:", error);
  }
}
