<!DOCTYPE html>
<html>
<head>
  <title>Kitsify</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #7C4DFF;
      --primary-dark: #5E35B1;
      --primary-light: #B39DDB;

      --success-color: #4CAF50;
      --error-color: #F44336;
      --text-color: #333;
      --text-light: #757575;
      --bg-color: #FFFFFF;
      --bg-light: #F5F7FA;
      --border-radius: 8px;
      --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      --transition: all 0.3s ease;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      width: 360px;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: var(--bg-color);
      color: var(--text-color);
    }

    .header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid var(--primary-light);
    }

    .logo {
      font-size: 24px;
      margin-right: 10px;
      color: var(--primary-color);
    }

    h2 {
      color: var(--primary-color);
      font-weight: 600;
      margin: 0;
    }

    h3 {
      color: var(--primary-dark);
      font-size: 16px;
      margin: 15px 0 10px;
    }

    button {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 12px;
      padding: 10px 15px;
      width: 100%;
      cursor: pointer;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-weight: 500;
      transition: var(--transition);
      box-shadow: var(--shadow);
    }

    button:hover {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
    }

    button i {
      margin-right: 8px;
    }

    .button-group {
      display: grid;
      grid-template-columns: 1fr;
      gap: 10px;
    }

    .hidden {
      display: none;
    }

    .status {
      margin-top: 12px;
      padding: 12px;
      border-radius: var(--border-radius);
      font-size: 14px;
      animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .success {
      background-color: rgba(76, 175, 80, 0.1);
      color: var(--success-color);
      border-left: 4px solid var(--success-color);
    }

    .error {
      background-color: rgba(244, 67, 54, 0.1);
      color: var(--error-color);
      border-left: 4px solid var(--error-color);
    }

    input, select {
      width: 100%;
      padding: 10px 12px;
      margin: 8px 0;
      border: 1px solid #ddd;
      border-radius: var(--border-radius);
      font-size: 14px;
      transition: var(--transition);
    }

    input:focus, select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(124, 77, 255, 0.2);
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: var(--text-light);
      font-size: 14px;
    }

    .site-item {
      padding: 12px;
      margin: 8px 0;
      background-color: var(--bg-light);
      border-radius: var(--border-radius);
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .site-item:hover {
      background-color: rgba(124, 77, 255, 0.1);
      transform: translateX(3px);
    }

    .site-item i {
      margin-right: 8px;
      color: var(--primary-color);
    }

    .current-site {
      margin: 15px 0;
      padding: 12px;
      background-color: rgba(124, 77, 255, 0.1);
      border-radius: var(--border-radius);
      color: var(--primary-color);
      font-weight: 500;
      display: flex;
      align-items: center;
    }

    .current-site i {
      margin-right: 8px;
    }

    .server-select-container {
      margin-bottom: 15px;
      background-color: var(--bg-light);
      padding: 12px;
      border-radius: var(--border-radius);
    }

    #sitesList {
      margin-top: 20px;
      max-height: 200px;
      overflow-y: auto;
      padding-right: 5px;
    }

    #sitesList::-webkit-scrollbar {
      width: 6px;
    }

    #sitesList::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }

    #sitesList::-webkit-scrollbar-thumb {
      background: var(--primary-light);
      border-radius: 10px;
    }

    #sitesList::-webkit-scrollbar-thumb:hover {
      background: var(--primary-color);
    }

    .no-sites {
      padding: 12px;
      text-align: center;
      color: var(--text-light);
      font-style: italic;
      background-color: var(--bg-light);
      border-radius: var(--border-radius);
    }

    .connection-status {
      margin-left: auto;
      display: flex;
      align-items: center;
    }

    #connectionIndicator {
      font-size: 8px;
      color: #ccc;
      transition: var(--transition);
    }

    #connectionIndicator.connected {
      color: var(--success-color);
    }

    #connectionIndicator.disconnected {
      color: var(--error-color);
    }
  </style>
</head>
<body>
  <div class="header">
    <i class="fas fa-cookie-bite logo"></i>
    <h2>Kitsify</h2>
    <div id="connectionStatus" class="connection-status">
      <i class="fas fa-circle" id="connectionIndicator"></i>
    </div>
  </div>

  <!-- Login Form -->
  <div id="loginForm">
    <h3 style="text-align: center; margin-bottom: 20px; color: var(--text-color);">
      <i class="fas fa-sign-in-alt"></i> Please login to your Kitsify
    </h3>
    <button id="loginBtn">
      <i class="fas fa-sign-in-alt"></i> Login
    </button>
    <div id="loginStatus" class="status hidden"></div>
  </div>

  <!-- Site Management -->
  <div id="siteManager" class="hidden">
    <div class="current-site">
      <i class="fas fa-globe"></i>
      <div>
        <div style="font-size: 12px; color: var(--text-light);">Current website</div>
        <div id="currentSite">Unknown</div>
      </div>
    </div>

    <div class="server-select-container">
      <label for="serverSelect">Select Server</label>
      <select id="serverSelect">
        <option value="1">Server 1</option>
        <option value="2">Server 2</option>
        <option value="3">Server 3</option>
      </select>
    </div>

    <div class="button-group">
      <button id="autoInjectBtn">
        <i class="fas fa-magic"></i> Login Current Site
      </button>
      <button id="refreshSitesBtn">
        <i class="fas fa-sync-alt"></i> Refresh Websites
      </button>
    </div>

    <div id="actionStatus" class="status hidden"></div>

    <div id="sitesList">
      <h3><i class="fas fa-list"></i> Available Websites</h3>
      <div id="sitesListItems"></div>
    </div>

    <div id="versionInfo" style="margin-top: 10px; text-align: center; font-size: 11px; color: var(--text-light);"></div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
