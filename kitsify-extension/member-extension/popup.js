// DOM elements
const loginForm = document.getElementById("loginForm");
const siteManager = document.getElementById("siteManager");
const loginBtn = document.getElementById("loginBtn");
const autoInjectBtn = document.getElementById("autoInjectBtn");
const refreshSitesBtn = document.getElementById("refreshSitesBtn");
const loginStatus = document.getElementById("loginStatus");
const actionStatus = document.getElementById("actionStatus");
const currentSite = document.getElementById("currentSite");
const sitesListItems = document.getElementById("sitesListItems");
const serverSelect = document.getElementById("serverSelect");
const versionInfo = document.getElementById("versionInfo");
const errorLogModal = document.getElementById("errorLogModal");
const errorLogContent = document.getElementById("errorLogContent");
const connectionIndicator = document.getElementById("connectionIndicator");

// Update connection status indicator
function updateConnectionStatus(connected) {
  if (connected) {
    connectionIndicator.className = "fas fa-circle connected";
    connectionIndicator.title = "Connected to background script";
  } else {
    connectionIndicator.className = "fas fa-circle disconnected";
    connectionIndicator.title = "Disconnected from background script";
  }
}

// Utility function to send messages with error handling
function sendMessageSafely(message, callback, retries = 3) {
  // Check if extension context is valid
  if (!chrome.runtime || !chrome.runtime.id) {
    console.error("Extension context invalidated");
    updateConnectionStatus(false);
    if (callback) {
      callback({ success: false, error: "Extension context invalidated" });
    }
    return;
  }

  const attemptSend = (attempt) => {
    try {
      chrome.runtime.sendMessage(message, (response) => {
        // Check for context invalidation
        if (!chrome.runtime || !chrome.runtime.id) {
          console.error("Extension context invalidated during message send");
          updateConnectionStatus(false);
          if (callback) {
            callback({ success: false, error: "Extension context invalidated" });
          }
          return;
        }

        if (chrome.runtime.lastError) {
          const errorMsg = chrome.runtime.lastError.message;
          console.error(`Message send error (attempt ${attempt}):`, errorMsg);
          updateConnectionStatus(false);

          // Don't retry on certain fatal errors
          if (errorMsg && (errorMsg.includes("Extension context invalidated") ||
              errorMsg.includes("receiving end does not exist"))) {
            console.error("Fatal error, not retrying:", errorMsg);
            if (callback) {
              callback({ success: false, error: errorMsg });
            }
            return;
          }

          if (attempt < retries) {
            // Retry after a short delay
            setTimeout(() => attemptSend(attempt + 1), 100 * attempt);
          } else {
            // All retries failed
            console.error("All message send attempts failed:", errorMsg);
            updateConnectionStatus(false);
            if (callback) {
              callback({ success: false, error: "Extension connection failed" });
            }
          }
        } else {
          // Success
          updateConnectionStatus(true);
          if (callback) {
            callback(response);
          }
        }
      });
    } catch (error) {
      console.error(`Exception sending message (attempt ${attempt}):`, error);
      updateConnectionStatus(false);
      if (attempt < retries) {
        setTimeout(() => attemptSend(attempt + 1), 100 * attempt);
      } else {
        updateConnectionStatus(false);
        if (callback) {
          callback({ success: false, error: "Extension connection failed" });
        }
      }
    }
  };

  attemptSend(1);
}

// Check login status when popup opens
document.addEventListener("DOMContentLoaded", async () => {
  // Small delay to ensure background script is ready
  setTimeout(() => {
    // First do a health check
    sendMessageSafely({ action: "healthCheck" }, (healthResponse) => {
      if (healthResponse && healthResponse.success) {
        console.log("Background script health check:", healthResponse);

        // Get extension version
        sendMessageSafely({ action: "getVersion" }, (response) => {
          if (response && response.success) {
            versionInfo.textContent = `Version ${response.version}`;
          } else {
            versionInfo.textContent = "Version unknown";
          }
        });

        // Check login status by checking if we have authToken
        checkLoginStatus();
      } else {
        console.error("Background script health check failed");
        versionInfo.textContent = "Extension error";
        showLoginForm();
      }
    });
  }, 100);
});

// Function to check login status
function checkLoginStatus() {
  sendMessageSafely({ action: "getStatus" }, (response) => {
    if (response && response.isLoggedIn) {
      showSiteManager();
      updateCurrentSite();
      loadAvailableSites();
    } else {
      // Try to sync login from website cookies
      sendMessageSafely({ action: "syncLogin" }, (syncResponse) => {
        if (syncResponse && syncResponse.success) {
          // Login synced successfully
          showSiteManager();
          updateCurrentSite();
          loadAvailableSites();
        } else {
          // No login found, show login form
          showLoginForm();
        }
      });
    }
  });
}

// Check login status when popup becomes visible (user might have logged in on website)
document.addEventListener("visibilitychange", () => {
  if (!document.hidden) {
    checkLoginStatus();
  }
});

// Handle messages from background script
chrome.runtime.onMessage.addListener((message) => {
  if (message.type === 'AUTH_SUCCESS') {
    showSiteManager();
    updateCurrentSite();
    loadAvailableSites();
    clearStatus(loginStatus);
  } else if (message.type === 'COOKIES_INJECTED') {
    // Show notification when cookies are injected successfully
    showStatus(actionStatus, message.message || `Login successful!`, "success");
  }
});

// Handle login button click - redirect to login page
loginBtn.addEventListener("click", () => {
  try {
    // Open login page in new tab
    chrome.tabs.create({ url: "https://kitsify.com/sign-in" }, () => {
      if (chrome.runtime.lastError) {
        console.error("Failed to create tab:", chrome.runtime.lastError.message);
        showStatus(loginStatus, "Failed to open login page", "error");
        return;
      }

      // Show status message
      showStatus(loginStatus, "Redirecting to login page...", "success");

      // Close popup after redirect
      setTimeout(() => {
        window.close();
      }, 1000);
    });
  } catch (error) {
    console.error("Error opening login page:", error);
    showStatus(loginStatus, "Failed to open login page", "error");
  }
});

// Logout logic removed - not needed

// Manually inject cookies for current site
autoInjectBtn.addEventListener("click", () => {
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    if (chrome.runtime.lastError) {
      console.error("Failed to query tabs:", chrome.runtime.lastError.message);
      showStatus(actionStatus, "Failed to get current tab", "error");
      return;
    }

    if (tabs.length === 0) {
      showStatus(actionStatus, "No active tab found", "error");
      return;
    }

    try {
      const domain = new URL(tabs[0].url).hostname;
      const serverId = parseInt(serverSelect.value);

      // Allow re-injection by removing domain from injected list
      sendMessageSafely({ action: "forceInject", domain: domain }, () => {});

      // Request cookie injection
      sendMessageSafely(
        { action: "injectCookies", website: domain, serverId: serverId },
        (response) => {
          if (response && response.success) {
            showStatus(actionStatus, 'Login successful!', "success");
            chrome.tabs.reload(tabs[0].id);
          } else {
            showStatus(
              actionStatus,
              response?.error || "Failed! Try again.",
              "error"
            );
          }
        }
      );
    } catch (error) {
      console.error("Error processing current tab:", error);
      showStatus(actionStatus, "Failed to process current tab", "error");
    }
  });
});

// Inject cookies for a site selected from the list
function injectCookiesForSite(website) {
  // Allow re-injection by removing domain from injected list
  sendMessageSafely({ action: "forceInject", domain: website }, () => {});

  // Get the selected server ID
  const serverId = parseInt(serverSelect.value);

  sendMessageSafely(
    { action: "injectCookies", website, serverId },
    (response) => {
      if (response && response.success) {
        showStatus(actionStatus, response.message, "success");

        // Find and reload any tabs with this website or open a new one
        chrome.tabs.query({}, (tabs) => {
          if (chrome.runtime.lastError) {
            console.error("Failed to query tabs:", chrome.runtime.lastError.message);
            // Still try to open new tab
            chrome.tabs.create({ url: `https://${website}` });
            return;
          }

          let tabFound = false;

          for (const tab of tabs) {
            if (tab.url && tab.url.includes(website)) {
              chrome.tabs.reload(tab.id);
              chrome.tabs.update(tab.id, { active: true });
              tabFound = true;
              break;
            }
          }

          // Open new tab if site not already open
          if (!tabFound) {
            chrome.tabs.create({ url: `https://${website}` });
          }
        });
      } else {
        showStatus(
          actionStatus,
          response?.error || "Failed! Try again.",
          "error"
        );
      }
    }
  );
}

// UI Helper functions
function showLoginForm() {
  loginForm.classList.remove("hidden");
  siteManager.classList.add("hidden");
}

function showSiteManager() {
  loginForm.classList.add("hidden");
  siteManager.classList.remove("hidden");
}

function showStatus(element, message, type) {
  element.textContent = message;
  element.className = `status ${type}`;
  element.classList.remove("hidden");

  // Auto-clear success messages after 5 seconds
  if (type === "success") {
    setTimeout(() => {
      element.classList.add("hidden");
    }, 5000);
  }
}

function clearStatus(element) {
  element.textContent = "";
  element.className = "status hidden";
}

function updateCurrentSite() {
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    if (chrome.runtime.lastError) {
      console.error("Failed to query tabs for current site:", chrome.runtime.lastError.message);
      currentSite.textContent = "Unknown";
      return;
    }

    if (tabs.length > 0) {
      try {
        const url = new URL(tabs[0].url);
        currentSite.textContent = url.hostname;
      } catch (error) {
        console.error("Error parsing current tab URL:", error);
        currentSite.textContent = "Invalid URL";
      }
    } else {
      currentSite.textContent = "Unknown";
    }
  });
}

// Load available websites from the API
function loadAvailableSites() {
  sendMessageSafely({ action: "getAvailableSites" }, (response) => {
    if (response && response.success && response.sites) {
      sitesListItems.innerHTML = "";

      if (response.sites.length === 0) {
        sitesListItems.innerHTML = '<div class="no-sites">No websites available</div>';
        return;
      }

      response.sites.forEach(site => {
        const siteItem = document.createElement("div");
        siteItem.className = "site-item";
        siteItem.innerHTML = `<i class="fas fa-globe"></i> ${site}`;
        siteItem.addEventListener("click", () => injectCookiesForSite(site));
        sitesListItems.appendChild(siteItem);
      });
    } else {
      sitesListItems.innerHTML = '<div class="no-sites">Failed to load websites</div>';
    }
  });
}

// Refresh available websites
refreshSitesBtn.addEventListener("click", () => {
  showStatus(actionStatus, "Refreshing websites...", "success");
  loadAvailableSites();
});
