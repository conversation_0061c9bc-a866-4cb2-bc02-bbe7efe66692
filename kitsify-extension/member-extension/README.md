# Kitsify - Auto Login Tools

## Overview

Kitsify Auto Login Tools is a Chrome extension that allows authorized members to automatically log in to websites using securely stored cookies. This extension is part of the Kitsify ecosystem, which provides a complete solution for organizations to manage website access.

## Features

- **Secure Auto Login**: Automatically log in to authorized websites with a single click
- **Website Authentication**: Secure login through the Kitsify platform
- **Website Management**: View and access all websites you have permission to use
- **Multiple Server Support**: Choose between different server configurations for optimal performance
- **Automatic Cookie Injection**: Cookies are automatically injected when visiting supported websites

## Installation

### From Chrome Web Store

1. Visit the [Chrome Web Store](https://chrome.google.com/webstore) and search for "Kitsify Auto Login Tools"
2. Click "Add to Chrome" to install the extension
3. Once installed, click the extension icon to begin

### Manual Installation (Development)

1. Download or clone this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top-right corner
4. Click "Load unpacked" and select the `member-extension` directory
5. The extension should now be installed and visible in your browser toolbar

## Usage

1. Click the Kitsify icon in your browser toolbar
2. Log in through the Kitsify platform (you'll be redirected to the login page)
3. Browse to a supported website - cookies will be automatically injected
4. Alternatively, use the extension popup to view and access available websites

## Privacy

Kitsify takes your privacy seriously. The extension only accesses cookies for websites you are authorized to use, and all data is encrypted during transmission. For more information, please see our [Privacy Policy](privacy-policy.html).

## Support

If you encounter any issues or have questions, please visit our [support page](https://kitsify.com/support) or contact us through our website.

## Requirements

- Chrome browser version 88 or higher
- Active Kitsify account with appropriate permissions

## Development

### Building for Production

1. Ensure all PNG icons are properly generated
2. Update the version number in `manifest.json` when making changes
3. Test the extension thoroughly before submission
4. Package the extension for Chrome Web Store submission

## License

© 2025 Kitsify. All rights reserved.
