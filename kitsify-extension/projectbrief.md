# Project Brief: Multi-Website Cookie Storage Solution

## Project Overview

This project implements a browser extension system that allows administrators to save cookies from multiple websites and share them with authorized members.

## Components

1. Admin Extension: Browser extension for administrators to log in to websites and save cookies.
2. Member Extension: Browser extension for members to access websites using saved cookies.
3. Server: Backend for cookie storage, authentication, and access control.

## Key Features

- Secure cookie extraction and storage
- Role-based access control
- Periodic cookie validation and refresh
- Encrypted storage of sensitive data
- User authentication for both admins and members
