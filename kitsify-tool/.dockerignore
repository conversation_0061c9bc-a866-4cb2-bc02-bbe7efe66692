# Dependencies
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Testing
coverage
test-results
playwright-report
playwright/.cache

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
# Keep .env.product for production builds
!.env.product

# Misc
.git
.github
.vscode
README.md
.gitignore
.eslintrc.js
.prettierrc
.storybook
storybook-static
*storybook.log

# Docker
Dockerfile
docker-compose.yml
.dockerignore
