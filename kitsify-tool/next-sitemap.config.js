/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.SITE_URL || 'https://kitsify.com',
  generateRobotsTxt: true,
  changefreq: 'daily',
  priority: 0.7,
  exclude: ['/server-sitemap.xml'],
  alternateRefs: [
    {
      href: 'https://kitsify.com/en',
      hreflang: 'en',
    },
    {
      href: 'https://kitsify.com/vi',
      hreflang: 'vi',
    },
    {
      href: 'https://kitsify.com/fr',
      hreflang: 'fr',
    },
    {
      href: 'https://kitsify.com/th',
      hreflang: 'th',
    },
  ],
  transform: async (config, path) => {
    return {
      loc: path,
      changefreq: config.changefreq,
      priority: config.priority,
      lastmod: config.autoLastmod ? new Date().toISOString() : undefined,
      alternateRefs: config.alternateRefs ?? [],
    };
  },
};
