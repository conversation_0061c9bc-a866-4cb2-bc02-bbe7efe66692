# Build stage
FROM node:20-alpine AS builder

WORKDIR /app

# Install dependencies for better layer caching
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

# Copy the rest of the application code
COPY . .

# Set environment variables for build
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# Set public environment variables for build time
# These will be embedded in the client-side code
ARG NEXT_PUBLIC_API_URL=https://admin.kitsify.com
ARG NEXT_PUBLIC_APP_URL=https://kitsify.com
ARG NEXT_PUBLIC_PAYPAL_CLIENT_ID
ARG NEXT_PUBLIC_EXTENSION_ID

ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL}
ENV NEXT_PUBLIC_PAYPAL_CLIENT_ID=${NEXT_PUBLIC_PAYPAL_CLIENT_ID}
ENV NEXT_PUBLIC_EXTENSION_ID=${NEXT_PUBLIC_EXTENSION_ID}

# Update next.config.ts to enable standalone output
RUN sed -i 's/withNextIntl({/withNextIntl({\n    output: "standalone",/' next.config.ts

# Build the application without ESLint
ENV NEXT_DISABLE_ESLINT=1
ENV NODE_OPTIONS="--max-old-space-size=4096"
RUN yarn build

# Production stage
FROM node:20-alpine AS runner

WORKDIR /app

# Install wget for healthcheck
RUN apk add --no-cache wget

# Set environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3001

# Runtime environment variables (will be available to the server)
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-https://admin.kitsify.com}
ENV NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL:-https://kitsify.com}
ENV NEXT_PUBLIC_PAYPAL_CLIENT_ID=${NEXT_PUBLIC_PAYPAL_CLIENT_ID:-}
ENV NEXT_PUBLIC_EXTENSION_ID=${NEXT_PUBLIC_EXTENSION_ID:-}

# Create a non-root user to run the application
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy necessary files from the build stage
COPY --from=builder /app/next.config.ts ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# Copy locales if they exist
COPY --from=builder /app/src/locales ./src/locales

# Set the correct permissions
RUN chown -R nextjs:nodejs /app

# Switch to the non-root user
USER nextjs

# Expose the port the app will run on
EXPOSE 3001

# Set the command to run the application
CMD ["node", "server.js"]
