'use client';

import { useLocale } from 'next-intl';
import { useEffect, useState } from 'react';
import { FaFacebookMessenger } from 'react-icons/fa';

const MessengerButton = () => {
  const locale = useLocale();
  const [isClient, setIsClient] = useState(false);

  // Ensure component only renders on client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleClick = () => {
    // Determine the correct Facebook Messenger URL based on locale
    const messengerUrl = locale === 'vi'
      ? 'https://www.facebook.com/messages/t/108592857933314'
      : 'https://www.facebook.com/messages/t/110106370397786';

    // Open in new tab
    window.open(messengerUrl, '_blank', 'noopener,noreferrer');
  };

  // Don't render on server side to avoid hydration issues
  if (!isClient) {
    return null;
  }

  return (
    <button
      type="button"
      onClick={handleClick}
      className="fixed bottom-4 right-4 z-50 flex size-12 animate-bounce items-center justify-center rounded-full bg-blue-600 text-white shadow-lg transition-all duration-300 hover:bg-blue-700 hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-blue-300 md:bottom-6 md:right-6 md:size-14"
      aria-label="Contact us on Facebook Messenger"
      title="Contact us on Facebook Messenger"
    >
      <FaFacebookMessenger className="size-5 md:size-6" />
    </button>

  );
};

export default MessengerButton;
