'use client';

import { useEffect, useState } from 'react';

const StickyButton = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > window.innerHeight) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div
      className={`fixed bottom-0 left-0 w-full border-t-0 bg-gray-950 py-3 ${
        isVisible ? 'opacity-100' : 'invisible opacity-0'
      } transition-opacity duration-300`}
    >
      <div className="container mx-auto flex items-center justify-between px-4 md:px-20">
        <div className="flex flex-col">
          <p className="text-lg text-white">Kitsify</p>
          <p className="text-gray-400">$25.00 / month</p>
        </div>
        <div className="flex">
          <button type="button" className="me-2 h-12 w-32 rounded-lg bg-green-600 px-5 font-medium text-white hover:bg-green-800 focus:outline-none focus:ring-4 focus:ring-green-300">Payment</button>
          <button type="button" className="h-12 w-32 rounded-lg bg-indigo-600 px-5 font-medium text-white hover:bg-indigo-800 focus:outline-none focus:ring-4 focus:ring-purple-300">Join</button>
        </div>
      </div>
    </div>
  );
};

export default StickyButton;
