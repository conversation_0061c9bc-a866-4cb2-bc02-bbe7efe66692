import { useTranslations } from 'next-intl';
import React from 'react';

const Counter = React.lazy(() => import('../Counter'));

const BenefitsSection: React.FC = () => {
  const t = useTranslations('Index');

  return (
    <div className="grid grid-cols-1 gap-6 px-5 md:grid-cols-4">
      <div className="flex flex-col">
        <span className="mb-4 text-center text-5xl font-semibold">
          <Counter start={50} targetNumber={99} />
          %
        </span>
        <span className="text-center text-xl font-semibold uppercase">
          {t('BENEFITS_SECTION.SAVE')}
        </span>
        <p className="font-medium text-gray-600">
          {t('BENEFITS_SECTION.SAVE_DESCRIPTION')}
        </p>
      </div>
      <div className="flex flex-col">
        <span className="mb-4 text-center text-5xl font-semibold">
          <Counter start={50} targetNumber={100} />
          %
        </span>
        <span className="text-center text-xl font-semibold uppercase">
          {t('BENEFITS_SECTION.EASY_TO_USE')}
        </span>
        <p className="font-medium text-gray-600">
          {t('BENEFITS_SECTION.EASY_TO_USE_DESCRIPTION')}
        </p>
      </div>
      <div className="flex flex-col">
        <div className="mb-4 text-center  text-5xl font-semibold">
          <Counter start={0} targetNumber={24} />
          /
          <Counter start={0} targetNumber={7} />
        </div>
        <span className="text-center text-xl font-semibold uppercase">
          {t('BENEFITS_SECTION.SUPPORT')}
        </span>
        <p className="font-medium text-gray-600">
          {t('BENEFITS_SECTION.SUPPORT_DESCRIPTION')}
        </p>
      </div>
      <div className="flex flex-col">
        <span className="mb-4  text-center text-5xl font-semibold">
          <Counter start={950} targetNumber={999} />
          %
        </span>
        <span className="text-center text-xl font-semibold uppercase">
          {t('BENEFITS_SECTION.ACCELERATE_SUCCESS')}
        </span>
        <p className="font-medium text-gray-600">
          {t('BENEFITS_SECTION.ACCELERATE_SUCCESS_DESCRIPTION')}
        </p>
      </div>
    </div>
  );
};

export default BenefitsSection;
