'use client';

import { useState } from 'react';
import GiftButton from '@/components/GiftButton';
import GiftModal from '@/components/GiftModal';

const GiftButtonWithModal = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <GiftButton onClick={handleOpenModal} />
      <GiftModal isOpen={isModalOpen} onClose={handleCloseModal} />
    </>
  );
};

export default GiftButtonWithModal;
