'use client';

import { useTranslations } from 'next-intl';
import React, { useEffect, useRef, useState } from 'react';
import { FaGift, FaTimes } from 'react-icons/fa';
import { api } from '@/services/api';

type GiftModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

const GiftModal: React.FC<GiftModalProps> = ({ isOpen, onClose }) => {
  const t = useTranslations('GiftModal');
  const modalRef = useRef<HTMLDivElement>(null);
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');

  // Close modal when clicking outside
  useEffect(() => {
    if (!isOpen) {
      return;
    }

    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, onClose]);

  // Close modal on Escape key
  useEffect(() => {
    if (!isOpen) {
      return;
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setEmail('');
      setMessage('');
      setMessageType('');
      setIsSubmitting(false);
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      setMessage('Please enter a valid email address');
      setMessageType('error');
      return;
    }

    setIsSubmitting(true);
    setMessage('');
    setMessageType('');

    try {
      const response = await api.post('/gifts/send-email', { email: email.trim() });

      if (response.data.success) {
        setMessage(t('success_message'));
        setMessageType('success');
        setEmail('');

        // Close modal after 3 seconds
        setTimeout(() => {
          onClose();
        }, 3000);
      } else {
        setMessage(response.data.message || t('error_message'));
        setMessageType('error');
      }
    } catch (error) {
      console.error('Gift email error:', error);
      setMessage(t('error_message'));
      setMessageType('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 p-4">
      <div
        ref={modalRef}
        className="w-full max-w-md overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 ease-in-out"
        style={{ animation: 'fadeIn 0.3s ease-in-out' }}
      >
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-pink-500 to-purple-600 px-6 py-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FaGift className="size-6 animate-bounce" />
              <h3 className="text-xl font-bold">{t('title')}</h3>
            </div>
            <button
              type="button"
              onClick={onClose}
              className="rounded-full p-1 text-white transition-colors hover:bg-white/20"
              disabled={isSubmitting}
            >
              <FaTimes className="size-5" />
            </button>
          </div>
          <p className="mt-2 text-sm opacity-90">{t('subtitle')}</p>
        </div>

        {/* Modal Body */}
        <div className="p-6">
          <p className="mb-4 text-gray-700">{t('description')}</p>

          {/* Benefits List */}
          <div className="mb-6 space-y-2">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>{t('benefit1')}</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>{t('benefit2')}</span>
            </div>
          </div>

          {/* Email Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <input
                type="email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                placeholder={t('email_placeholder')}
                className="w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-700 transition-colors focus:border-pink-500 focus:outline-none focus:ring-2 focus:ring-pink-200"
                required
                disabled={isSubmitting}
              />
            </div>

            {/* Message Display */}
            {message && (
              <div className={`rounded-lg p-3 text-sm ${
                messageType === 'success'
                  ? 'bg-green-100 text-green-700'
                  : 'bg-red-100 text-red-700'
              }`}
              >
                {message}
              </div>
            )}

            <button
              type="submit"
              disabled={isSubmitting || !email.trim()}
              className="w-full rounded-lg bg-gradient-to-r from-pink-500 to-purple-600 px-6 py-3 font-semibold text-white transition-all duration-300 hover:from-pink-600 hover:to-purple-700 hover:shadow-lg disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isSubmitting ? t('claiming') : t('claim_button')}
            </button>
          </form>

          {/* Terms */}
          <p className="mt-4 text-xs text-gray-500">{t('terms')}</p>
        </div>
      </div>
    </div>
  );
};

export default GiftModal;
