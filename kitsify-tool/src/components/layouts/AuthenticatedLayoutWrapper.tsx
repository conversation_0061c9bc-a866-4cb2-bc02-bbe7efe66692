'use client';

import dynamic from 'next/dynamic';

// Use dynamic import with SSR disabled for the authenticated layout
const AuthenticatedLayout = dynamic(
  () => import('./AuthenticatedLayout'),
  { ssr: false },
);

type AuthenticatedLayoutWrapperProps = {
  children: React.ReactNode;
};

const AuthenticatedLayoutWrapper: React.FC<AuthenticatedLayoutWrapperProps> = ({ children }) => {
  return <AuthenticatedLayout>{children}</AuthenticatedLayout>;
};

export default AuthenticatedLayoutWrapper;
