'use client';

import { useTranslations } from 'next-intl';
import React, { useEffect, useRef } from 'react';

type AdsGuideModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

const AdsGuideModal: React.FC<AdsGuideModalProps> = ({ isOpen, onClose }) => {
  const t = useTranslations('AdsGuideModal');
  const modalRef = useRef<HTMLDivElement>(null);

  // Close modal when clicking outside
  useEffect(() => {
    if (!isOpen) {
      return;
    }

    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, onClose]);

  // Disable body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
      <div
        ref={modalRef}
        className="mx-4 w-full max-w-2xl overflow-y-auto rounded-xl bg-white shadow-2xl transition-all duration-300 ease-in-out"
        style={{ animation: 'fadeIn 0.3s ease-in-out' }}
      >
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4 text-white">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold">{t('title')}</h3>
            <button
              type="button"
              onClick={onClose}
              className="rounded-full p-1 text-white transition-colors hover:bg-white/20"
            >
              <svg
                className="size-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Modal Body */}
        <div className="p-6">
          {/* Video Guide Section */}
          <div className="mb-6">
            <p className="mb-2 text-gray-700">
              <strong>{t('video_guide')}</strong>
              {' '}
              <a
                href="https://www.youtube.com/watch?v=your-video-id"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 underline hover:text-blue-800"
              >
                {t('video_link')}
              </a>
            </p>
          </div>

          {/* Step 1 */}
          <div className="mb-6">
            <p className="mb-2 text-gray-700">
              <strong>{t('step1_title')}</strong>
              {' '}
              {t('step1_description')}
              {' '}
              <a
                href={t('step1_link')}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 underline hover:text-blue-800"
              >
                {t('step1_link')}
              </a>
            </p>
          </div>

          {/* Step 2 */}
          <div className="mb-6">
            <p className="mb-4 text-gray-700">
              <strong>{t('step2_title')}</strong>
              {' '}
              {t('step2_description')}
            </p>

            {/* Email Section */}
            <div className="mb-4">
              <p className="mb-2 text-gray-700">
                <strong>{t('email_label')}</strong>
              </p>
              <div className="rounded bg-gray-100 p-3">
                <code className="text-sm text-gray-800">{t('email_value')}</code>
              </div>
            </div>

            {/* Password Section */}
            <div className="mb-4">
              <p className="mb-2 text-gray-700">
                <strong>{t('password_label')}</strong>
              </p>
              <div className="rounded bg-gray-100 p-3">
                <span className="text-sm text-gray-500">{t('password_placeholder')}</span>
              </div>
            </div>

            {/* Support Note */}
            <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
              <p className="text-sm text-yellow-800">
                {t('support_note')}
              </p>
            </div>
          </div>
        </div>

        {/* Modal Footer */}
        <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
          <div className="flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="rounded-lg bg-gray-400 px-4 py-2 text-sm font-medium text-white shadow-sm transition-all hover:bg-gray-500"
            >
              {t('close')}
            </button>
          </div>
        </div>
      </div>

      {/* Global styles for animation */}
      <style jsx global>
        {`
          @keyframes fadeIn {
            from {
              opacity: 0;
              transform: scale(0.95);
            }
            to {
              opacity: 1;
              transform: scale(1);
            }
          }
        `}
      </style>
    </div>
  );
};

export default AdsGuideModal;
