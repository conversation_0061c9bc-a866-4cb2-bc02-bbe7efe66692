'use client';

import { useTranslations } from 'next-intl';
import dynamic from 'next/dynamic';

const Collapse = dynamic(() => import('@/components/Collapse'));

type Question = {
  title: string;
  content: string;
};

const FAQ: React.FC = () => {
  const paymentT = useTranslations('Payment');

  // Get questions from translations
  let questions: Question[] = [];

  try {
    // Using ts-expect-error to bypass the type checking for raw
    // @ts-expect-error - raw method accepts string keys that might not be in the type
    const questionsData = paymentT.raw('questions') as Record<string, Question>;
    questions = Object.values(questionsData);
  } catch (error) {
    console.error('Error fetching questions:', error);
    questions = [];
  }

  return (
    <section className="bg-gray-100 py-16">
      <div className="container mx-auto">
        <h1 className="mb-6 text-center text-4xl font-bold">
          {paymentT('faq_title')}
        </h1>
        <div className="mx-auto space-y-4 md:w-3/4 lg:w-2/3">
          {questions.map((question) => {
            return (
              <div key={question.title} className="mb-4">
                <Collapse title={question.title}>
                  <p className="text-lg">{question.content}</p>
                </Collapse>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default FAQ;
