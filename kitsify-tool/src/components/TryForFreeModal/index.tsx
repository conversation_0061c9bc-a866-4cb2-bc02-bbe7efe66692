'use client';

import { useLocale, useTranslations } from 'next-intl';
import React, { useEffect, useRef } from 'react';
import { FaFacebookMessenger } from 'react-icons/fa';

type TryForFreeModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

const TryForFreeModal: React.FC<TryForFreeModalProps> = ({ isOpen, onClose }) => {
  const t = useTranslations('TryForFreeModal');
  const locale = useLocale();
  const modalRef = useRef<HTMLDivElement>(null);

  // Close modal when clicking outside
  useEffect(() => {
    if (!isOpen) {
      return;
    }

    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, onClose]);

  // Disable body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  const handleFanpageClick = () => {
    // Determine the correct Facebook Messenger URL based on locale
    const messengerUrl = locale === 'vi'
      ? 'https://www.facebook.com/messages/t/108592857933314'
      : 'https://www.facebook.com/messages/t/110106370397786';

    // Open in new tab
    window.open(messengerUrl, '_blank', 'noopener,noreferrer');
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 p-4">
      <div
        ref={modalRef}
        className="max-h-[90vh] w-full max-w-4xl overflow-y-auto rounded-2xl bg-white shadow-2xl transition-all duration-300 ease-in-out"
        style={{ animation: 'fadeIn 0.3s ease-in-out' }}
      >
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="mb-2 text-3xl font-bold">{t('title')}</h3>
              <p className="text-lg opacity-90">{t('subtitle')}</p>
            </div>
            <button
              type="button"
              onClick={onClose}
              className="rounded-full p-2 text-white transition-colors hover:bg-white/20"
            >
              <svg
                className="size-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Modal Body */}
        <div className="px-6 py-8">
          {/* Main Message */}
          <div className="mb-8 text-center">
            <h4 className="mb-4 text-2xl font-bold text-gray-800">{t('main_message')}</h4>
            <p className="text-lg text-gray-600">{t('description')}</p>
          </div>

          {/* Urgent Message */}
          <div className="mb-8 rounded-lg border-l-4 border-orange-500 bg-gradient-to-r from-orange-100 to-red-100 p-4">
            <p className="text-center font-semibold text-orange-800">{t('urgent_message')}</p>
          </div>

          {/* Benefits Section */}
          <div className="mb-8">
            <h5 className="mb-6 text-center text-xl font-bold">{t('benefits_title')}</h5>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="flex items-start space-x-3 rounded-lg bg-blue-50 p-4">
                <span className="text-2xl">{t('benefit1').split(' ')[0]}</span>
                <p className="text-gray-700">{t('benefit1').substring(2)}</p>
              </div>
              <div className="flex items-start space-x-3 rounded-lg bg-green-50 p-4">
                <span className="text-2xl">{t('benefit2').split(' ')[0]}</span>
                <p className="text-gray-700">{t('benefit2').substring(2)}</p>
              </div>
              <div className="flex items-start space-x-3 rounded-lg bg-purple-50 p-4">
                <span className="text-2xl">{t('benefit3').split(' ')[0]}</span>
                <p className="text-gray-700">{t('benefit3').substring(2)}</p>
              </div>
              <div className="flex items-start space-x-3 rounded-lg bg-red-50 p-4">
                <span className="text-2xl">{t('benefit4').split(' ')[0]}</span>
                <p className="text-gray-700">{t('benefit4').substring(2)}</p>
              </div>
              <div className="flex items-start space-x-3 rounded-lg bg-yellow-50 p-4">
                <span className="text-2xl">{t('benefit5').split(' ')[0]}</span>
                <p className="text-gray-700">{t('benefit5').substring(2)}</p>
              </div>
              <div className="flex items-start space-x-3 rounded-lg bg-pink-50 p-4">
                <span className="text-2xl">{t('benefit6').split(' ')[0]}</span>
                <p className="text-gray-700">{t('benefit6').substring(2)}</p>
              </div>
            </div>
          </div>

          {/* Contact Fanpage Section */}
          <div className="rounded-2xl bg-gradient-to-r from-blue-600 to-indigo-700 p-8 text-center text-white">
            <div className="mb-6">
              <div className="mx-auto mb-4 flex size-20 items-center justify-center rounded-full bg-white/20 text-white">
                <FaFacebookMessenger className="size-10" />
              </div>
              <h6 className="mb-3 text-2xl font-bold">{t('contact_fanpage')}</h6>
              <p className="text-lg opacity-90">{t('fanpage_description')}</p>
            </div>

            <button
              type="button"
              onClick={handleFanpageClick}
              className="inline-flex items-center space-x-3 rounded-full bg-white px-8 py-4 text-lg font-bold text-blue-600 shadow-lg transition-all duration-300 hover:scale-105 hover:bg-gray-100 hover:shadow-xl"
            >
              <FaFacebookMessenger className="size-6" />
              <span>{t('contact_fanpage')}</span>
            </button>
          </div>
        </div>

        {/* Modal Footer */}
        <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
          <div className="flex justify-center">
            <button
              type="button"
              onClick={onClose}
              className="rounded-lg bg-gray-500 px-6 py-2 text-sm font-medium text-white shadow-sm transition-all hover:bg-gray-600"
            >
              {t('close')}
            </button>
          </div>
        </div>
      </div>

      {/* Global styles for animation */}
      <style jsx global>
        {`
          @keyframes fadeIn {
            from {
              opacity: 0;
              transform: scale(0.95);
            }
            to {
              opacity: 1;
              transform: scale(1);
            }
          }
        `}
      </style>
    </div>
  );
};

export default TryForFreeModal;
