import Image from 'next/image';
import { useEffect, useState } from 'react';
import { Autoplay } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';

const feedbackTexts = [
  'Very convenient! Just 1 click and I can use many cool AI tools. Much cheaper than buying each tool separately.',
  'Easy to use website, intuitive interface. Many diverse AI tools for all my needs.',
  'Fast access speed, no lag. I love being able to use multiple AIs at once!',
  'I saved a lot thanks to the cheap package and many integrated AI tools.',
  'Friendly interface, easy to use even for beginners. Rich and frequently updated AI tools.',
  'With just one platform, I can work, study, and create more effectively.',
  'Cheap but quality matches big sites. Customer support is also enthusiastic.',
  'No need to register for each AI service separately anymore, saves so much time.',
  'I am very satisfied with the variety of AI tools provided by the site.',
  'An all-in-one solution for AI lovers. The price is so reasonable!',
  'Quick registration, easy to use, lots of useful features, very satisfied!',
  'Customer service responds quickly, very helpful when there is an issue.',
];

const Feedback: React.FC = () => {
  const [feedbacks, setFeedbacks] = useState<Array<{ text: string; avatarUrl: string }>>([]);

  useEffect(() => {
    const fetchUsers = async () => {
      const res = await fetch('https://randomuser.me/api/?results=12&nat=us');
      const data = await res.json();
      const users = data.results;

      // Map feedback text to avatar from API
      const combined = feedbackTexts.map((text, index) => ({
        text,
        avatarUrl: users[index]?.picture?.large || '',
      }));

      setFeedbacks(combined);
    };

    fetchUsers();
  }, []);

  return (
    <div className="w-full overflow-hidden">
      <Swiper
        modules={[Autoplay]}
        loop={feedbacks.length >= 4}
        autoplay={{
          delay: 200,
          disableOnInteraction: false,
        }}
        speed={2500}
        breakpoints={{
          480: {
            slidesPerView: 1,
            spaceBetween: 10,
          },
          768: {
            slidesPerView: 4,
            spaceBetween: 10,
          },
          1024: {
            slidesPerView: 4,
            spaceBetween: 40,
          },
          1480: {
            slidesPerView: 8,
            spaceBetween: 20,
          },
          1920: {
            slidesPerView: 10,
            spaceBetween: 20,
          },
        }}
      >
        {feedbacks.map((feedback, index) => (
          <SwiperSlide
            key={feedback.text}
            className="flex items-center justify-center"
          >
            {index % 2 === 0 && (
              <div className="flex flex-col">
                <div className="mb-6 w-96 rounded-3xl border px-5 py-4 shadow-lg">
                  <div className="flex items-center space-x-3">
                    <Image
                      className="size-10 rounded-full object-cover"
                      src={feedbacks[index]?.avatarUrl || ''}
                      alt="User Avatar"
                      width={40}
                      height={40}
                    />
                    <span>{feedbacks[index]?.text}</span>
                  </div>
                </div>
                <div className="mb-6 ms-7 w-96 rounded-3xl border px-5 py-4 shadow-lg">
                  <div className="flex items-center space-x-3">
                    <Image
                      className="size-10 rounded-full object-cover"
                      src={feedbacks[index + 1]?.avatarUrl || ''}
                      alt="User Avatar"
                      width={40}
                      height={40}
                    />
                    <span>{feedbacks[index + 1]?.text}</span>
                  </div>
                </div>
              </div>
            )}
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default Feedback;
