'use client';

import Link from 'next/link';
import { FaDiscord, FaFacebook, FaInstagram, FaTwitter } from 'react-icons/fa';

const DISCORD_LINK = 'https://discord.gg/QAQdZh5z';

const MainFooter = () => {
  // Get current year for copyright
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-gray-300">
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          {/* Company Info */}
          <div className="mb-6 md:mb-0">
            <h2 className="mb-6 text-2xl font-bold text-white">Kitsify</h2>
            <p className="mb-4 text-gray-400">
              Powerful toolkit designed to optimize E-commerce, Dropshipping, Affiliate, SEO, and more.
            </p>
            <div className="flex space-x-4">
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white" aria-label="Twitter">
                <FaTwitter size={20} />
              </a>
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white" aria-label="Facebook">
                <FaFacebook size={20} />
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white" aria-label="Instagram">
                <FaInstagram size={20} />
              </a>
              <a href={DISCORD_LINK} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white" aria-label="Discord">
                <FaDiscord size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="mb-6 text-lg font-semibold uppercase text-white">Quick Links</h3>
            <ul>
              <li className="mb-3">
                <Link href="/" className="text-gray-400 hover:text-white">Home</Link>
              </li>
              <li className="mb-3">
                <Link href="/tools" className="text-gray-400 hover:text-white">Tools</Link>
              </li>
              <li className="mb-3">
                <Link href="/payment" className="text-gray-400 hover:text-white">Pricing</Link>
              </li>
              <li className="mb-3">
                <Link href="/sign-in" className="text-gray-400 hover:text-white">Sign In</Link>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="mb-6 text-lg font-semibold uppercase text-white">Resources</h3>
            <ul>
              <li className="mb-3">
                <a href={DISCORD_LINK} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white">Discord Community</a>
              </li>
              <li className="mb-3">
                <Link href="#" className="text-gray-400 hover:text-white">Blog</Link>
              </li>
              <li className="mb-3">
                <Link href="#" className="text-gray-400 hover:text-white">Tutorials</Link>
              </li>
              <li className="mb-3">
                <Link href="#" className="text-gray-400 hover:text-white">Support</Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="mb-6 text-lg font-semibold uppercase text-white">Contact</h3>
            <p className="mb-3 text-gray-400">Email: <EMAIL></p>
            <p className="mb-3 text-gray-400">Discord: Join our community</p>
          </div>
        </div>

        <hr className="my-8 border-gray-700" />

        <div className="flex flex-col items-center justify-between md:flex-row">
          <p className="mb-4 text-sm text-gray-400 md:mb-0">
            &copy;
            {' '}
            {currentYear}
            {' '}
            Kitsify. All rights reserved.
          </p>
          <div className="flex space-x-6">
            <Link href="/privacy-policy" className="text-sm text-gray-400 hover:text-white">Privacy Policy</Link>
            <Link href="/" className="text-sm text-gray-400 hover:text-white">Terms of Service</Link>
            <Link href="/" className="text-sm text-gray-400 hover:text-white">Cookie Policy</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default MainFooter;
