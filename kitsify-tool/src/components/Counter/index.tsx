'use client';
import React, { useEffect, useState } from 'react';
import { useInView } from 'react-intersection-observer';

type CounterProps = {
  start: number;
  targetNumber: number;
};

const Counter: React.FC<CounterProps> = ({ start, targetNumber }) => {
  const [count, setCount] = useState(0);
  const { ref, inView } = useInView({
    triggerOnce: false,
    threshold: 0.1,
  });

  useEffect(() => {
    if (inView) {
      setCount(start);
      const interval = setInterval(() => {
        setCount((prev) => {
          if (prev < targetNumber) {
            return prev + 1;
          } else {
            clearInterval(interval);
            return prev;
          }
        });
      }, 10);

      return () => clearInterval(interval);
    }
    return () => {};
  }, [inView, start, targetNumber]);

  return <span ref={ref}>{count}</span>;
};

export default Counter;
