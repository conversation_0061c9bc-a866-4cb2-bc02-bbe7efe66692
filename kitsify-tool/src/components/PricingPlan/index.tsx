'use client';

import type { Package } from '@/services/packages';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import PaymentModal from '@/components/PaymentModal';
import { packageService } from '@/services/packages';

export const PricingPlan = () => {
  const [packages, setPackages] = useState<Package[]>([]);
  const [selectedMonth, setSelectedMonth] = useState<number>(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPackageId, setSelectedPackageId] = useState<number>(1);
  const [selectedDurationId, setSelectedDurationId] = useState<number | null>(null);
  const [expandedFeatures, setExpandedFeatures] = useState<Record<number, boolean>>({});
  const t = useTranslations('Payment');

  // Fetch packages when component mounts
  useEffect(() => {
    const fetchPackages = async () => {
      const months = [1, 3, 6, 12]; // Available month options
      // Always use USD as the currency
      const data = await packageService.getPackageDurations(months, 'USD');
      // Filter out packages with has_trail = true
      const filteredPackages = data.filter(pkg => !pkg.has_trail);
      setPackages(filteredPackages);
    };

    fetchPackages();
  }, []);

  // Prevent scrolling when modal is open
  useEffect(() => {
    if (isModalOpen) {
      // Add overflow-hidden to body when modal opens
      document.body.style.overflow = 'hidden';
    } else {
      // Remove overflow-hidden from body when modal closes
      document.body.style.overflow = 'auto';
    }

    // Cleanup function to ensure we remove the class when component unmounts
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isModalOpen]);

  // Handle month selection
  const handleMonthSelect = (month: number) => {
    setSelectedMonth(month);
  };

  // Render package features
  const renderFeatures = (pkg: Package, isBestValue: boolean) => {
    if (pkg.features && pkg.features.length > 0) {
      return pkg.features.map(feature => (
        <div key={`feature-${pkg.id}-${feature.description}`} className="flex items-center">
          {feature.img && (
            <Image
              src={feature.img}
              alt={feature.description}
              width={24}
              height={24}
              className="mr-2 rounded-full"
            />
          )}
          <span className="leading-none">
            {feature.description}
          </span>
        </div>
      ));
    }

    // Default features if package has no features
    return (
      <>
        <div className="flex items-start">
          <div className={`mr-3 flex size-6 shrink-0 items-center justify-center rounded-full p-1 shadow-sm ${isBestValue ? 'bg-gradient-to-r from-cyan-500 to-blue-500' : 'bg-gradient-to-r from-orange-400 to-red-500'}`}>
            <svg className="size-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
            </svg>
          </div>
          <span className={isBestValue ? 'text-gray-200' : 'text-gray-600'}>
            {t('email_support')}
          </span>
        </div>
        {isBestValue && (
          <div className="flex items-start">
            <div className="mr-3 flex size-6 shrink-0 items-center justify-center rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 p-1 shadow-sm">
              <svg className="size-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
              </svg>
            </div>
            <span className="text-gray-200">{t('live_chat_support')}</span>
          </div>
        )}
      </>
    );
  };

  return (
    <div className="container mx-auto text-center">
      <h1 className="mb-6 text-4xl font-bold">{t('pricing')}</h1>
      <p className="mb-6 text-xl">{t('save_description')}</p>

      {/* Duration Selector */}
      <div className="mx-auto mb-10 flex items-center justify-center">
        <div className="flex flex-wrap items-center gap-2 md:gap-3">
          {[1, 3, 6, 12].map(month => (
            <button
              key={month}
              type="button"
              className={`rounded-lg p-2 text-base font-medium transition-all duration-300 md:px-4 ${
                selectedMonth === month
                  ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg hover:bg-gradient-to-bl'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              onClick={() => handleMonthSelect(month)}
              aria-pressed={selectedMonth === month}
            >
              {month}
              {' '}
              {month === 1 ? t('month') : t('months')}
            </button>
          ))}
        </div>
      </div>

      <div className="mx-auto grid w-full grid-cols-1 gap-8 px-4 md:grid-cols-3 lg:px-10">
        {packages.filter(pkg => !pkg.is_single_tool).map((pkg) => {
          // Find the appropriate duration for the selected month
          const duration = pkg.durations.find(d => d.duration_days === (selectedMonth === 12 ? 365 : selectedMonth * 30)) || pkg.durations[0];
          if (!duration) {
            return null;
          }

          // Determine if this is the "best value" package based on is_best_choice field
          const isBestValue = pkg.is_best_choice || false;

          return (
            <div
              key={pkg.id}
              className="relative flex flex-col rounded-2xl border-t p-6 text-left shadow-xl"
            >
              {isBestValue && (
                <div className="absolute inset-x-0 -top-4 mx-auto w-max rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 px-4 py-1 text-sm font-bold text-white shadow-lg">
                  {t('best_value')}
                </div>
              )}

              <div className="mb-4 inline-flex w-max items-center justify-center gap-2">
                <span className="rounded-full bg-gradient-to-r from-orange-400 to-red-500 px-3 py-1 text-sm font-bold text-white shadow-sm">
                  {t('save')}
                  {' '}
                  {duration.discount_percent}
                  %
                </span>
              </div>

              <span className="mb-3 text-2xl font-bold">{pkg.name}</span>

              <div>
                <span className="text-lg leading-none text-gray-500 line-through">{`${duration.original_price}$`}</span>
              </div>

              <div className="mb-4">
                <span className="text-3xl font-bold leading-none text-gray-900">
                  {`${duration.price}$`}
                </span>
                <span>/</span>
                <span className="ml-2 text-lg text-gray-600">
                  {duration.duration_days}
                  {' '}
                  {t('days')}
                </span>
              </div>

              <div className="mb-6 grow">
                {/* Package features */}
                <div className={`relative ${!expandedFeatures[pkg.id] && pkg.features && pkg.features.length > 10 ? 'max-h-[350px] overflow-hidden' : ''}`}>
                  <div className="space-y-3">
                    {renderFeatures(pkg, isBestValue)}
                  </div>
                  {!expandedFeatures[pkg.id] && pkg.features && pkg.features.length > 10 && (
                    <div className="absolute inset-x-0 bottom-0 flex h-20 items-end justify-center bg-gradient-to-t from-white to-transparent">
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          setExpandedFeatures(prev => ({ ...prev, [pkg.id]: true }));
                        }}
                        className={`mb-2 flex items-center rounded-full px-4 py-2 text-sm font-medium text-white shadow-md transition-all duration-300 ${isBestValue ? 'bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600' : 'bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600'}`}
                      >
                        <span>{t('show_more')}</span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="ml-1 size-4" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
                {expandedFeatures[pkg.id] && pkg.features && pkg.features.length > 10 && (
                  <div className="mt-4 flex justify-center">
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        setExpandedFeatures(prev => ({ ...prev, [pkg.id]: false }));
                      }}
                      className={`flex items-center rounded-full px-4 py-2 text-sm font-medium text-white shadow-md transition-all duration-300 ${isBestValue ? 'bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600' : 'bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600'}`}
                    >
                      <span>{t('show_less')}</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="ml-1 size-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                )}
              </div>

              <button
                type="button"
                className={`mt-auto rounded-lg px-5 py-3 text-center text-base font-medium text-white shadow-md transition-all duration-300 focus:outline-none focus:ring-4 ${
                  isBestValue
                    ? 'bg-gradient-to-r from-cyan-500 to-blue-500 hover:bg-gradient-to-bl focus:ring-cyan-300'
                    : 'bg-gradient-to-r from-purple-500 to-indigo-500 hover:bg-gradient-to-bl focus:ring-purple-300'
                }`}
                onClick={() => {
                  setSelectedPackageId(pkg.id);
                  setSelectedDurationId(duration.id);
                  setIsModalOpen(true);
                }}
              >
                {t('buy_now')}
              </button>
            </div>
          );
        })}
      </div>

      {/* Single Tools Section */}
      {packages.filter(pkg => pkg.is_single_tool).length > 0 && (
        <>
          <div className="mb-5 mt-10">
            <h3 className="mb-4 text-3xl font-bold">{t('single_tools')}</h3>
            <p className="text-lg text-gray-600">{t('single_tools_description')}</p>
          </div>

          <div className="mx-auto grid w-full grid-cols-1 gap-8 px-4 md:grid-cols-2 lg:grid-cols-3 lg:px-10">
            {packages.filter(pkg => pkg.is_single_tool).map((pkg) => {
              // Find the appropriate duration for the selected month
              const duration = pkg.durations.find(d => d.duration_days === (selectedMonth === 12 ? 365 : selectedMonth * 30)) || pkg.durations[0];
              if (!duration) {
                return null;
              }

              return (
                <div
                  key={pkg.id}
                  className="relative flex flex-col rounded-2xl border-t p-6 text-left shadow-xl"
                >
                  {/* Package Image */}
                  {pkg.pk_img && (
                    <div className="mb-4">
                      <div className="relative h-32 w-full overflow-hidden rounded-lg bg-white shadow-md">
                        <Image
                          src={pkg.pk_img}
                          alt={pkg.name}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                      </div>
                    </div>
                  )}

                  {/* Discount Badge */}
                  {duration.discount_percent > 0 && (
                    <div className="mb-4 inline-flex w-max items-center justify-center gap-2">
                      <span className="rounded-full bg-gradient-to-r from-orange-400 to-red-500 px-3 py-1 text-sm font-bold text-white shadow-sm">
                        {t('save')}
                        {' '}
                        {duration.discount_percent}
                        %
                      </span>
                    </div>
                  )}

                  {/* Package Name */}
                  <span className="mb-3 text-2xl font-bold">{pkg.name}</span>

                  {/* Original Price */}
                  {duration.discount_percent > 0 && (
                    <div>
                      <span className="text-lg leading-none text-gray-500 line-through">
                        {duration.original_price}
                        $
                      </span>
                    </div>
                  )}

                  {/* Current Price */}
                  <div className="mb-4">
                    <span className="text-3xl font-bold leading-none text-gray-900">
                      {duration.price}
                      $
                    </span>
                    <span>/</span>
                    <span className="ml-2 text-lg text-gray-600">
                      {duration.duration_days}
                      {' '}
                      {t('days')}
                    </span>
                  </div>

                  {/* Features */}
                  <div className="mb-6 grow">
                    <div className={`relative ${!expandedFeatures[pkg.id] && pkg.features && pkg.features.length > 10 ? 'max-h-[500px] overflow-hidden' : ''}`}>
                      <div className="space-y-3">
                        {renderFeatures(pkg, false)}
                      </div>
                      {!expandedFeatures[pkg.id] && pkg.features && pkg.features.length > 10 && (
                        <div className="absolute inset-x-0 bottom-0 flex h-20 items-end justify-center bg-gradient-to-t from-white to-transparent">
                          <button
                            type="button"
                            onClick={(e) => {
                              e.stopPropagation();
                              setExpandedFeatures(prev => ({ ...prev, [pkg.id]: true }));
                            }}
                            className="mb-2 flex items-center rounded-full bg-gradient-to-r from-purple-500 to-indigo-500 px-4 py-2 text-sm font-medium text-white shadow-md transition-all duration-300 hover:from-purple-600 hover:to-indigo-600"
                          >
                            <span>{t('show_more')}</span>
                            <svg xmlns="http://www.w3.org/2000/svg" className="ml-1 size-4" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </button>
                        </div>
                      )}
                    </div>
                    {expandedFeatures[pkg.id] && pkg.features && pkg.features.length > 10 && (
                      <div className="mt-4 flex justify-center">
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation();
                            setExpandedFeatures(prev => ({ ...prev, [pkg.id]: false }));
                          }}
                          className="flex items-center rounded-full bg-gradient-to-r from-purple-500 to-indigo-500 px-4 py-2 text-sm font-medium text-white shadow-md transition-all duration-300 hover:from-purple-600 hover:to-indigo-600"
                        >
                          <span>{t('show_less')}</span>
                          <svg xmlns="http://www.w3.org/2000/svg" className="ml-1 size-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Buy Button */}
                  <button
                    type="button"
                    className="mt-auto rounded-lg bg-gradient-to-r from-purple-500 to-indigo-500 px-5 py-3 text-center text-base font-medium text-white shadow-md transition-all duration-300 hover:bg-gradient-to-bl focus:outline-none focus:ring-4 focus:ring-purple-300"
                    onClick={() => {
                      setSelectedPackageId(pkg.id);
                      setSelectedDurationId(duration.id);
                      setIsModalOpen(true);
                    }}
                  >
                    {t('buy_now')}
                  </button>
                </div>
              );
            })}
          </div>
        </>
      )}
      <div className="mt-6 text-center">
        <Link href="/shop">
          <button type="button" className="rounded-lg bg-gradient-to-r from-slate-400 to-slate-500 px-5 py-2 text-center text-base font-medium text-white shadow-md transition-all duration-300 hover:bg-gradient-to-bl focus:outline-none focus:ring-4 focus:ring-slate-300">
            {t('see_more')}
          </button>
        </Link>
      </div>

      <PaymentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        packageId={selectedPackageId}
        durationId={selectedDurationId}
      />
    </div>
  );
};

export default PricingPlan;
