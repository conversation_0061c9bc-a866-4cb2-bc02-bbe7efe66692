import type { Category } from '@/services/categories';
import React, { useEffect, useState } from 'react';
import { categoriesService } from '@/services/categories';

type FilterSidebarProps = {
  selectedCategories: number[];
  onCategoryChange: (categories: number[]) => void;
  searchTerm: string;
  onSearchChange: (search: string) => void;
};

const FilterSidebar: React.FC<FilterSidebarProps> = ({
  selectedCategories,
  onCategoryChange,
  searchTerm,
  onSearchChange,
}) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  // Load categories on mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const data = await categoriesService.getCategories();
        setCategories(data);
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleCategoryToggle = (categoryId: number) => {
    if (selectedCategories.includes(categoryId)) {
      onCategoryChange(selectedCategories.filter(c => c !== categoryId));
    } else {
      onCategoryChange([...selectedCategories, categoryId]);
    }
  };

  return (
    <div className="rounded-lg bg-white p-4 shadow-md">
      <h2 className="mb-4 text-lg font-semibold">Filters</h2>

      {/* Search */}
      <div className="mb-6">
        <label htmlFor="search" className="mb-1 block text-sm font-medium text-gray-700">
          Search
        </label>
        <input
          type="text"
          id="search"
          value={searchTerm}
          onChange={e => onSearchChange(e.target.value)}
          placeholder="Search products..."
          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Categories */}
      <div>
        <h3 className="mb-2 text-sm font-medium text-gray-700">Categories</h3>
        <div className="space-y-2">
          {loading
            ? (
                <div className="text-sm text-gray-500">Loading categories...</div>
              )
            : (
                categories.map(category => (
                  <div key={category.id} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`category-${category.id}`}
                      checked={selectedCategories.includes(category.id)}
                      onChange={() => handleCategoryToggle(category.id)}
                      className="size-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label
                      htmlFor={`category-${category.id}`}
                      className="ml-2 block text-sm text-gray-700"
                    >
                      {category.name}
                    </label>
                  </div>
                ))
              )}
        </div>
      </div>
    </div>
  );
};

export default FilterSidebar;
