'use client';

import type { FC } from 'react';
import { useTranslations } from 'next-intl';

import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import TryForFreeModal from '@/components/TryForFreeModal';

import { useRouter } from '@/libs/i18nNavigation';
import { authService } from '@/services/auth';

// Removed unused Discord invite link

const Header: FC = () => {
  const router = useRouter();
  const t = useTranslations('Header');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isTryForFreeModalOpen, setIsTryForFreeModalOpen] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      const authStatus = await authService.checkAuthStatus();
      setIsAuthenticated(authStatus);
    };

    checkAuth();
  }, []);

  // Listen for auth change events
  useEffect(() => {
    // Event listener for auth status changes
    const handleAuthChange = () => {
      setIsAuthenticated(true);
    };

    // Add event listener for auth changes
    window.addEventListener('kitsify-auth-change', handleAuthChange);

    // Clean up event listener
    return () => {
      window.removeEventListener('kitsify-auth-change', handleAuthChange);
    };
  }, []);

  const handleAuthButtonClick = () => {
    if (isAuthenticated) {
      router.push('/tools');
    } else {
      router.push('/sign-in');
    }
  };

  const handleTryForFreeClick = () => {
    setIsTryForFreeModalOpen(true);
  };

  return (
    <header className="sticky top-0 z-50 shadow-md transition-all duration-300">
      <nav className="fixed start-0 top-0 z-20 w-full border-b border-gray-200 bg-white">
        <div className="container mx-auto flex flex-wrap items-center justify-between py-2">
          <Link href="/" className="flex items-center rtl:space-x-reverse">
            <Image src="/assets/images/kitsify_rect.png" alt="Kitsify Logo" height={55} width={0} unoptimized style={{ height: '55px', width: 'auto' }} />
          </Link>
          <div className="flex md:order-2 md:space-x-0 rtl:space-x-reverse">
            <button
              type="button"
              onClick={handleTryForFreeClick}
              className="me-2 rounded-xl bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-800 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
            >
              {t('try_for_free')}
            </button>
            <button
              type="button"
              onClick={handleAuthButtonClick}
              className="rounded-xl border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100"
            >
              {isAuthenticated ? t('dashboard') : t('login')}
            </button>
          </div>
          <div className="hidden w-full items-center justify-between md:order-1 md:flex md:w-auto" id="navbar-sticky">
            <ul className="mt-4 flex flex-col rounded-lg border border-gray-100 bg-gray-50 p-4 font-medium md:mt-0 md:flex-row md:space-x-8 md:border-0 md:bg-white md:p-0 rtl:space-x-reverse">
              <li>
                <a href="#top" className="block rounded-sm px-3 py-2 text-lg text-gray-900 hover:bg-gray-100 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-500">{t('home')}</a>
              </li>
              <li>
                <a href="#pricing" className="block rounded-sm px-3 py-2 text-lg text-gray-900 hover:bg-gray-100 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-500">{t('pricing')}</a>
              </li>
              <li>
                <a href="#tools" className="block rounded-sm px-3 py-2 text-lg text-gray-900 hover:bg-gray-100 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-500">{t('tools')}</a>
              </li>
              <li>
                <a href="#guide" className="block rounded-sm px-3 py-2 text-lg text-gray-900 hover:bg-gray-100 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-500">{t('guide')}</a>
              </li>
              <li>
                <Link href="/shop" className="block rounded-sm px-3 py-2 text-lg text-gray-900 hover:bg-gray-100 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-500">
                  {t('shop')}
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </nav>

      {/* Try For Free Modal */}
      <TryForFreeModal
        isOpen={isTryForFreeModalOpen}
        onClose={() => setIsTryForFreeModalOpen(false)}
      />
    </header>
  );
};

export default Header;
