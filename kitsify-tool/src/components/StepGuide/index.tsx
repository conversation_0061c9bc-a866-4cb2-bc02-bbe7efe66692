'use client';

import { useTranslations } from 'next-intl';
import React from 'react';
import { FaDownload } from 'react-icons/fa';
import { MdDashboard } from 'react-icons/md';
import { RiMoneyDollarCircleLine } from 'react-icons/ri';
import { TbClick } from 'react-icons/tb';

type Step = {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  buttonText?: string;
  buttonLink?: string;
};

const StepGuide: React.FC = () => {
  const t = useTranslations('Index');

  const steps: Step[] = [
    {
      id: 1,
      title: 'CLICK_ON_GET_ACCESS',
      description: 'CLICK_ON_GET_ACCESS_DESC',
      icon: <TbClick className="size-8" />,
    },
    {
      id: 2,
      title: 'PAY_FOR_THE_PLAN',
      description: 'PAY_FOR_THE_PLAN_DESC',
      icon: <RiMoneyDollarCircleLine className="size-8" />,
    },
    {
      id: 3,
      title: 'OPEN_DASHBOARD',
      description: 'OPEN_DASHBOARD_DESC',
      icon: <MdDashboard className="size-8" />,
    },
    {
      id: 4,
      title: 'DOWNLOAD_EXTENSION',
      description: 'DOWNLOAD_EXTENSION_DESC',
      icon: <FaDownload className="size-8" />,
    },
  ];

  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
        {steps.map((step, _) => (
          <div key={step.id} className="relative">
            <div className="absolute right-0 top-8 hidden w-full border-t-2 border-gray-300 lg:block" />
            <div className="relative flex flex-col items-center text-center">
              {/* Icon circle */}
              <div className="mb-4 flex size-16 items-center justify-center rounded-full bg-gray-100">
                {step.icon}
              </div>
              {/* Content */}
              <span className="mb-2 text-lg font-bold">
                {t(`STEPS_GUIDE.${step.title}` as any)}
              </span>
              <p className="text-gray-600">
                {t(`STEPS_GUIDE.${step.description}` as any)}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default StepGuide;
