import React, { useEffect, useState } from 'react';
import { subscriptionService } from '@/services/subscription';
import { toastError, toastSuccess } from '@/utils/toast';

type SubscriptionStatus = {
  id: number;
  paypal_subscription_id: string;
  paypal_plan_id: string;
  status: string;
  start_date: string;
  next_billing_date?: string;
  amount: number;
  currency: string;
  cancelled_at?: string;
  cancel_reason?: string;
  packageDuration?: {
    id: number;
    duration_days: number;
    price: number;
    package: {
      id: number;
      name: string;
      description: string;
    };
  };
};

export default function SubscriptionManagement() {
  const [subscriptions, setSubscriptions] = useState<SubscriptionStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [cancellingId, setCancellingId] = useState<number | null>(null);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState<SubscriptionStatus | null>(null);
  const [cancelReason, setCancelReason] = useState('');

  const loadSubscriptions = async () => {
    try {
      setLoading(true);
      const data = await subscriptionService.getUserSubscriptions();
      setSubscriptions(data);
    } catch (error) {
      console.error('Failed to load subscriptions:', error);
      toastError('Failed to load subscriptions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSubscriptions();
  }, []);

  const handleCancelSubscription = async () => {
    if (!selectedSubscription) {
      return;
    }

    try {
      setCancellingId(selectedSubscription.id);
      const result = await subscriptionService.cancelSubscription(
        selectedSubscription.id,
        cancelReason || 'User requested cancellation',
      );

      if (result.success) {
        toastSuccess('Subscription cancelled successfully');
        setShowCancelModal(false);
        setCancelReason('');
        setSelectedSubscription(null);
        await loadSubscriptions(); // Reload subscriptions
      } else {
        toastError(result.error || 'Failed to cancel subscription');
      }
    // eslint-disable-next-line unused-imports/no-unused-vars
    } catch (error) {
      toastError('Failed to cancel subscription');
    } finally {
      setCancellingId(null);
    }
  };

  const openCancelModal = (subscription: SubscriptionStatus) => {
    setSelectedSubscription(subscription);
    setShowCancelModal(true);
  };

  const closeCancelModal = () => {
    setShowCancelModal(false);
    setSelectedSubscription(null);
    setCancelReason('');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="size-12 animate-spin rounded-full border-4 border-blue-500 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-4xl p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Subscription Management</h1>
        <p className="mt-2 text-gray-600">Manage your active subscriptions and billing information.</p>
      </div>

      {subscriptions.length === 0
        ? (
            <div className="rounded-lg bg-gray-50 p-8 text-center">
              <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-full bg-gray-200">
                <svg className="size-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900">No subscriptions found</h3>
              <p className="mt-2 text-gray-500">You don't have any active subscriptions yet.</p>
            </div>
          )
        : (
            <div className="space-y-6">
              {subscriptions.map(subscription => (
                <div key={subscription.id} className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="mb-2 flex items-center gap-3">
                        <h3 className="text-xl font-semibold text-gray-900">
                          {subscription.packageDuration?.package.name || 'Subscription'}
                        </h3>
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${subscriptionService.getStatusColor(subscription.status)} bg-opacity-10`}>
                          {subscriptionService.getStatusText(subscription.status)}
                        </span>
                      </div>

                      <p className="mb-4 text-gray-600">
                        {subscription.packageDuration?.package.description || 'Monthly subscription service'}
                      </p>

                      <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-3">
                        <div>
                          <span className="font-medium text-gray-500">Monthly Amount:</span>
                          <div className="text-lg font-semibold text-gray-900">
                            {subscriptionService.formatCurrency(subscription.amount, subscription.currency)}
                          </div>
                        </div>

                        <div>
                          <span className="font-medium text-gray-500">Start Date:</span>
                          <div className="text-gray-900">{formatDate(subscription.start_date)}</div>
                        </div>

                        {subscription.next_billing_date && (
                          <div>
                            <span className="font-medium text-gray-500">Next Billing:</span>
                            <div className="text-gray-900">{formatDate(subscription.next_billing_date)}</div>
                          </div>
                        )}
                      </div>

                      {subscription.cancelled_at && (
                        <div className="mt-4 rounded-md bg-red-50 p-3">
                          <div className="text-sm">
                            <span className="font-medium text-red-800">Cancelled on:</span>
                            <span className="ml-1 text-red-700">{formatDate(subscription.cancelled_at)}</span>
                          </div>
                          {subscription.cancel_reason && (
                            <div className="mt-1 text-sm text-red-700">
                              <span className="font-medium">Reason:</span>
                              {' '}
                              {subscription.cancel_reason}
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    <div className="ml-6">
                      {subscription.status === 'active' && (
                        <button
                          type="button"
                          onClick={() => openCancelModal(subscription)}
                          disabled={cancellingId === subscription.id}
                          className="rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50"
                        >
                          {cancellingId === subscription.id ? 'Cancelling...' : 'Cancel Subscription'}
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

      {/* Cancel Confirmation Modal */}
      {showCancelModal && selectedSubscription && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="mx-4 w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
            <h3 className="mb-4 text-lg font-medium text-gray-900">
              Cancel Subscription
            </h3>

            <p className="mb-4 text-gray-600">
              Are you sure you want to cancel your subscription to
              {' '}
              <strong>{selectedSubscription.packageDuration?.package.name}</strong>
              ?
              This action cannot be undone.
            </p>

            <div className="mb-4">
              <label htmlFor="cancelReason" className="mb-2 block text-sm font-medium text-gray-700">
                Reason for cancellation (optional)
              </label>
              <textarea
                id="cancelReason"
                rows={3}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                placeholder="Please let us know why you're cancelling..."
                value={cancelReason}
                onChange={e => setCancelReason(e.target.value)}
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={closeCancelModal}
                className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Keep Subscription
              </button>
              <button
                type="button"
                onClick={handleCancelSubscription}
                disabled={cancellingId === selectedSubscription.id}
                className="rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50"
              >
                {cancellingId === selectedSubscription.id ? 'Cancelling...' : 'Cancel Subscription'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
