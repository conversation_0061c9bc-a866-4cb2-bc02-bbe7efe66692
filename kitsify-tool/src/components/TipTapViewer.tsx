'use client';

import Color from '@tiptap/extension-color';
import TextStyle from '@tiptap/extension-text-style';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { marked } from 'marked';
import { useEffect } from 'react';

type TipTapViewerProps = {
  content: string;
  className?: string;
  editable?: boolean;
  onContentChange?: (content: string) => void;
  markdownMode?: boolean;
};

// Function to preprocess content and convert \n to <br> tags or process markdown
const preprocessContent = (content: string, markdownMode = false): string => {
  if (!content) {
    return content;
  }

  if (markdownMode) {
    try {
      // Configure marked for better HTML output
      marked.setOptions({
        breaks: true, // Convert line breaks to <br>
        gfm: true, // GitHub Flavored Markdown
      });

      // Convert markdown to HTML
      return marked(content) as string;
    } catch (error) {
      console.error('Error processing markdown:', error);
      // Fallback to basic line break replacement
      return content.replace(/\n/g, '<br>');
    }
  }

  // Handle line breaks within HTML tags
  // Replace \n with <br> tags, but be careful not to break HTML structure
  return content.replace(/\n/g, '<br>');
};

export default function TipTapViewer({
  content,
  className = '',
  editable = false,
  onContentChange,
  markdownMode = false,
}: TipTapViewerProps) {
  // Preprocess content to handle line breaks or markdown
  const processedContent = preprocessContent(content, markdownMode);

  const editor = useEditor({
    extensions: [
      StarterKit,
      TextStyle,
      Color,
    ],
    content: processedContent,
    editable,
    onUpdate: ({ editor }) => {
      if (editable && onContentChange) {
        onContentChange(editor.getHTML());
      }
    },
    editorProps: {
      attributes: {
        class: `prose prose-sm sm:prose lg:prose-lg xl:prose-xl mx-auto focus:outline-none ${
          editable ? 'border border-gray-300 rounded-lg p-4 min-h-[200px]' : ''
        } ${className}`,
      },
    },
  });

  useEffect(() => {
    if (editor) {
      const newProcessedContent = preprocessContent(content, markdownMode);
      if (newProcessedContent !== editor.getHTML()) {
        editor.commands.setContent(newProcessedContent);
      }
    }
  }, [editor, content, markdownMode]);

  if (!editor) {
    return (
      <div className="animate-pulse">
        <div className="mb-2 h-4 w-3/4 rounded bg-gray-200"></div>
        <div className="mb-2 h-4 w-1/2 rounded bg-gray-200"></div>
        <div className="h-4 w-5/6 rounded bg-gray-200"></div>
      </div>
    );
  }

  return (
    <div className={`tiptap-viewer ${className}`}>
      <EditorContent editor={editor} />
    </div>
  );
}
