'use client';

import { useLocale, useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { usePathname, useRouter } from '@/libs/i18nNavigation';
import { authService } from '@/services/auth';
import { AppConfig } from '@/utils/AppConfig';
import { getLocaleDisplayName, getLocaleFlag } from '@/utils/localeDetection';

type AuthHeaderProps = {
  toggleSidebar: () => void;
};

const AuthHeader: React.FC<AuthHeaderProps> = ({ toggleSidebar }) => {
  const t = useTranslations('Header');
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale();
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const [languageDropdownOpen, setLanguageDropdownOpen] = useState(false);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('#user-dropdown-button') && !target.closest('#user-dropdown')) {
        setUserDropdownOpen(false);
      }
      if (!target.closest('#language-dropdown-button') && !target.closest('#language-dropdown')) {
        setLanguageDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle language change
  const handleLanguageChange = (newLocale: string) => {
    // Set cookie to remember user's preference
    document.cookie = `preferred-locale=${newLocale}; max-age=${60 * 60 * 24 * 365}; path=/; samesite=lax`;

    // Use the i18n router which handles locale switching properly
    // pathname from usePathname() already excludes the locale prefix
    router.replace(pathname, { locale: newLocale });
    setLanguageDropdownOpen(false);
  };

  return (
    <header className="sticky top-0 z-10 flex h-16 items-center bg-white shadow">
      <div className="flex w-full items-center justify-between px-4">
        {/* Left side - Mobile menu button */}
        <button
          type="button"
          onClick={toggleSidebar}
          className="rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 focus:outline-none md:hidden"
        >
          <svg className="size-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>

        {/* Page title - visible on desktop */}
        <h1 className="hidden text-xl font-semibold text-gray-800 md:block">
          {t('dashboard')}
        </h1>

        {/* Right side - Language switcher and User dropdown */}
        <div className="flex items-center space-x-3">
          {/* Language Switcher */}
          <div className="relative">
            <button
              type="button"
              id="language-dropdown-button"
              onClick={() => setLanguageDropdownOpen(!languageDropdownOpen)}
              className="flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <span className="mr-2">{getLocaleFlag(locale)}</span>
              <span className="hidden sm:block">{getLocaleDisplayName(locale)}</span>
              <svg
                className="ml-1 size-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {/* Language Dropdown menu */}
            {languageDropdownOpen && (
              <div
                id="language-dropdown"
                className="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black/5 focus:outline-none"
              >
                {AppConfig.locales.map(localeOption => (
                  <button
                    key={localeOption}
                    type="button"
                    onClick={() => handleLanguageChange(localeOption)}
                    className={`flex w-full items-center px-4 py-2 text-left text-sm hover:bg-gray-100 ${
                      locale === localeOption ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                    }`}
                  >
                    <span className="mr-3">{getLocaleFlag(localeOption)}</span>
                    <span>{getLocaleDisplayName(localeOption)}</span>
                    {locale === localeOption && (
                      <svg
                        className="ml-auto size-4 text-blue-600"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* User Dropdown */}
          <div className="relative">
            <button
              type="button"
              id="user-dropdown-button"
              onClick={() => setUserDropdownOpen(!userDropdownOpen)}
              className="flex items-center rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <span className="sr-only">Open user menu</span>
              <div className="flex items-center">
                <div className="flex size-8 items-center justify-center rounded-full bg-blue-500 text-white">
                  <span className="font-medium">U</span>
                </div>
                <span className="ml-2 hidden text-sm font-medium text-gray-700 md:block">
                  User
                </span>
                <svg
                  className="ml-1 size-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </div>
            </button>

            {/* Dropdown menu */}
            {userDropdownOpen && (
              <div
                id="user-dropdown"
                className="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black/5 focus:outline-none"
              >
                <button
                  type="button"
                  onClick={async () => {
                    try {
                      const result = await authService.logout();
                      if (result.success) {
                        router.push('/sign-in');
                      } else {
                        toast.error(result.error);
                      }
                    } catch (error) {
                      console.error('Logout error:', error);
                    }
                  }}
                  className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                >
                  {t('sign_out')}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default AuthHeader;
