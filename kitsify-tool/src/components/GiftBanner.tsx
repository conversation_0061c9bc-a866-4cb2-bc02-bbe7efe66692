import Image from 'next/image';
import React from 'react';
import { IoClose } from 'react-icons/io5';

type GiftBannerProps = {
  isVisible: boolean;
  onClose: () => void;
};

const GiftBanner: React.FC<GiftBannerProps> = ({ isVisible, onClose }) => {
  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative m-4 lg:h-[500px] lg:w-[800px]">
        {/* Close button */}
        <button
          type="button"
          onClick={onClose}
          className="absolute -right-3 z-10 rounded-full bg-white p-2 shadow-lg transition-all duration-200 hover:bg-gray-100"
        >
          <IoClose size={20} className="text-gray-600" />
        </button>

        {/* Banner image */}
        <div className="overflow-hidden">
          <Image
            src="/assets/images/gift_tool_banner.png"
            alt="Gift Tool Banner"
            width={800}
            height={500}
            className="h-auto w-full object-cover"
          />
        </div>
      </div>
    </div>
  );
};

export default GiftBanner;
