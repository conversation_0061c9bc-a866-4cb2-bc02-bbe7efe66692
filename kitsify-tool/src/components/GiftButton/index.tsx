'use client';

import { useEffect, useState } from 'react';
import { FaGift } from 'react-icons/fa';

type GiftButtonProps = {
  onClick: () => void;
};

const GiftButton: React.FC<GiftButtonProps> = ({ onClick }) => {
  const [isClient, setIsClient] = useState(false);

  // Ensure component only renders on client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Don't render on server side to avoid hydration issues
  if (!isClient) {
    return null;
  }

  return (
    <button
      type="button"
      onClick={onClick}
      className="fixed bottom-20 right-4 z-50 flex size-12 animate-pulse items-center justify-center rounded-full bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-lg transition-all duration-300 hover:scale-110 hover:from-pink-600 hover:to-purple-700 hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-pink-300 md:bottom-24 md:right-6 md:size-14"
      aria-label="Claim special gift"
      title="🎁 Claim your special gift!"
    >
      <FaGift className="size-5 md:size-6" />
    </button>
  );
};

export default GiftButton;
