'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { usePackages } from '@/contexts/PackagesContext';
import { usePathname, useRouter } from '@/libs/i18nNavigation';
import { authService } from '@/services/auth';

type SidebarProps = {
  isOpen: boolean;
  toggleSidebar: () => void;
};

// Define a type for navigation items
type NavItem = {
  name: string;
  href: string;
  icon: React.ReactNode;
  subItems?: {
    name: string;
    href: string;
  }[];
};

const Sidebar: React.FC<SidebarProps> = ({ isOpen, toggleSidebar }) => {
  const t = useTranslations('Navigation');
  const router = useRouter();
  const pathname = usePathname();

  // Use packages context
  const { hasActivePackage, hasFeatureAccess } = usePackages();

  // State to track which submenus are open
  const [openSubMenus, setOpenSubMenus] = useState<Record<string, boolean>>({});
  // State to track user status
  const [userStatus, setUserStatus] = useState<string | null>(null);

  // Check user status
  useEffect(() => {
    const checkUserStatus = async () => {
      try {
        const response = await authService.getUserStatus();
        if (response.isAuthenticated && response.user) {
          setUserStatus(response.user.status);
        }
      } catch (error) {
        console.error('Error checking user status:', error);
      }
    };

    checkUserStatus();
  }, []);

  // Toggle submenu open/closed
  const toggleSubMenu = (itemName: string) => {
    setOpenSubMenus(prev => ({
      ...prev,
      [itemName]: !prev[itemName],
    }));
  };

  // Define base navigation items
  const baseNavItems: NavItem[] = [
    {
      name: t('dashboard'),
      href: '/tools',
      icon: (
        <svg className="size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
        </svg>
      ),
    },
    {
      name: t('package_info'),
      href: '/package',
      icon: (
        <svg className="size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
        </svg>
      ),
    },
  ];

  // Resources menu item
  const resourcesMenuItem: NavItem = {
    name: t('resources'),
    href: '#',
    icon: (
      <svg className="size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
      </svg>
    ),
    subItems: [
      {
        name: t('tshirt_designs'),
        href: '/resources/tshirt-designs',
      },
      {
        name: t('theme'),
        href: '/resources/theme',
      },
      {
        name: t('courses'),
        href: '/resources/courses',
      },
      {
        name: t('books'),
        href: '/resources/books',
      },
      {
        name: t('ads_template'),
        href: '/resources/ads-template',
      },
    ],
  };

  // Prompt Library menu item
  const promptLibraryMenuItem: NavItem = {
    name: t('prompt_library'),
    href: '/prompts',
    icon: (
      <svg className="size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
    ),
    subItems: [
      {
        name: t('prompt_library'),
        href: '/prompts',
      },
      {
        name: t('prompt_history'),
        href: '/prompts/history',
      },
    ],
  };

  // Prompt Video menu item
  const promptVideoMenuItem: NavItem = {
    name: t('prompt_video'),
    href: '/prompt-video',
    icon: (
      <svg className="size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
      </svg>
    ),
    subItems: [
      {
        name: t('generate_prompt'),
        href: '/prompt-video/generate',
      },
      {
        name: t('prompt_video_history'),
        href: '/prompt-video/history',
      },
    ],
  };

  // Build navigation items based on permissions
  const navItems: NavItem[] = [...baseNavItems];

  // Add prompt library menu item only if user has permission
  if (hasFeatureAccess('has_prompt_library')) {
    navItems.push(promptLibraryMenuItem);
  }

  // Add prompt video menu item only if user has permission
  if (hasFeatureAccess('has_prompt_video')) {
    navItems.push(promptVideoMenuItem);
  }

  // Add resources menu item only if user has active package/product and user status is not 'inactive'
  if (hasActivePackage() && userStatus !== 'inactive') {
    navItems.push(resourcesMenuItem);
  }

  return (
    <>
      {/* Mobile sidebar backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-20 bg-black/50 transition-opacity md:hidden"
          onClick={toggleSidebar}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              toggleSidebar();
            }
          }}
          role="button"
          tabIndex={0}
          aria-label={t('close_sidebar')}
        >
        </div>
      )}

      {/* Sidebar */}
      <aside
        className={`fixed inset-y-0 left-0 z-30 w-64 bg-white shadow-lg transition-all duration-300 ease-in-out md:relative md:translate-x-0${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex h-full flex-col">
          {/* Sidebar header */}
          <div className="flex h-16 items-center justify-between border-b px-4">
            <div className="flex items-center">
              <Link href="/" className="flex items-center rtl:space-x-reverse">
                <Image src="/assets/images/kitsify_rect.png" alt="Kitsify Logo" height={50} width={0} unoptimized style={{ height: '50px', width: 'auto' }} />
              </Link>
            </div>
            <button
              type="button"
              onClick={toggleSidebar}
              className="rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 focus:outline-none md:hidden"
            >
              <svg className="size-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto px-2 py-4">
            <ul className="space-y-2">
              {navItems.map((item) => {
                const isActive = pathname === item.href;
                const isSubActive = item.subItems?.some(subItem => pathname === subItem.href);
                const isOpen = openSubMenus[item.name] || false;

                // Check if this item has sub-items
                if (item.subItems) {
                  return (
                    <li key={item.href || item.name}>
                      <button
                        type="button"
                        onClick={() => toggleSubMenu(item.name)}
                        className={`flex w-full items-center justify-between rounded-lg px-4 py-3 text-sm font-medium transition-colors ${
                          isSubActive
                            ? 'bg-blue-100 text-blue-700'
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <div className="flex items-center">
                          <span className="mr-3">{item.icon}</span>
                          <span>{item.name}</span>
                        </div>
                        <svg
                          className={`size-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                      </button>

                      {/* Sub-menu */}
                      {isOpen && (
                        <ul className="mt-2 space-y-1 pl-6">
                          {item.subItems.map((subItem) => {
                            const isSubItemActive = pathname === subItem.href;
                            return (
                              <li key={subItem.href}>
                                <Link
                                  href={subItem.href}
                                  className={`flex items-center rounded-lg px-4 py-2 text-sm font-medium transition-colors ${
                                    isSubItemActive
                                      ? 'bg-blue-50 text-blue-700'
                                      : 'text-gray-600 hover:bg-gray-50'
                                  }`}
                                >
                                  <span>{subItem.name}</span>
                                  {isSubItemActive && (
                                    <span className="ml-auto size-2 rounded-full bg-blue-500"></span>
                                  )}
                                </Link>
                              </li>
                            );
                          })}
                        </ul>
                      )}
                    </li>
                  );
                }

                // Regular menu item without sub-items
                return (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className={`flex items-center rounded-lg px-4 py-3 text-sm font-medium transition-colors ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <span className="mr-3">{item.icon}</span>
                      <span>{item.name}</span>
                      {isActive && (
                        <span className="ml-auto size-2 rounded-full bg-blue-500"></span>
                      )}
                    </Link>
                  </li>
                );
              })}
            </ul>
          </nav>

          {/* Sidebar footer */}
          <div className="border-t p-4">
            <button
              type="button"
              onClick={async () => {
                try {
                  const result = await authService.logout();
                  if (result.success) {
                    router.push('/sign-in');
                  } else {
                    toast.error(result.error);
                  }
                } catch (error) {
                  console.error('Logout error:', error);
                }
              }}
              className="flex w-full items-center rounded-lg px-4 py-2 text-sm font-medium text-red-600 hover:bg-red-50"
            >
              <svg className="mr-3 size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
              </svg>
              <span>{t('logout')}</span>
            </button>
          </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
