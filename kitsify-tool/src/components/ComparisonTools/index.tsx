import { useTranslations } from 'next-intl';
import React from 'react';

// Reusable check and cross icons
const CheckIcon: React.FC = () => (
  <span className="flex size-6 items-center justify-center text-green-500">
    <svg className="size-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
    </svg>
  </span>
);

const CrossIcon: React.FC = () => (
  <span className="flex size-6 items-center justify-center text-red-500">
    <svg className="size-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M6 18L18 6M6 6l12 12"></path>
    </svg>
  </span>
);

const ComparisionTools: React.FC = () => {
  const t = useTranslations('Index');

  const featuresData = [
    {
      feature: 'feature1',
      ecom: true,
      other: false,
    },
    {
      feature: 'feature2',
      ecom: true,
      other: false,
    },
    {
      feature: 'feature3',
      ecom: true,
      other: false,
    },
    {
      feature: 'feature4',
      ecom: true,
      other: false,
    },
    {
      feature: 'feature5',
      ecom: true,
      other: false,
    },
  ];

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      <div className="flex flex-col px-5 md:px-0">
        <h1 className="mb-6 text-center text-4xl font-bold uppercase">
          {t('COMPARISON_TOOLS.KITSIFY_VS_OTHERS')}
        </h1>
        <p className="mb-8 text-xl">
          {t('COMPARISON_TOOLS.UNDERSTAND_PURCHASE')}
        </p>
        <div className="mb-5 flex flex-col">
          <div className="mb-3 flex items-center">
            <div className="flex size-8 items-center justify-center rounded-full bg-green-400 p-2 dark:bg-green-500">
              <svg
                aria-hidden="true"
                className="size-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                >
                </path>
              </svg>
            </div>
            <span className="ml-3 text-lg font-semibold">
              {t('COMPARISON_TOOLS.QUICK_ACCESS')}
            </span>
          </div>
          <div className="mb-3 flex items-center">
            <div className="flex size-8 items-center justify-center rounded-full bg-green-400 p-2 dark:bg-green-500">
              <svg
                aria-hidden="true"
                className="size-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                >
                </path>
              </svg>
            </div>
            <span className="ml-3 text-lg font-semibold">
              {t('COMPARISON_TOOLS.AHEAD_OF_COMPETITORS')}
            </span>
          </div>
          <div className="mb-3 flex items-center">
            <div className="flex size-8 items-center justify-center rounded-full bg-green-400 p-2 dark:bg-green-500">
              <svg
                aria-hidden="true"
                className="size-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                >
                </path>
              </svg>
            </div>
            <span className="ml-3 text-lg font-semibold">
              {t('COMPARISON_TOOLS.ALL_IN_ONE')}
            </span>
          </div>
        </div>
        <button
          type="button"
          className="rounded-full border border-gray-300 bg-white px-5 py-2.5 font-medium hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100 md:max-w-80 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:hover:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
        >
          {t('COMPARISON_TOOLS.GET_ACCESS')}
        </button>
      </div>
      <div className="mt-5 w-full">
        <div className="mx-auto mt-7 overflow-hidden rounded-lg shadow-lg">
          <div className="grid grid-cols-4">
            {/* Column Headers */}
            <div className="col-span-2 rounded-tl-lg border-t border-gray-100"></div>
            <div className="border-t border-gray-100 py-4 text-center">
              <span className="text-lg font-bold text-gray-800">{t('COMPARISON_TOOLS.ANOTHER_TOOL')}</span>
            </div>
            <div className="rounded-t-lg border-2 border-green-500 py-4 text-center">
              <span className="text-lg font-bold text-gray-800">Kitsify</span>
            </div>

            {/* Feature Rows */}
            {featuresData.map((item, index) => (
              <React.Fragment key={item.feature}>
                <div className="col-span-2 border-b border-gray-200 bg-gray-50 px-6 py-4">
                  <p className="font-medium text-gray-800">
                    {/* eslint-disable-next-line ts/ban-ts-comment */}
                    {/* @ts-expect-error */}
                    {t(`COMPARISON_TOOLS.FEATURES_DATA.${item.feature}`)}
                  </p>
                </div>
                <div className="flex items-center justify-center border-b border-gray-200 bg-gray-50 px-6 py-3">
                  {item.other ? <CheckIcon /> : <CrossIcon />}
                </div>
                <div className={`flex items-center justify-center border-x-2 border-b-2 border-green-500 px-6 py-3 ${index < featuresData.length - 1 ? '' : 'rounded-b-lg'}`}>
                  {item.ecom ? <CheckIcon /> : <CrossIcon />}
                </div>
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComparisionTools;
