import { useTranslations } from 'next-intl';
import React, { useEffect, useState } from 'react';

type NotificationProps = {
  isOpen: boolean;
  onClose: () => void;
};

export default function Notification({
  isOpen,
  onClose,
}: NotificationProps) {
  const [isVisible, setIsVisible] = useState(false);
  const t = useTranslations('Notification');

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      // Restore body scroll when modal is closed
      document.body.style.overflow = 'unset';
    }

    // Cleanup function to restore scroll when component unmounts
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key press
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, onClose]);

  if (!isOpen) {
    return null;
  }

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm transition-all duration-300 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
    >
      <div
        className={`mx-4 w-full max-w-lg overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 ${
          isVisible ? 'translate-y-0 scale-100' : 'translate-y-4 scale-95'
        }`}
        role="dialog"
        aria-modal="true"
      >
        {/* Header with success icon */}
        <div className="bg-gradient-to-r from-green-500 to-emerald-600 px-8 py-6 text-white">
          <div className="flex items-center">
            <div className="mr-2 flex size-12 items-center justify-center rounded-full bg-white/20 backdrop-blur-sm">
              <svg
                className="size-7 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2.5"
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-bold leading-tight">
                {t('payment_success_title')}
              </h3>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white px-8 py-6">
          <div className="space-y-4">
            {/* Success message */}
            <p className="text-base leading-relaxed text-gray-700">
              {t('payment_success_subtitle')}
            </p>

            {/* Email instructions section */}
            <div className="rounded-r-lg border-l-4 border-blue-400 bg-blue-50 p-4">
              <h4 className="mb-2 font-semibold text-blue-900">
                {t('email_check_title')}
              </h4>
              <p className="mb-2 text-sm leading-relaxed text-blue-800">
                {t('email_check_description')}
              </p>
              <p className="text-sm leading-relaxed text-blue-800">
                {t('email_confirmation')}
              </p>
            </div>

            {/* Support message */}
            <div className="rounded-r-lg border-l-4 border-amber-400 bg-amber-50 p-4">
              <p className="mb-2 text-sm leading-relaxed text-amber-800">
                {t('email_support')}
              </p>
              <p className="text-sm font-medium text-amber-900">
                {t('support_message')}
              </p>
            </div>
          </div>

          {/* Close button */}
          <div className="mt-6 flex justify-end">
            <button
              type="button"
              className="rounded-lg bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-3 text-sm font-semibold text-white transition-all duration-200 hover:scale-105 hover:from-green-600 hover:to-emerald-700 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              onClick={onClose}
            >
              {t('close_button')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
