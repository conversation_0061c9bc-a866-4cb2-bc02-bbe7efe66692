'use client';

import { useRouter } from 'next/navigation';

import Button from './Button';
import Heading from './Heading';

type EmptyStateProps = {
  title?: string;
  subtitle?: string;
  showReset?: boolean;
  label?: string;
  reset?: () => void;
};

const EmptyState: React.FC<EmptyStateProps> = ({
  title = 'No exact matches',
  subtitle = 'Try changing or removing some of your filters.',
  label = 'Remove all filters',
  showReset,
  reset,
}) => {
  const router = useRouter();

  return (
    <div
      className="
        flex
        h-[60vh]
        flex-col
        items-center
        justify-center
        gap-2
      "
    >
      <Heading center title={title} subtitle={subtitle} />
      <div
        className="mt-4 flex w-48
        flex-row
        gap-2 "
      >
        {showReset && (
          <Button
            outline
            label={label ?? 'Remove all filters'}
            onClick={() => reset && reset()}
          />
        )}
        <Button outline label="Go Back" onClick={() => router.push('/')} />
      </div>
    </div>
  );
};

export default EmptyState;
