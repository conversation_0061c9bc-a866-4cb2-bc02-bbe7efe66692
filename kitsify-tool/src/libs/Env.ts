// Environment variables configuration
// This is the single source of truth for all environment variables in the application

// Type definitions for better type safety
type NodeEnv = 'development' | 'production' | 'test';

// Environment variables with getter functions to ensure runtime evaluation
export const Env = {
  // Server environment variables (only available on server-side)
  server: {
    get DATABASE_URL() {
      return process.env.DATABASE_URL || '';
    },
    get LOGTAIL_SOURCE_TOKEN() {
      return process.env.LOGTAIL_SOURCE_TOKEN || '';
    },
  },

  // Client environment variables (available on both client and server)
  client: {
    get NEXT_PUBLIC_APP_URL() {
      return process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001';
    },
    get NEXT_PUBLIC_API_URL() {
      return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    },
    get NEXT_PUBLIC_PAYPAL_CLIENT_ID() {
      return process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID || '';
    },
    get NEXT_PUBLIC_EXTENSION_ID() {
      return process.env.NEXT_PUBLIC_EXTENSION_ID || '';
    },
  },

  // Shared environment variables
  shared: {
    get NODE_ENV() {
      return (process.env.NODE_ENV as NodeEnv) || 'development';
    },
    get VERCEL_ENV() {
      return process.env.VERCEL_ENV || '';
    },
    get VERCEL_URL() {
      return process.env.VERCEL_URL || '';
    },
    get VERCEL_PROJECT_PRODUCTION_URL() {
      return process.env.VERCEL_PROJECT_PRODUCTION_URL || '';
    },
  },
};

// Convenience functions for commonly used environment variables
export const getApiUrl = (): string => Env.client.NEXT_PUBLIC_API_URL;
export const getAppUrl = (): string => Env.client.NEXT_PUBLIC_APP_URL;
export const getPaypalClientId = (): string => Env.client.NEXT_PUBLIC_PAYPAL_CLIENT_ID;
export const getExtensionId = (): string => Env.client.NEXT_PUBLIC_EXTENSION_ID;
export const isProduction = (): boolean => Env.shared.NODE_ENV === 'production';
export const isDevelopment = (): boolean => Env.shared.NODE_ENV === 'development';

// Enhanced getBaseUrl function (moved from Helpers.ts for consistency)
export const getBaseUrl = (): string => {
  if (Env.client.NEXT_PUBLIC_APP_URL) {
    return Env.client.NEXT_PUBLIC_APP_URL;
  }

  if (
    Env.shared.VERCEL_ENV === 'production'
    && Env.shared.VERCEL_PROJECT_PRODUCTION_URL
  ) {
    return `https://${Env.shared.VERCEL_PROJECT_PRODUCTION_URL}`;
  }

  if (Env.shared.VERCEL_URL) {
    return `https://${Env.shared.VERCEL_URL}`;
  }

  return 'http://localhost:3000';
};
