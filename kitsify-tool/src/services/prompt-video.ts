import { api } from './api';

export type PromptVideoData = {
  location?: string;
  time?: string;
  weather?: string;
  lightingDetails?: string;
  characters?: any[];
  visualStyle?: string[];
  lightingStyle?: string[];
  dominantColors?: string[];
  postProcessingEffects?: string[];
  motionEffects?: string[];
  creativeEffects?: string[];
  surrealElements?: string;
  composition?: string[];
  cameraAngle?: string[];
  cameraMotion?: string[];
  focusPoint?: string[];
  editingPace?: string[];
  transitions?: string[];
  mood?: string[];
  backgroundMusic?: string[];
  soundEffects?: string[];
  typography?: string;
  subtitleInstructions?: string[];
  resolution?: string[];
  aspectRatio?: string[];
  duration?: string[];
};

export type GeneratePromptVideoParams = {
  prompt_data: PromptVideoData;
  model?: string;
};

export type GeneratePromptVideoResponse = {
  result: string;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
};

export type PromptVideoHistory = {
  id: number;
  user_id: number;
  prompt_data: PromptVideoData;
  generated_result: string;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  created_at: string;
};

export type PromptVideoHistoriesResponse = {
  data: PromptVideoHistory[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
};

export type QueryPromptVideoHistoriesParams = {
  page?: number;
  pageSize?: number;
  search_text?: string;
};

class PromptVideoService {
  async generatePromptVideo(
    params: GeneratePromptVideoParams,
  ): Promise<GeneratePromptVideoResponse | null> {
    try {
      const response = await api.post('/prompt-video/generate', params);
      return response.data;
    } catch (error) {
      console.error('Error generating video prompt:', error);
      return null;
    }
  }

  async getPromptVideoHistories(
    params: QueryPromptVideoHistoriesParams = {},
  ): Promise<PromptVideoHistoriesResponse> {
    try {
      const searchParams = new URLSearchParams();

      if (params.page) {
        searchParams.append('page', params.page.toString());
      }
      if (params.pageSize) {
        searchParams.append('pageSize', params.pageSize.toString());
      }
      if (params.search_text) {
        searchParams.append('search_text', params.search_text);
      }

      const response = await api.get(`/prompt-video-histories?${searchParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching prompt video histories:', error);
      return {
        data: [],
        pagination: {
          page: params.page || 1,
          pageSize: params.pageSize || 20,
          total: 0,
          totalPages: 0,
        },
      };
    }
  }

  async getPromptVideoHistoryById(id: number): Promise<PromptVideoHistory | null> {
    try {
      const response = await api.get(`/prompt-video-histories/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching prompt video history with ID ${id}:`, error);
      return null;
    }
  }

  async deletePromptVideoHistory(id: number): Promise<boolean> {
    try {
      await api.delete(`/prompt-video-histories/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting prompt video history with ID ${id}:`, error);
      return false;
    }
  }
}

export const promptVideoService = new PromptVideoService();
