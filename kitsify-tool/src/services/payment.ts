import { getPaypalClientId, isProduction } from '@/libs/Env';
import { api } from './api';
import { authService } from './auth';

declare global {
  // eslint-disable-next-line ts/consistent-type-definitions
  interface Window {
    paypal: any;
  }
}

type PaymentResponse = {
  success: boolean;
  transactionId?: string;
  error?: string;
  orderId?: string;
  paymentId?: number;
  packageId?: number;
  durationId?: number;
};

class PaymentService {
  private paypalClientId = getPaypalClientId();

  async initializePayPal(): Promise<void> {
    if (typeof window !== 'undefined' && !window.paypal) {
      const script = document.createElement('script');

      // Use sandbox URL for development and live URL for production
      const isProd = isProduction();
      const paypalDomain = isProd ? 'www.paypal.com' : 'www.sandbox.paypal.com';

      script.src = `https://${paypalDomain}/sdk/js?client-id=${this.paypalClientId}&currency=USD&intent=capture`;

      // Only set data-sandbox attribute in non-production environments
      if (!isProd) {
        script.setAttribute('data-sandbox', 'true');
      }

      script.async = true;
      document.body.appendChild(script);
    }
  }

  async createOrder(durationId: number): Promise<PaymentResponse> {
    try {
      // The backend API expects package_duration_id
      const payload = {
        package_duration_id: durationId,
      };

      const response = await api.post(
        `/payments/create-order`,
        payload,
      );

      const data = response.data;
      return {
        success: true,
        orderId: data.orderId,
        paymentId: data.paymentId,
        packageId: data.packageId,
        durationId: data.durationId,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create order',
      };
    }
  }

  async capturePayment(orderId: string, paymentId: number): Promise<PaymentResponse> {
    try {
      const response = await api.post(
        `/payments/capture/${orderId}/${paymentId}`,
        {},
      );

      const data = response.data;
      return {
        success: true,
        transactionId: data.transactionId,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to capture payment',
      };
    }
  }

  async processPayment(durationId: number): Promise<PaymentResponse> {
    try {
      const authStatus = await authService.checkAuthStatus();
      if (!authStatus) {
        throw new Error('User must be authenticated to make a payment');
      }

      // We need to ensure durationId is provided
      if (!durationId) {
        throw new Error('Duration ID is required for payment processing');
      }
      const orderResult = await this.createOrder(durationId);
      if (!orderResult.success) {
        return orderResult;
      }

      return new Promise((resolve) => {
        window.paypal.Buttons({
          createOrder: () => {
            return orderResult.orderId;
          },
          onApprove: async () => {
            const captureResult = await this.capturePayment(
              orderResult.orderId!,
              orderResult.paymentId!,
            );
            resolve(captureResult);
          },
          onError: () => {
            resolve({
              success: false,
              error: 'Payment failed. Please try again.',
            });
          },
        }).render('#paypal-button-container');
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment processing failed',
      };
    }
  }
}

export const paymentService = new PaymentService();
