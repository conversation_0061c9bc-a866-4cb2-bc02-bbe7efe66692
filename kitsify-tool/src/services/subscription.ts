import { getPaypalClientId, isProduction } from '@/libs/Env';
import { api } from './api';
import { authService } from './auth';

declare global {
  // eslint-disable-next-line ts/consistent-type-definitions
  interface Window {
    paypal: any;
  }
}

type SubscriptionResponse = {
  success: boolean;
  subscriptionId?: number;
  paypalSubscriptionId?: string;
  approvalUrl?: string;
  error?: string;
  planId?: string;
  amount?: number;
  packageId?: number;
  durationId?: number;
};

type SubscriptionStatus = {
  id: number;
  paypal_subscription_id: string;
  paypal_plan_id: string;
  status: string;
  start_date: string;
  next_billing_date?: string;
  amount: number;
  currency: string;
  packageDuration?: {
    id: number;
    duration_days: number;
    price: number;
    package: {
      id: number;
      name: string;
      description: string;
    };
  };
};

class SubscriptionService {
  private paypalClientId = getPaypalClientId();

  async initializePayPal(): Promise<void> {
    if (typeof window !== 'undefined' && !window.paypal) {
      const script = document.createElement('script');

      // Use sandbox URL for development and live URL for production
      const isProd = isProduction();
      const paypalDomain = !isProd ? 'www.paypal.com' : 'www.sandbox.paypal.com';

      script.src = `https://${paypalDomain}/sdk/js?client-id=${this.paypalClientId}&vault=true&intent=subscription`;

      script.async = true;
      document.body.appendChild(script);
    }
  }

  async createSubscription(durationId: number): Promise<SubscriptionResponse> {
    try {
      const authStatus = await authService.checkAuthStatus();
      if (!authStatus) {
        throw new Error('User must be authenticated to create a subscription');
      }

      const payload = {
        package_duration_id: durationId,
        return_url: `${window.location.origin}/subscription/success`,
        cancel_url: `${window.location.origin}/subscription/cancel`,
      };

      const response = await api.post('/subscriptions/create', payload);

      const data = response.data;
      return {
        success: true,
        subscriptionId: data.subscriptionId,
        paypalSubscriptionId: data.paypalSubscriptionId,
        approvalUrl: data.approvalUrl,
        planId: data.planId,
        amount: data.amount,
        packageId: data.packageId,
        durationId: data.durationId,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create subscription',
      };
    }
  }

  async activateSubscription(paypalSubscriptionId: string): Promise<SubscriptionResponse> {
    try {
      const response = await api.post(`/subscriptions/activate/${paypalSubscriptionId}`);

      return {
        success: response.data.success,
        subscriptionId: response.data.subscriptionId,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to activate subscription',
      };
    }
  }

  async cancelSubscription(subscriptionId: number, reason?: string): Promise<SubscriptionResponse> {
    try {
      const payload = reason ? { reason } : {};
      const response = await api.post(`/subscriptions/cancel/${subscriptionId}`, payload);

      return {
        success: response.data.success,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to cancel subscription',
      };
    }
  }

  async getUserSubscriptions(): Promise<SubscriptionStatus[]> {
    try {
      const response = await api.get('/subscriptions/user');
      return response.data;
    } catch (error) {
      console.error('Failed to get user subscriptions:', error);
      return [];
    }
  }

  async getSubscription(subscriptionId: number): Promise<SubscriptionStatus | null> {
    try {
      const response = await api.get(`/subscriptions/${subscriptionId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get subscription:', error);
      return null;
    }
  }

  async processSubscription(durationId: number): Promise<SubscriptionResponse> {
    try {
      const authStatus = await authService.checkAuthStatus();
      if (!authStatus) {
        throw new Error('User must be authenticated to create a subscription');
      }

      if (!durationId) {
        throw new Error('Duration ID is required for subscription processing');
      }

      const subscriptionResult = await this.createSubscription(durationId);
      if (!subscriptionResult.success) {
        return subscriptionResult;
      }

      return new Promise((resolve) => {
        window.paypal.Buttons({
          style: {
            shape: 'rect',
            color: 'gold',
            layout: 'vertical',
            label: 'subscribe',
          },
          createSubscription: () => {
            return subscriptionResult.paypalSubscriptionId;
          },
          onApprove: async (data: { subscriptionID: string }) => {
            try {
              const activateResult = await this.activateSubscription(data.subscriptionID);
              resolve(activateResult);
            // eslint-disable-next-line unused-imports/no-unused-vars
            } catch (error) {
              resolve({
                success: false,
                error: 'Failed to activate subscription',
              });
            }
          },
          onError: (err: Error) => {
            console.error('PayPal subscription error:', err);
            resolve({
              success: false,
              error: 'Subscription failed. Please try again.',
            });
          },
          onCancel: () => {
            resolve({
              success: false,
              error: 'Subscription was cancelled by user.',
            });
          },
        }).render('#paypal-subscription-container');
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Subscription processing failed',
      };
    }
  }

  formatCurrency(amount: number, currency = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount);
  }

  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'active':
        return 'text-green-600';
      case 'cancelled':
        return 'text-red-600';
      case 'suspended':
        return 'text-yellow-600';
      case 'expired':
        return 'text-gray-600';
      case 'pending':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  }

  getStatusText(status: string): string {
    switch (status.toLowerCase()) {
      case 'active':
        return 'Active';
      case 'cancelled':
        return 'Cancelled';
      case 'suspended':
        return 'Suspended';
      case 'expired':
        return 'Expired';
      case 'pending':
        return 'Pending';
      default:
        return status;
    }
  }
}

export const subscriptionService = new SubscriptionService();
