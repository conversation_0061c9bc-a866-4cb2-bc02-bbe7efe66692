import { api } from './api';

export type Category = {
  id: number;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  img_url?: string;
  sort: number;
  is_hot?: boolean;
  created_at: string;
  updated_at: string;
};

export const categoriesService = {
  async getCategories(): Promise<Category[]> {
    const response = await api.get('/products/categories');
    return response.data; // ProductsController returns simple array
  },
};
