import axios from 'axios';
import { getApiUrl } from '@/libs/Env';

// Create an axios instance with default config
export const api = axios.create({
  baseURL: getApiUrl(),
  withCredentials: true,
});

// Add response interceptor to handle token refresh
api.interceptors.response.use(
  response => response,
  async (error) => {
    const originalRequest = error.config;

    // Only attempt to refresh token if:
    // 1. The response status is 401
    // 2. The request hasn't been retried yet
    // 3. The request is not to /auth/status or /auth/refresh endpoints (to prevent infinite loops)
    if (
      error.response?.status === 401
      && !originalRequest._retry
      && !originalRequest.url?.includes('/auth/status')
      && !originalRequest.url?.includes('/auth/refresh')
    ) {
      originalRequest._retry = true;

      try {
        await api.post('/auth/refresh');
        return api(originalRequest);
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  },
);
