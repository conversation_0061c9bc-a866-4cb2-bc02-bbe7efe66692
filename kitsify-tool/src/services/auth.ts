import { getApiUrl } from '@/libs/Env';
import { api } from './api';

export const authService = {
  login: async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await api.post('/login', { email, password });

      if (response.data.success) {
        // If login successful, notify extension
        notifyExtension('syncLogin');
        return { success: true };
      }

      return {
        success: false,
        error: 'Invalid credentials',
      };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Lo<PERSON> failed',
      };
    }
  },

  loginWithGoogle: (returnUrl?: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const encodedReturnUrl = returnUrl ? `?return_url=${encodeURIComponent(returnUrl)}` : '';
      const authUrl = `${getApiUrl()}/auth/google${encodedReturnUrl}`;

      // Use a direct window location change instead of window.open for the popup
      const authWindow = window.open('about:blank', '_blank', 'width=600,height=800');

      if (authWindow) {
        // Only set location after window is created to avoid preload issues
        authWindow.location.href = authUrl;
      }

      // Set up message event listener to receive auth result
      const messageHandler = async (event: MessageEvent) => {
        // Check if this is our auth message regardless of origin in production
        if (event.data?.type === 'AUTH_SUCCESS') {
          // Remove the event listener
          window.removeEventListener('message', messageHandler);

          // Resolve the promise with success
          resolve(true);
        } else if (event.data?.type === 'AUTH_FAILED') {
          // Remove the event listener
          window.removeEventListener('message', messageHandler);

          // Resolve the promise with failure
          resolve(false);
        }
      };

      // Add the message event listener
      window.addEventListener('message', messageHandler);

      // Set a timeout to clean up if we don't get a response
      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        if (authWindow && !authWindow.closed) {
          authWindow.close();
        }
        resolve(false);
      }, 300000); // 5 minutes timeout
    });
  },

  loginWithDiscord: (returnUrl?: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const encodedReturnUrl = returnUrl ? `?return_url=${encodeURIComponent(returnUrl)}` : '';
      const authUrl = `${getApiUrl()}/auth/discord${encodedReturnUrl}`;

      // Use a direct window location change instead of window.open for the popup
      // This approach avoids the preload resource warning
      const authWindow = window.open('about:blank', '_blank', 'width=600,height=800');

      if (authWindow) {
        // Only set location after window is created to avoid preload issues
        authWindow.location.href = authUrl;
      }

      // Set up message event listener to receive auth result
      const messageHandler = async (event: MessageEvent) => {
        // Check if this is our auth message regardless of origin in production
        // This is necessary because the message might come from kitsify.com while API_URL is admin.kitsify.com
        if (event.data?.type === 'AUTH_SUCCESS') {
          // Remove the event listener
          window.removeEventListener('message', messageHandler);

          // Resolve the promise with success
          resolve(true);
        } else if (event.data?.type === 'AUTH_FAILED') {
          // Remove the event listener
          window.removeEventListener('message', messageHandler);

          // Resolve the promise with failure
          resolve(false);
        }
      };

      // Add the message event listener
      window.addEventListener('message', messageHandler);

      // Set a timeout to clean up if we don't get a response
      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        if (authWindow && !authWindow.closed) {
          authWindow.close();
        }
        resolve(false);
      }, 300000); // 5 minutes timeout
    });
  },

  checkAuthStatus: async () => {
    try {
      const response = await api.get('/auth/status');

      return response.data.isAuthenticated;
    } catch (error) {
      console.error('Error checking auth status:', error);
      return false;
    }
  },

  getUserStatus: async () => {
    try {
      const response = await api.get('/auth/status');

      return {
        isAuthenticated: response.data.isAuthenticated,
        user: response.data.user,
      };
    } catch (error) {
      console.error('Error getting user status:', error);
      return {
        isAuthenticated: false,
        user: null,
      };
    }
  },

  refreshToken: async () => {
    try {
      await api.post('/auth/refresh');
      return true;
    } catch (error) {
      console.error('Error refreshing token:', error);
      return false;
    }
  },

  logout: async (): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await api.post('/logout');

      if (response.data.success) {
        // If logout successful, notify extension
        notifyExtension('syncLogout');
        return { success: true };
      }

      return {
        success: false,
        error: 'Logout failed',
      };
    } catch (error) {
      console.error('Logout error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Logout failed',
      };
    }
  },

  // Called after successful login to notify extension
  notifyExtensionAfterLogin: () => {
    notifyExtension('syncLogin');
  },

  // Called to force inject cookies for a specific domain
  forceInjectForDomain: (domain: string) => {
    notifyExtensionForDomain('forceInjectForDomain', domain);
  },
};

// Function to notify the extension about authentication changes
function notifyExtension(action: 'syncLogin' | 'syncLogout'): void {
  try {
    if (typeof window !== 'undefined') {
      // Check if extension is ready first
      const checkExtensionReady = () => {
        return new Promise<boolean>((resolve) => {
          const readyHandler = (event: MessageEvent) => {
            if (event.data?.type === 'KITSIFY_EXTENSION_READY') {
              window.removeEventListener('message', readyHandler);
              resolve(event.data.connected === true);
            }
          };

          window.addEventListener('message', readyHandler);

          // Trigger extension ready check
          window.postMessage({ type: 'KITSIFY_CHECK_EXTENSION' }, window.location.origin);

          // Timeout after 2 seconds
          setTimeout(() => {
            window.removeEventListener('message', readyHandler);
            resolve(false);
          }, 2000);
        });
      };

      // Wait for extension to be ready, then send auth sync
      checkExtensionReady().then((isReady) => {
        if (!isReady) {
          console.warn('Extension not ready, sending auth sync anyway');
        }

        window.postMessage(
          { type: 'KITSIFY_AUTH_SYNC', action },
          window.location.origin,
        );

        // Listen for response from extension
        const responseHandler = (event: MessageEvent) => {
          if (event.data?.type === 'KITSIFY_AUTH_SYNC_RESPONSE') {
            // Remove the event listener after receiving response
            window.removeEventListener('message', responseHandler);
          }
        };

        window.addEventListener('message', responseHandler);

        // Set a timeout to clean up the listener if no response is received
        setTimeout(() => {
          window.removeEventListener('message', responseHandler);
          console.warn('Extension auth sync response timeout');
        }, 5000); // 5 second timeout
      });
    } else {
      console.error('Window object not available');
    }
  } catch (error) {
    console.error('Error notifying extension:', error);
  }
}

// Function to notify the extension about domain-specific actions
function notifyExtensionForDomain(action: string, domain: string): void {
  try {
    if (typeof window !== 'undefined') {
      window.postMessage(
        { type: 'KITSIFY_DOMAIN_ACTION', action, domain },
        window.location.origin,
      );
    } else {
      console.error('Window object not available');
    }
  } catch (error) {
    console.error('Error notifying extension for domain:', error);
  }
}
