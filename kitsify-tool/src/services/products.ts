import { api } from './api';

export type ProductDuration = {
  id: number;
  product_id: number;
  duration_days: number;
  original_price: number;
  discount_price: number;
  quantity: number;
  discount_percent: number;
};

export type PaymentMethod = {
  id: number;
  name: string;
  description?: string;
};

export type ProductDiscount = {
  id: number;
  product_id: number;
  payment_method_id: number;
  discount_percent: number;
  paymentMethod: PaymentMethod;
};

export type Product = {
  id: number;
  name: string;
  description?: string;
  image_url?: string;
  features: string[];
  durations: ProductDuration[];
  productDiscounts?: ProductDiscount[];
  created_at: Date;
  updated_at: Date;
  category_id: number;
  price: number;
  quantity: number;
  discount_percent: number;
  discount_price: number;
  sold_out: boolean;
  original_price: number;
  is_hot?: boolean;
};

export type ProductUser = {
  id: number;
  user_id: number;
  product_id: number;
  start_date: string;
  expires_at: string;
  status: string;
  created_at: string;
  updated_at: string;
  product: Product;
  user?: any;
};

export type UserProductsResponse = {
  data: ProductUser[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
};

export type ProductQueryParams = {
  page?: number;
  limit?: number;
  search?: string;
  categories?: string[];
  inStock?: boolean;
};

export type ProductsResponse = {
  data: Product[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
};

class ProductService {
  async fetchUserProducts(): Promise<UserProductsResponse | null> {
    try {
      const response = await api.get('/product-users/my');
      return response.data;
    } catch (error) {
      console.error('Error fetching user products:', error);
      return null;
    }
  }

  async getProduct(id: number): Promise<Product | null> {
    try {
      const response = await api.get(`/products/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product with ID ${id}:`, error);
      return null;
    }
  }

  async getProductWithDiscounts(id: number): Promise<Product | null> {
    try {
      const response = await api.get(`/products/${id}/with-discounts`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product with discounts for ID ${id}:`, error);
      return null;
    }
  }

  /**
   * Fetch products with optional filtering
   */
  async getProducts(params: ProductQueryParams = {}): Promise<ProductsResponse> {
    try {
      const { page = 1, limit = 12, search, categories, inStock } = params;

      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      if (search) {
        queryParams.append('search', search);
      }

      if (categories && categories.length > 0) {
        queryParams.append('categories', categories.join(','));
      }

      if (inStock !== undefined) {
        queryParams.append('inStock', inStock.toString());
      }

      const response = await api.get(`/products?${queryParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  }

  /**
   * Fetch product durations by product ID
   */
  async getProductDurations(productId: number): Promise<ProductDuration[]> {
    try {
      const response = await api.get(`/products/${productId}/durations`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product durations for product ${productId}:`, error);
      throw error;
    }
  }

  /**
   * Fetch a single product duration by ID
   */
  async getProductDuration(durationId: number): Promise<ProductDuration> {
    try {
      const response = await api.get(`/products/durations/${durationId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product duration ${durationId}:`, error);
      throw error;
    }
  }
}

export const productService = new ProductService();
