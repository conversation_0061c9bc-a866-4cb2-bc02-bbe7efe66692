import type { Product } from './products';
import { api } from './api';

export type OrderItem = {
  id: number;
  orderId: number;
  productId: number;
  product?: Product;
  quantity: number;
  price: number;
  createdAt: Date;
  updatedAt: Date;
};

export type Order = {
  id: number;
  userId: number;
  status: string;
  totalAmount: number;
  paypalOrderId?: string;
  items: OrderItem[];
  createdAt: Date;
  updatedAt: Date;
};

export type CreateOrderItemDto = {
  product_id: number;
  quantity: number;
};

export type CreateOrderDto = {
  items: CreateOrderItemDto[];
};

export type CheckoutResponse = {
  orderId: number;
  paypalOrderId: string;
  approvalLink: string;
  totalAmount: number;
};

export type CapturePaymentResponse = {
  status: string;
  orderId: number;
  paypalOrderId: string;
};

export const ordersService = {
  /**
   * Create a new order and initiate checkout
   */
  checkout: async (orderData: CreateOrderDto): Promise<CheckoutResponse> => {
    try {
      const response = await api.post('/orders/checkout', orderData);
      return response.data;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  },

  /**
   * Get all orders for the current user
   */
  getUserOrders: async (): Promise<Order[]> => {
    try {
      const response = await api.get('/orders');
      return response.data;
    } catch (error) {
      console.error('Error fetching user orders:', error);
      throw error;
    }
  },

  /**
   * Get a specific order by ID
   */
  getOrder: async (id: number): Promise<Order> => {
    try {
      const response = await api.get(`/orders/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching order ${id}:`, error);
      throw error;
    }
  },

  /**
   * Capture payment for an order after PayPal approval
   */
  capturePayment: async (orderId: number): Promise<CapturePaymentResponse> => {
    try {
      const response = await api.post(`/orders/capture/${orderId}`);
      return response.data;
    } catch (error) {
      console.error(`Error capturing payment for order ${orderId}:`, error);
      throw error;
    }
  },
};
