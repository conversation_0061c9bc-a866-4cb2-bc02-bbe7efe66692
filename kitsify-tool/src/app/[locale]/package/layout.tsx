import { getTranslations, setRequestLocale } from 'next-intl/server';
import AuthenticatedLayoutWrapper from '@/components/layouts/AuthenticatedLayoutWrapper';

export async function generateMetadata(props: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Package',
  });

  return {
    title: t('meta_title') || 'Package Information',
  };
}

export default async function PackageLayout(props: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  return (
    <AuthenticatedLayoutWrapper>
      {props.children}
    </AuthenticatedLayoutWrapper>
  );
}
