'use client';

import type { PackageUser } from '@/services/packages';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import TryForFreeModal from '@/components/TryForFreeModal';
import { usePackages } from '@/contexts/PackagesContext';

export default function PackagePage() {
  const t = useTranslations('Package');
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Use packages context instead of making API calls
  const { userPackages, loading } = usePackages();

  // Extract packages and products from the response
  const packages = userPackages?.data?.packages || [];
  const products = userPackages?.data?.products || [];

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  // Calculate days remaining
  const calculateDaysRemaining = (expiryDate: string) => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  if (loading) {
    return (
      <div className="flex min-h-[60vh] items-center justify-center">
        <div className="size-12 animate-spin rounded-full border-y-2 border-blue-500"></div>
      </div>
    );
  }

  const hasPackagesOrProducts = packages.length > 0 || products.length > 0;

  return (
    <div className="container mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800">{t('page_title')}</h1>
        <p className="mt-2 text-gray-600">
          Manage your packages and products
        </p>
      </div>

      {!hasPackagesOrProducts
        ? (
            <div className="rounded-lg bg-white p-6 shadow-md">
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <svg
                  className="mb-4 size-16 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                  >
                  </path>
                </svg>
                <h2 className="mb-2 text-xl font-semibold text-gray-700">{t('no_packages')}</h2>
                <p className="mb-6 text-gray-500">
                  {t('no_packages_description')}
                </p>
                <button
                  type="button"
                  onClick={() => {
                    setIsModalOpen(true);
                  }}
                  className="rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  {t('browse_packages')}
                </button>
              </div>
            </div>
          )
        : (
            <>
              {/* Packages Section */}
              {packages.length > 0 && (
                <div className="mb-8">
                  <h2 className="mb-4 text-2xl font-bold text-gray-800">Your Packages</h2>
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {packages.map((pkg: PackageUser) => (
                      <div key={pkg.id} className="overflow-hidden rounded-lg bg-white shadow-md transition-all duration-300 hover:shadow-lg">
                        <div className="bg-blue-600 p-4 text-white">
                          <h3 className="text-xl font-bold">
                            Package #
                            {pkg.id}
                          </h3>
                          <span className="text-sm opacity-75">Package</span>
                        </div>
                        <div className="p-6">
                          <div className="mb-4">
                            <div className="mb-2 flex justify-between">
                              <span className="text-gray-600">
                                {t('status')}
                              </span>
                              <span className={`font-medium ${calculateDaysRemaining(pkg.expires_at) > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {calculateDaysRemaining(pkg.expires_at) > 0 ? t('active') : t('expired')}
                              </span>
                            </div>
                            <div className="mb-2 flex justify-between">
                              <span className="text-gray-600">
                                {t('purchase_date')}
                              </span>
                              <span className="font-medium">{formatDate(pkg.start_date)}</span>
                            </div>
                            <div className="mb-2 flex justify-between">
                              <span className="text-gray-600">
                                {t('expiry_date')}
                              </span>
                              <span className="font-medium">{formatDate(pkg.expires_at)}</span>
                            </div>
                            <div className="mb-2 flex justify-between">
                              <span className="text-gray-600">
                                {t('days_remaining')}
                              </span>
                              <span className="font-medium">
                                {calculateDaysRemaining(pkg.expires_at)}
                                {' '}
                                days
                              </span>
                            </div>

                            {/* Features */}
                            <div className="mb-2">
                              <span className="text-gray-600">Features:</span>
                              <div className="mt-1 flex flex-wrap gap-2">
                                {pkg.has_prompt_library && (
                                  <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                    Prompt Library
                                  </span>
                                )}
                                {pkg.has_prompt_video && (
                                  <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800">
                                    Prompt Video
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Progress bar for days remaining */}
                          <div className="mb-4">
                            <div className="mb-1 flex justify-between text-xs">
                              <span>{t('progress')}</span>
                              <span>
                                {calculateDaysRemaining(pkg.expires_at)}
                                {' '}
                                {t('days_left')}
                              </span>
                            </div>
                            <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                              <div
                                className="h-full rounded-full bg-blue-600"
                                style={{
                                  width: `${Math.min(
                                    (calculateDaysRemaining(pkg.expires_at) / 30) * 100,
                                    100,
                                  )}%`,
                                }}
                              >
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Products Section */}
              {products.length > 0 && (
                <div className="mb-8">
                  <h2 className="mb-4 text-2xl font-bold text-gray-800">Your Products</h2>
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {products.map((product: PackageUser) => (
                      <div key={product.id} className="overflow-hidden rounded-lg bg-white shadow-md transition-all duration-300 hover:shadow-lg">
                        <div className="bg-green-600 p-4 text-white">
                          <h3 className="text-xl font-bold">
                            Product #
                            {product.id}
                          </h3>
                          <span className="text-sm opacity-75">Product</span>
                        </div>
                        <div className="p-6">
                          <div className="mb-4">
                            <div className="mb-2 flex justify-between">
                              <span className="text-gray-600">
                                {t('status')}
                              </span>
                              <span className={`font-medium ${calculateDaysRemaining(product.expires_at) > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {calculateDaysRemaining(product.expires_at) > 0 ? t('active') : t('expired')}
                              </span>
                            </div>
                            <div className="mb-2 flex justify-between">
                              <span className="text-gray-600">
                                {t('purchase_date')}
                              </span>
                              <span className="font-medium">{formatDate(product.start_date)}</span>
                            </div>
                            <div className="mb-2 flex justify-between">
                              <span className="text-gray-600">
                                {t('expiry_date')}
                              </span>
                              <span className="font-medium">{formatDate(product.expires_at)}</span>
                            </div>
                            <div className="mb-2 flex justify-between">
                              <span className="text-gray-600">
                                {t('days_remaining')}
                              </span>
                              <span className="font-medium">
                                {calculateDaysRemaining(product.expires_at)}
                                {' '}
                                days
                              </span>
                            </div>

                            {/* Features */}
                            <div className="mb-2">
                              <span className="text-gray-600">Features:</span>
                              <div className="mt-1 flex flex-wrap gap-2">
                                {product.has_prompt_library && (
                                  <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                    Prompt Library
                                  </span>
                                )}
                                {product.has_prompt_video && (
                                  <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800">
                                    Prompt Video
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Progress bar for days remaining */}
                          <div className="mb-4">
                            <div className="mb-1 flex justify-between text-xs">
                              <span>{t('progress')}</span>
                              <span>
                                {calculateDaysRemaining(product.expires_at)}
                                {' '}
                                {t('days_left')}
                              </span>
                            </div>
                            <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                              <div
                                className="h-full rounded-full bg-green-600"
                                style={{
                                  width: `${Math.min(
                                    (calculateDaysRemaining(product.expires_at) / 30) * 100,
                                    100,
                                  )}%`,
                                }}
                              >
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}

      <TryForFreeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
}
