'use client';

import type { Category } from '@/services/categories';
import type { Product, ProductDuration, ProductsResponse } from '@/services/products';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { Autoplay, Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import ProductCard from '@/components/ProductCard';
import ShopLayout from '@/components/ShopLayout';
import { useCart } from '@/contexts/CartContext';
import { categoriesService } from '@/services/categories';
import { productService } from '@/services/products';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';

function CategoryPageContent() {
  const t = useTranslations('Shop');
  const params = useParams<{ id: string }>();
  const { addToCart } = useCart();
  const categoryId = Number.parseInt(params?.id || '0', 10);

  // State for products and pagination
  const [products, setProducts] = useState<Product[]>([]);
  const [category, setCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [categoryLoading, setCategoryLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [meta, setMeta] = useState<{ total: number; page: number; limit: number; totalPages: number }>({
    total: 0,
    page: 1,
    limit: 12,
    totalPages: 0,
  });

  // Banner images
  const bannerImages = [
    '/assets/images/banner1.png',
    '/assets/images/banner2.png',
  ];

  // Load category
  useEffect(() => {
    const fetchCategory = async () => {
      if (!categoryId) {
        return;
      }

      setCategoryLoading(true);
      try {
        const categories = await categoriesService.getCategories();
        const foundCategory = categories.find(cat => cat.id === categoryId);
        if (foundCategory) {
          setCategory(foundCategory);
        } else {
          setError('Category not found');
        }
      } catch (error) {
        console.error('Error fetching category:', error);
        setError('Failed to load category. Please try again later.');
      } finally {
        setCategoryLoading(false);
      }
    };

    fetchCategory();
  }, [categoryId]);

  // Load products
  useEffect(() => {
    if (!categoryId) {
      return;
    }

    const fetchProducts = async () => {
      setLoading(true);
      try {
        const response: ProductsResponse = await productService.getProducts({
          page: meta.page,
          limit: meta.limit,
          categories: [categoryId.toString()],
        });

        setProducts(response.data);
        setMeta(response.meta);
        setError(null);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [categoryId, meta.page, meta.limit]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setMeta(prev => ({ ...prev, page }));
  };

  // Handle adding product to cart
  const handleAddToCart = (product: Product, quantity: number = 1, selectedDuration: ProductDuration) => {
    addToCart(product, quantity, selectedDuration);
  };

  // Render pagination
  const renderPagination = () => {
    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, meta.page - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(meta.totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          type="button"
          key={i}
          onClick={() => handlePageChange(i)}
          className={`mx-1 rounded px-3 py-1 ${meta.page === i ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
        >
          {i}
        </button>,
      );
    }

    return (
      <div className="mt-8 flex justify-center">
        <button
          type="button"
          onClick={() => handlePageChange(Math.max(1, meta.page - 1))}
          disabled={meta.page === 1}
          className="mx-1 rounded bg-gray-200 px-3 py-1 text-gray-700 hover:bg-gray-300 disabled:cursor-not-allowed disabled:opacity-50"
        >
          &laquo;
        </button>

        {pages}

        <button
          type="button"
          onClick={() => handlePageChange(Math.min(meta.totalPages, meta.page + 1))}
          disabled={meta.page === meta.totalPages}
          className="mx-1 rounded bg-gray-200 px-3 py-1 text-gray-700 hover:bg-gray-300 disabled:cursor-not-allowed disabled:opacity-50"
        >
          &raquo;
        </button>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Banner Section */}
      <div className="mb-10 overflow-hidden rounded-lg shadow-md">
        <Swiper
          modules={[Pagination, Autoplay]}
          pagination={{ clickable: true }}
          autoplay={{ delay: 5000, disableOnInteraction: false }}
          loop
          className="h-[250px] w-full md:h-[650px]"
        >
          {bannerImages.map((image, index) => (
            <SwiperSlide key={index}>
              <div className="relative size-full">
                <Image
                  src={image}
                  alt={`Banner ${index + 1}`}
                  fill
                  className="object-contain md:object-cover"
                  priority={index === 0}
                />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* Category Title */}
      {categoryLoading
        ? (
            <div className="mb-8 flex h-10 items-center justify-center">
              <div className="size-8 animate-spin rounded-full border-y-2 border-blue-500"></div>
            </div>
          )
        : category
          ? (
              <div className="mb-8">
                <h1 className="text-3xl font-bold">{category.name}</h1>
                {category.description && (
                  <p className="mt-2 text-gray-600">{category.description}</p>
                )}
              </div>
            )
          : (
              <div className="mb-8">
                <h1 className="text-3xl font-bold">{t('noProducts') || 'Danh mục không tồn tại'}</h1>
              </div>
            )}

      {/* Products */}
      <div className="mt-8">
        {loading
          ? (
              <div className="flex h-64 items-center justify-center">
                <div className="size-12 animate-spin rounded-full border-y-2 border-blue-500"></div>
              </div>
            )
          : error
            ? (
                <div className="rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
                  {error}
                </div>
              )
            : products.length === 0
              ? (
                  <div className="py-12 text-center">
                    <h2 className="mb-2 text-xl font-semibold">{t('noProducts')}</h2>
                    <p className="text-gray-600">Không có sản phẩm nào trong danh mục này.</p>
                  </div>
                )
              : (
                  <>
                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                      {products.map(product => (
                        <ProductCard
                          key={product.id}
                          product={product}
                          onAddToCart={handleAddToCart}
                        />
                      ))}
                    </div>

                    {meta.totalPages > 1 && renderPagination()}
                  </>
                )}
      </div>
    </div>
  );
}

export default function CategoryPage() {
  return (
    <ShopLayout>
      <CategoryPageContent />
    </ShopLayout>
  );
}
