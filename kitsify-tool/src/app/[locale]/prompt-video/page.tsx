'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';

export default function PromptVideoPage() {
  const t = useTranslations('PromptVideo');

  const features = [
    {
      title: t('generate_title'),
      description: 'Create professional video prompts using AI-powered analysis and detailed form inputs.',
      href: '/prompt-video/generate',
      icon: (
        <svg className="size-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      ),
      color: 'from-blue-500 to-purple-600',
      bgColor: 'from-blue-50 to-purple-50',
    },
    {
      title: t('history_title'),
      description: 'View and manage your previously generated video prompts with search and pagination.',
      href: '/prompt-video/history',
      icon: (
        <svg className="size-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: 'from-green-500 to-teal-600',
      bgColor: 'from-green-50 to-teal-50',
    },
  ];

  return (
    <div className="mx-auto max-w-7xl">
      {/* Header */}
      <div className="mb-12 text-center">
        <h1 className="mb-4 text-4xl font-bold text-gray-900">
          {t('page_title')}
        </h1>
        <p className="mx-auto max-w-3xl text-lg text-gray-600">
          {t('page_description')}
        </p>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
        {features.map((feature, index) => (
          <Link
            key={index}
            href={feature.href}
            className="group relative overflow-hidden rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl"
          >
            {/* Background Gradient */}
            <div className={`absolute inset-0 bg-gradient-to-br ${feature.bgColor} opacity-50 transition-opacity group-hover:opacity-70`}></div>

            {/* Content */}
            <div className="relative z-10">
              <div className="mb-6 flex items-center">
                <div className={`rounded-lg bg-gradient-to-r ${feature.color} p-3 text-white shadow-lg`}>
                  {feature.icon}
                </div>
              </div>

              <h3 className="mb-4 text-2xl font-bold text-gray-900 transition-colors group-hover:text-gray-800">
                {feature.title}
              </h3>

              <p className="mb-6 text-gray-600 transition-colors group-hover:text-gray-700">
                {feature.description}
              </p>

              <div className="flex items-center text-blue-600 transition-colors group-hover:text-blue-700">
                <span className="font-medium">Get Started</span>
                <svg className="ml-2 size-5 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>

            {/* Decorative Elements */}
            <div className="absolute -right-4 -top-4 size-24 rounded-full bg-white opacity-10"></div>
            <div className="absolute -bottom-6 -left-6 size-32 rounded-full bg-white opacity-5"></div>
          </Link>
        ))}
      </div>

      {/* Additional Info Section */}
      <div className="mt-16 rounded-2xl bg-gradient-to-r from-gray-50 to-blue-50 p-8">
        <div className="text-center">
          <h2 className="mb-4 text-2xl font-bold text-gray-900">
            AI-Powered Video Prompt Generation
          </h2>
          <p className="mx-auto max-w-2xl text-gray-600">
            Our advanced AI system helps you create detailed, professional video prompts by analyzing your inputs
            and generating comprehensive descriptions for cinematography, lighting, mood, and technical specifications.
          </p>
        </div>

        <div className="mt-8 grid grid-cols-1 gap-6 md:grid-cols-3">
          <div className="text-center">
            <div className="mx-auto mb-4 flex size-12 items-center justify-center rounded-lg bg-blue-100">
              <svg className="size-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="mb-2 font-semibold text-gray-900">Fast Generation</h3>
            <p className="text-sm text-gray-600">Generate professional video prompts in seconds</p>
          </div>

          <div className="text-center">
            <div className="mx-auto mb-4 flex size-12 items-center justify-center rounded-lg bg-green-100">
              <svg className="size-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="mb-2 font-semibold text-gray-900">Professional Quality</h3>
            <p className="text-sm text-gray-600">Industry-standard prompts for video production</p>
          </div>

          <div className="text-center">
            <div className="mx-auto mb-4 flex size-12 items-center justify-center rounded-lg bg-purple-100">
              <svg className="size-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h3 className="mb-2 font-semibold text-gray-900">Easy to Use</h3>
            <p className="text-sm text-gray-600">Intuitive interface with guided form inputs</p>
          </div>
        </div>
      </div>
    </div>
  );
}
