import { getTranslations, setRequestLocale } from 'next-intl/server';
import AuthenticatedLayoutWrapper from '@/components/layouts/AuthenticatedLayoutWrapper';

export async function generateMetadata(props: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'PromptVideo',
  });

  return {
    title: t('meta_title') || 'AI Video Prompt Generator',
    description: t('meta_description') || 'Create professional video prompts with AI assistance',
  };
}

export default async function PromptVideoLayout(props: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  return (
    <AuthenticatedLayoutWrapper>
      {props.children}
    </AuthenticatedLayoutWrapper>
  );
}
