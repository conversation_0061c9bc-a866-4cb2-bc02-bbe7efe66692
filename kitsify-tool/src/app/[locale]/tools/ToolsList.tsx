'use client';

import type { Account } from '@/services/accounts';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import AdsGuideModal from '@/components/AdsGuideModal';
import { accountsService } from '@/services/accounts';
import { authService } from '@/services/auth';

export default function ToolsList() {
  const t = useTranslations('Tools');
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [filteredAccounts, setFilteredAccounts] = useState<Account[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isAdsGuideModalOpen, setIsAdsGuideModalOpen] = useState<boolean>(false);

  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        setLoading(true);
        setError(null);
        // Use the new endpoint that includes both package and product accounts
        const response = await accountsService.getAccountsWithProducts();

        if (response?.data) {
          // Remove duplicates by website_url to ensure unique accounts
          const uniqueAccounts = response.data.reduce((acc: Account[], current: Account) => {
            const existingAccount = acc.find(account => account.website_url === current.website_url);
            if (!existingAccount) {
              acc.push(current);
            }
            return acc;
          }, []);

          setAccounts(uniqueAccounts);
        }
      } catch (err) {
        console.error('Error fetching accounts:', err);
        setError('Failed to load accounts. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchAccounts();
  }, []);

  // Filter accounts when search term changes
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredAccounts(accounts);
    } else {
      const filtered = accounts.filter((account) => {
        const nameMatch = (account.name?.toLowerCase() || '').includes(searchTerm.toLowerCase());
        const urlMatch = account.website_url.toLowerCase().includes(searchTerm.toLowerCase());
        return nameMatch || urlMatch;
      });
      setFilteredAccounts(filtered);
    }
  }, [searchTerm, accounts]);

  const handleAccountClick = async (account: Account) => {
    if (account.login_cookie === 0) {
      // Show the guide modal instead of auto login
      setIsAdsGuideModalOpen(true);
    } else {
      // For accounts with login_cookie = 1, extract domain and force inject cookies
      try {
        const url = new URL(account.website_url);
        const domain = url.hostname;

        // First ensure extension is synced with login status
        authService.notifyExtensionAfterLogin();

        // Then force inject cookies for this specific domain with a small delay
        setTimeout(() => {
          authService.forceInjectForDomain(domain);
        }, 100); // Reduced delay for better UX

        // Open the website after a short delay to allow cookie injection
        setTimeout(() => {
          window.open(account.website_url, '_blank');
        }, 150); // Open after cookie injection
      // eslint-disable-next-line unused-imports/no-unused-vars
      } catch (err) {
        // Fallback: just open the website
        window.open(account.website_url, '_blank');
        toast.error('Failed to process account URL, opening website directly');
      }
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-[60vh] items-center justify-center">
        <div className="size-12 animate-spin rounded-full border-y-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-10 text-center">
        <div className="mb-4 text-red-500">{error}</div>
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="rounded bg-blue-500 px-4 py-2 font-bold text-white hover:bg-blue-700"
        >
          {t('try_again')}
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">{t('page_title')}</h1>
        </div>

        <div className="relative w-full md:w-96">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <svg className="size-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <input
            type="text"
            className="block w-full rounded-lg border border-gray-300 bg-white p-2.5 pl-10 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            placeholder={t('search_accounts')}
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </div>
        <Link
          href="https://chromewebstore.google.com/detail/hbdinbncknklkkjeehlkngpbhhadenhn?utm_source=item-share-cb"
          target="_blank"
          rel="noopener noreferrer"
        >
          <button type="button" className="mb-2 me-2 rounded-full border border-gray-300 bg-white px-5 py-2.5 text-sm font-medium text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100">Download Extension</button>
        </Link>
      </div>

      {filteredAccounts.length === 0
        ? (
            <div className="rounded-lg bg-white p-8 text-center shadow-md">
              <svg className="mx-auto size-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="mt-4 text-lg text-gray-500">
                {searchTerm
                  ? t('no_search_results')
                  : t('no_accounts')}
              </p>
            </div>
          )
        : (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
              {filteredAccounts.map((account) => {
                return (
                  <div
                    key={account.id}
                    className="max-w-md overflow-hidden rounded-lg border border-gray-200 bg-white"
                  >
                    <div className="flex items-center gap-4 p-3">
                      <div className="gap-1">
                        {account.img_intro
                          ? (
                              <Image
                                src={account.img_intro}
                                alt={account.name}
                                width={70}
                                height={70}
                                sizes="w-[70px] h-[70px]"
                                style={{ objectFit: 'cover', height: '70px', maxWidth: '70px' }}
                                className="transition-transform duration-500 group-hover:scale-110"
                              />
                            )
                          : (
                              <div className="flex h-full items-center justify-center bg-gray-100">
                                <svg
                                  className="size-20 text-gray-400"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                  />
                                </svg>
                              </div>
                            )}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="text-lg font-medium">{account.name || 'Unnamed Account'}</h3>
                        </div>
                        <p className="line-clamp-3 text-sm text-gray-500">
                          {account.description || `A tool for ${account.name || 'online services'}`}
                        </p>
                      </div>
                    </div>
                    <div className="border-t border-gray-200 px-4 py-2 text-right">
                      <button
                        type="button"
                        onClick={() => handleAccountClick(account)}
                        className="cursor-pointer rounded-md border border-gray-300 px-3 py-1 font-medium text-gray-800 hover:text-gray-600 focus:outline-none"
                      >
                        {account.login_cookie === 0 ? 'Guide' : 'Access'}
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

      {/* AdsGuide Modal */}
      <AdsGuideModal
        isOpen={isAdsGuideModalOpen}
        onClose={() => setIsAdsGuideModalOpen(false)}
      />
    </div>
  );
}
