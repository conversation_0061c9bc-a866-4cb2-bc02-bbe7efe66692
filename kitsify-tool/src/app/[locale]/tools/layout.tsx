import { getTranslations, setRequestLocale } from 'next-intl/server';
import AuthenticatedLayoutWrapper from '@/components/layouts/AuthenticatedLayoutWrapper';

export async function generateMetadata(props: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Tools',
  });

  return {
    title: t('meta_title') || 'My Tools',
  };
}

export default async function ToolsLayout(props: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  return (
    <AuthenticatedLayoutWrapper>
      {props.children}
    </AuthenticatedLayoutWrapper>
  );
}
