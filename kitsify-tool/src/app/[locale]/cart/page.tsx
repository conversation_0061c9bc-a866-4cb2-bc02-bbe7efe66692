'use client';

import type { CreateOrderDto } from '@/services/orders';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import ShopLayout from '@/components/ShopLayout';
import { useCart } from '@/contexts/CartContext';
import { authService } from '@/services/auth';
import { ordersService } from '@/services/orders';

function CartPageContent() {
  const t = useTranslations('Cart');
  const router = useRouter();
  const {
    cart,
    removeFromCart,
    updateQuantity,
    totalPrice,
  } = useCart();

  // Cart state
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Handle checkout
  const handleCheckout = async () => {
    if (cart.length === 0) {
      return;
    }

    setLoading(true);
    setError(null);

    // Check auth status before proceeding
    const isAuthenticated = await authService.checkAuthStatus();
    if (!isAuthenticated) {
      // User is not logged in, redirect to sign-in page
      router.push(`/sign-in?callbackUrl=${encodeURIComponent('/cart')}`);
      return;
    }

    try {
      // Create order payload
      const orderData: CreateOrderDto = {
        items: cart.map(item => ({
          product_id: item.product.id,
          quantity: item.quantity,
        })),
      };

      // Call API to create order
      const response = await ordersService.checkout(orderData);

      // Store order ID for payment capture later
      localStorage.setItem('pendingOrderId', response.orderId.toString());

      // Open PayPal in new tab instead of redirecting
      if (response.approvalLink) {
        window.open(response.approvalLink, '_blank');
        setError(null);
        setLoading(false);
      } else {
        setError('Payment link not available. Please try again.');
      }
    } catch (err) {
      console.error('Checkout error:', err);
      setError('An error occurred during checkout. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle continue shopping
  const handleContinueShopping = () => {
    router.push('/shop');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-8 text-3xl font-bold">{t('title')}</h1>

      {cart.length === 0
        ? (
            <div className="rounded-lg bg-white py-12 text-center shadow-md">
              <h2 className="mb-4 text-xl font-semibold">{t('emptyCart')}</h2>
              <p className="mb-6 text-gray-600">{t('emptyCartMessage')}</p>
              <button
                type="button"
                onClick={handleContinueShopping}
                className="rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700"
              >
                {t('continueShopping')}
              </button>
            </div>
          )
        : (
            <div className="flex flex-col gap-8 lg:flex-row">
              {/* Cart items */}
              <div className="lg:w-2/3">
                <div className="overflow-hidden rounded-lg bg-white shadow-md">
                  <table className="w-full">
                    <thead className="border-b bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          {t('product')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          {t('price')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          {t('quantity')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          {t('total')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          {t('actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {cart.map((item) => {
                        // Get price from selectedDuration if available, otherwise from product
                        let discountPrice = 0;
                        let originalPrice = 0;

                        if (item.selectedDuration) {
                          discountPrice = Number(item.selectedDuration.discount_price) || 0;
                          originalPrice = Number(item.selectedDuration.original_price) || 0;
                        } else {
                          discountPrice = Number(item.product.discount_price) || 0;
                          originalPrice = Number(item.product.original_price) || 0;
                        }

                        const price = discountPrice > 0 ? discountPrice : originalPrice;
                        const itemTotal = price * item.quantity;

                        return (
                          <tr key={`${item.product.id}-${item.selectedDuration?.id || 'no-duration'}`}>
                            <td className="whitespace-nowrap px-6 py-4">
                              <div className="flex items-center">
                                <div className="relative size-16 shrink-0">
                                  {item.product.image_url
                                    ? (
                                        <Image
                                          src={item.product.image_url}
                                          alt={item.product.name}
                                          fill
                                          className="object-cover"
                                        />
                                      )
                                    : (
                                        <div className="flex size-full items-center justify-center bg-gray-200">
                                          <span className="text-xs text-gray-400">No image</span>
                                        </div>
                                      )}
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">
                                    {item.product.name}
                                    {item.selectedDuration && (
                                      <span className="ml-2 rounded bg-blue-100 px-2 py-1 text-xs text-blue-600">
                                        {item.selectedDuration.duration_days}
                                        {' '}
                                        days
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="whitespace-nowrap px-6 py-4">
                              <div className="text-sm text-gray-900">
                                $
                                {price.toFixed(2)}
                              </div>
                              {discountPrice > 0 && (
                                <div className="text-xs text-gray-500 line-through">
                                  $
                                  {originalPrice.toFixed(2)}
                                </div>
                              )}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4">
                              <div className="flex items-center">
                                <button
                                  type="button"
                                  onClick={() => updateQuantity(
                                    item.product.id,
                                    item.quantity - 1,
                                    item.selectedDuration?.id,
                                  )}
                                  className="rounded-l-md border border-gray-300 bg-gray-50 px-2 py-1 hover:bg-gray-100"
                                >
                                  -
                                </button>
                                <input
                                  type="number"
                                  min="1"
                                  value={item.quantity}
                                  onChange={e => updateQuantity(
                                    item.product.id,
                                    Number.parseInt(e.target.value) || 1,
                                    item.selectedDuration?.id,
                                  )}
                                  className="w-12 border-y border-gray-300 py-1 text-center"
                                />
                                <button
                                  type="button"
                                  onClick={() => updateQuantity(
                                    item.product.id,
                                    item.quantity + 1,
                                    item.selectedDuration?.id,
                                  )}
                                  className="rounded-r-md border border-gray-300 bg-gray-50 px-2 py-1 hover:bg-gray-100"
                                >
                                  +
                                </button>
                              </div>
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                              $
                              {itemTotal.toFixed(2)}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4">
                              <button
                                type="button"
                                onClick={() => removeFromCart(item.product.id, item.selectedDuration?.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                {t('remove')}
                              </button>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Order summary */}
              <div className="lg:w-1/3">
                <div className="rounded-lg bg-white p-6 shadow-md">
                  <h2 className="text-lg font-semibold">{t('orderSummary')}</h2>

                  <div className="pt-4">
                    <div className="flex justify-between border-t border-gray-200 pt-4 text-lg font-semibold">
                      <span>{t('total')}</span>
                      <span>
                        $
                        {totalPrice.toFixed(2)}
                      </span>
                    </div>

                    {error && (
                      <div className="mt-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
                        {error}
                      </div>
                    )}

                    <button
                      type="button"
                      onClick={handleCheckout}
                      disabled={loading || cart.length === 0}
                      className={`mt-6 w-full rounded-md px-4 py-2 font-medium text-white ${
                        loading
                          ? 'cursor-not-allowed bg-blue-400'
                          : 'bg-blue-600 hover:bg-blue-700'
                      }`}
                    >
                      {loading ? t('processing') : t('payNow')}
                    </button>

                    <button
                      type="button"
                      onClick={handleContinueShopping}
                      className="mt-4 w-full rounded-md bg-gray-200 px-4 py-2 text-gray-800 hover:bg-gray-300"
                    >
                      {t('continueShopping')}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
    </div>
  );
}

export default function CartPage() {
  return (
    <ShopLayout>
      <CartPageContent />
    </ShopLayout>
  );
}
