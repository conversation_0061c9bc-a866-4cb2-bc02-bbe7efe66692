'use client';

import type { Package } from '@/services/packages';
import { useTranslations } from 'next-intl';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import PaymentModal from '@/components/PaymentModal';
import { packageService } from '@/services/packages';

const Collapse = dynamic(() => import('@/components/Collapse'));
const StickyButton = dynamic(() => import('@/components/StickyButton'));

const DISCORD_LINK = 'https://discord.gg/QAQdZh5z';

const avatars = [
  '/assets/images/avatar1.webp',
  '/assets/images/avatar2.webp',
  '/assets/images/avatar3.webp',
  '/assets/images/avatar4.webp',
  '/assets/images/avatar5.webp',
];

type IBenefit = {
  [key: string]: string;
};

type IQuestion = {
  title: string;
  content: string;
};

type IQuestions = {
  [key: string]: IQuestion;
};

export default function Payment() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPackageId, setSelectedPackageId] = useState<number>(1);
  const [selectedDurationId, setSelectedDurationId] = useState<number | null>(null);
  const [packages, setPackages] = useState<Package[]>([]);
  const [selectedCurrency, setSelectedCurrency] = useState<string>('USD');
  const [selectedMonth, setSelectedMonth] = useState<number>(1);
  const t = useTranslations('Payment');

  // Fetch packages when component mounts or when currency/month changes
  useEffect(() => {
    const fetchPackages = async () => {
      const months = [1, 3, 6, 12]; // Available month options
      const data = await packageService.getPackageDurations(months, selectedCurrency);
      setPackages(data);
    };

    fetchPackages();
  }, [selectedCurrency]);

  // Handle month selection
  const handleMonthSelect = (month: number) => {
    setSelectedMonth(month);
  };

  let benefitsData: IBenefit = {};
  let benefits: string[] = [];
  try {
    benefitsData = t.raw('benefits' as any) as IBenefit;
    benefits = Object.values(benefitsData);
  } catch (error) {
    console.error('Error fetching benefits:', error);
    benefits = [];
  }

  let questionsData: IQuestions = {};
  let questions: IQuestion[] = [];
  try {
    questionsData = t.raw('questions' as any) as IQuestions;
    questions = Object.values(questionsData);
  } catch (error) {
    console.error('Error fetching questions:', error);
    questions = [];
  }

  return (
    <>
      <section className="bg-gray-900 py-12 text-white">
        <div className="container mx-auto">
          <div className="mx-auto flex flex-col items-center md:w-1/2">
            <div className="relative mb-6 h-[300px] w-full overflow-hidden rounded-3xl">
              <Image
                src="/assets/images/tools5.webp"
                alt="27+ Tools"
                fill
                sizes="w-[240px] h-[250px]"
                priority
              />
            </div>
            <p className="mb-2 text-xl">{t('brand')}</p>
            <h2 className="mb-2 text-2xl">{t('title')}</h2>
            <p className="text-center text-xl md:px-6 xl:px-8">{t('description')}</p>
            <div className="my-6 grid w-full grid-cols-2 gap-6 px-10">
              <button
                type="button"
                className="me-2 h-12 rounded-lg bg-green-600 px-5 text-lg font-medium text-white hover:bg-green-800 focus:outline-none focus:ring-4 focus:ring-green-300"
                onClick={() => setIsModalOpen(true)}
              >
                {t('payment_button')}
              </button>
              <button type="button" className="h-12 rounded-lg bg-indigo-600 px-5 text-lg font-medium text-white hover:bg-indigo-800 focus:outline-none focus:ring-4 focus:ring-purple-300">
                <Link href={DISCORD_LINK} target="_blank" rel="noopener noreferrer" className="text-white">
                  {t('join_button')}
                </Link>
              </button>
            </div>

            <div className="mb-10 w-full text-center md:px-10">
              <div className="flex flex-col items-center rounded-2xl bg-gray-950 py-6">
                <p className="mb-4 text-xl">{t('join_people')}</p>
                <div className="flex items-center">
                  {avatars.map((src, index) => (
                    <Image
                      key={src}
                      src={src}
                      alt={`Avatar ${index + 1}`}
                      height={40}
                      width={40}
                      className={`size-10 rounded-full border-2 border-white object-cover shadow-lg ${
                        index > 0 ? '-ml-3' : ''
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>

            <p className="mb-6 text-xl">{t('benefits_title')}</p>
            <div className="w-full md:px-10">
              <div className="flex items-center rounded-2xl bg-gray-950 p-6">
                <Image
                  src="/assets/images/discord_ava.webp"
                  alt="join discord"
                  height={70}
                  width={70}
                  className="rounded-lg"
                  sizes="w-[70px] h-[70px]"
                />
                <div className="ms-4 flex flex-col">
                  <p className="text-xl">{t('discord_access')}</p>
                  <p className="text-xl text-gray-500">{t('tools_access')}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="my-12 w-full md:px-20">
            <div className="flex flex-col items-center rounded-2xl bg-gray-950 p-6 md:px-0">
              <h2 className="mb-4 text-xl font-semibold">{t('learn_about')}</h2>
              <h2 className="mb-6 text-2xl font-semibold">{t('brand')}</h2>
              <button type="button" className="rounded-lg border border-gray-300 bg-white px-5 py-2.5 text-xl font-medium text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:hover:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700">{t('send_message')}</button>
              <p className="mx-auto mt-6 text-center text-xl md:w-1/2">{t('description')}</p>
            </div>
          </div>

          <div className="mx-auto text-center md:w-1/2 md:px-10">
            <p className="mb-6 text-2xl">{t('who_this_for')}</p>
            <div className="flex flex-col rounded-2xl bg-gray-950 p-8">
              <p className="mb-4 text-xl">{t('who_this_for')}</p>
              <p className="text-xl text-gray-500">{t('FOR_ALL_MMO_PLAYERS')}</p>
            </div>
          </div>

          <div className="my-12 w-full md:px-20">
            <div className="flex flex-col rounded-2xl bg-gray-950 py-8 text-center">
              <p className="mb-6 text-2xl">{t('pricing')}</p>
              <h2 className="mb-2 text-2xl font-semibold">{t('join_kitsify')}</h2>
              <p className="text-xl">{t('price_per_month')}</p>
              <div className="mx-auto md:w-1/2">
                <div className="my-8 grid w-full grid-cols-2 gap-6 px-10">
                  <button
                    type="button"
                    className="me-2 h-12 rounded-lg bg-green-600 px-5 text-lg font-medium text-white hover:bg-green-800 focus:outline-none focus:ring-4 focus:ring-green-300"
                    onClick={() => {
                      setSelectedPackageId(1);
                      setIsModalOpen(true);
                    }}
                  >
                    Payment
                  </button>
                  <button type="button" className="h-12 rounded-lg bg-indigo-600 px-5 text-lg font-medium text-white hover:bg-indigo-800 focus:outline-none focus:ring-4 focus:ring-purple-300">
                    <Link href={DISCORD_LINK} target="_blank" rel="noopener noreferrer" className="text-white">
                      Join
                    </Link>
                  </button>
                </div>
                <div className="rounded-2xl bg-gray-700 p-6">
                  {benefits.map(benefit => (
                    <div key={benefit} className="flex py-2">
                      <div className="flex size-8 items-center justify-center rounded-full bg-indigo-600 p-2">
                        <svg
                          aria-hidden="true"
                          className="size-8 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          >
                          </path>
                        </svg>
                      </div>
                      <p className="ms-3 flex-1 border-b border-gray-600 pb-3 text-left text-xl">{benefit}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Pricing Plans */}
          <div className="my-12 w-full md:px-20">
            <div className="flex flex-col rounded-2xl bg-gray-950 py-8 text-center">
              <h2 className="mb-6 text-2xl font-semibold">{t('pricing')}</h2>
              <p className="mb-4 text-xl">{t('save_description')}</p>

              {/* Currency and Duration Selector */}
              <div className="mx-auto mb-8 flex flex-col items-center justify-center gap-4 md:flex-row">
                {/* Currency Toggle */}
                <div className="flex items-center gap-2">
                  <button
                    type="button"
                    className={`rounded-lg px-3 py-1 ${selectedCurrency === 'USD' ? 'bg-blue-600 text-white' : 'bg-gray-700'}`}
                    onClick={() => setSelectedCurrency('USD')}
                    aria-pressed={selectedCurrency === 'USD'}
                  >
                    USD
                  </button>
                  <button
                    type="button"
                    className={`rounded-lg px-3 py-1 ${selectedCurrency === 'VND' ? 'bg-blue-600 text-white' : 'bg-gray-700'}`}
                    onClick={() => setSelectedCurrency('VND')}
                    aria-pressed={selectedCurrency === 'VND'}
                  >
                    VND
                  </button>
                </div>

                {/* Duration Selector */}
                <div className="flex items-center gap-2">
                  {[1, 3, 6, 12].map(month => (
                    <button
                      key={month}
                      type="button"
                      className={`rounded-lg px-3 py-1 ${selectedMonth === month ? 'bg-green-600 text-white' : 'bg-gray-700'}`}
                      onClick={() => handleMonthSelect(month)}
                      aria-pressed={selectedMonth === month}
                    >
                      {month}
                      {' '}
                      {month === 1 ? t('month') : t('months')}
                    </button>
                  ))}
                </div>
              </div>

              <div className="mx-auto grid w-full grid-cols-1 gap-8 px-4 md:grid-cols-3 md:px-10 lg:px-20">
                {packages.map((pkg) => {
                  // Find the appropriate duration for the selected month
                  const duration = pkg.durations.find(d => d.duration_days === selectedMonth * 30) || pkg.durations[0];
                  if (!duration) {
                    return null;
                  }

                  // Determine if this is the "best value" package (package with id 3)
                  const isBestValue = pkg.id === 3;

                  return (
                    <div key={pkg.id} className="flex flex-col rounded-2xl bg-gray-800 p-6 text-left shadow-lg transition-transform duration-300 hover:scale-105">
                      <div className="mb-4 inline-flex w-max items-center justify-center gap-2">
                        <span className="rounded-full bg-orange-500 px-3 py-1 text-sm font-semibold">
                          {t('save')}
                          {' '}
                          {duration.discount_percent}
                          %
                        </span>
                        {isBestValue && (
                          <span className="rounded-full bg-green-500 px-3 py-1 text-sm font-semibold">
                            {t('best_value')}
                          </span>
                        )}
                      </div>
                      <h3 className="mb-2 text-2xl font-bold">{pkg.name}</h3>
                      <div className="mb-1">
                        <span className="text-3xl font-bold text-white">{duration.price}</span>
                      </div>
                      <div className="mb-4">
                        <span className="text-lg text-gray-400 line-through">{duration.original_price}</span>
                        <span className="ml-2 text-lg text-white">
                          {duration.duration_days}
                          {' '}
                          {t('days')}
                        </span>
                      </div>

                      <div className="mb-6 grow">
                        {/* Package description */}
                        {pkg.description && pkg.description.split('\n').map((line, index) => (
                          <div key={`${pkg.id}-desc-${index}`} className="mb-3 flex items-start">
                            <div className="mr-3 flex size-6 shrink-0 items-center justify-center rounded-full bg-green-500 p-1">
                              <svg className="size-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                              </svg>
                            </div>
                            <span>{line}</span>
                          </div>
                        ))}

                        {/* Duration info */}
                        <div className="mb-3 flex items-start">
                          <div className="mr-3 flex size-6 shrink-0 items-center justify-center rounded-full bg-green-500 p-1">
                            <svg className="size-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                            </svg>
                          </div>
                          <span>
                            {t('duration')}
                            :
                            {' '}
                            {duration.duration_days}
                            {' '}
                            {t('days')}
                          </span>
                        </div>

                        {/* Affiliate program */}
                        <div className="mb-3 flex items-start">
                          <div className="mr-3 flex size-6 shrink-0 items-center justify-center rounded-full bg-green-500 p-1">
                            <svg className="size-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                            </svg>
                          </div>
                          <span>{t('affiliate_program')}</span>
                        </div>

                        {/* Additional benefits for premium package */}
                        {isBestValue && (
                          <>
                            <div className="mb-3 flex items-start">
                              <div className="mr-3 flex size-6 shrink-0 items-center justify-center rounded-full bg-green-500 p-1">
                                <svg className="size-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                                </svg>
                              </div>
                              <span>{t('email_support')}</span>
                            </div>
                            <div className="mb-3 flex items-start">
                              <div className="mr-3 flex size-6 shrink-0 items-center justify-center rounded-full bg-green-500 p-1">
                                <svg className="size-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                                </svg>
                              </div>
                              <span>{t('live_chat_support')}</span>
                            </div>
                          </>
                        )}
                      </div>

                      <button
                        type="button"
                        className={`mt-auto rounded-lg px-5 py-3 text-center text-base font-medium text-white focus:outline-none focus:ring-4 ${
                          isBestValue
                            ? 'bg-black hover:bg-gray-900 focus:ring-gray-700'
                            : 'bg-orange-500 hover:bg-orange-600 focus:ring-orange-300'
                        }`}
                        onClick={() => {
                          setSelectedPackageId(pkg.id);
                          setSelectedDurationId(duration.id);
                          setIsModalOpen(true);
                        }}
                      >
                        {t('buy_now')}
                      </button>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          <div className="my-12 w-full md:px-20">
            <p className="mb-6 text-center text-2xl">Frequently asked questions</p>
            {questions.map(question => (
              <div key={question.title} className="mb-3">
                <Collapse title={question.title}>
                  <p>{question.content}</p>
                </Collapse>
              </div>
            ))}
          </div>
        </div>
      </section>
      <StickyButton />
      <PaymentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        packageId={selectedPackageId}
        durationId={selectedDurationId}
      />
    </>
  );
}
