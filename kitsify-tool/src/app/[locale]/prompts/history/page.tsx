/* eslint-disable style/multiline-ternary */
'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import TipTapViewer from '@/components/TipTapViewer';
import { type PromptHistory, promptsService, type QueryPromptHistoriesParams } from '@/services/prompts';

export default function PromptHistoryPage() {
  const t = useTranslations('Prompts');
  const [histories, setHistories] = useState<PromptHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
  const pageSize = 10;

  const fetchHistories = async (params: QueryPromptHistoriesParams = {}) => {
    try {
      setLoading(true);
      const response = await promptsService.getPromptHistories({
        page: currentPage,
        pageSize,
        search_text: searchText || undefined,
        ...params,
      });

      setHistories(response.data);
      setTotalPages(response.pagination.totalPages);
      setTotal(response.pagination.total);
    } catch (error) {
      console.error('Error fetching prompt histories:', error);
      toast.error('Failed to load prompt histories');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHistories({ page: currentPage });
  }, [currentPage, searchText]);

  const handleSearch = () => {
    setCurrentPage(1);
    fetchHistories({ page: 1, search_text: searchText || undefined });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const toggleExpanded = (historyId: number) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(historyId)) {
        newSet.delete(historyId);
      } else {
        newSet.add(historyId);
      }
      return newSet;
    });
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(t('copy_success'));
    } catch (error) {
      console.error('Failed to copy:', error);
      toast.error('Failed to copy to clipboard');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold text-gray-900">
          Prompt History
        </h1>
        <p className="text-gray-600">
          View your generated prompt results and history
        </p>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="flex gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search in results..."
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-full rounded-lg border border-gray-300 px-4 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <button
            type="button"
            onClick={handleSearch}
            className="rounded-lg bg-blue-600 px-6 py-2 text-white transition-colors hover:bg-blue-700"
          >
            Search
          </button>
        </div>
      </div>

      {/* Loading */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="size-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Results */}
      {!loading && (
        <>
          {/* Stats */}
          <div className="mb-6">
            <p className="text-sm text-gray-600">
              Showing
              {' '}
              {histories?.length}
              {' '}
              of
              {' '}
              {total}
              {' '}
              results
            </p>
          </div>

          {/* History List */}
          <div className="space-y-6">
            {histories?.length === 0 ? (
              <div className="py-12 text-center">
                <div className="mb-4 text-gray-400">
                  <svg className="mx-auto size-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="mb-2 text-lg font-medium text-gray-900">No history found</h3>
                <p className="text-gray-600">You haven't generated any prompts yet.</p>
              </div>
            ) : (
              histories?.map((history) => {
                const isExpanded = expandedItems.has(history.id);
                return (
                  <div key={history.id} className="relative rounded-lg border bg-white shadow-sm">
                    {/* Copy Button - Positioned absolutely */}
                    <div className="absolute right-4 top-4 z-10">
                      <button
                        type="button"
                        onClick={() => copyToClipboard(history.generated_result)}
                        className="flex items-center gap-1 rounded-md px-2 py-1 text-xs text-gray-600 transition-colors hover:bg-gray-100 hover:text-gray-900"
                        title="Copy to clipboard"
                      >
                        <svg className="size-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        Copy
                      </button>
                    </div>

                    {/* Collapsible Header */}
                    <button
                      type="button"
                      className="flex w-full cursor-pointer items-center justify-between p-4 pr-20 text-left transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                      onClick={() => toggleExpanded(history.id)}
                    >
                      <div className="flex-1">
                        {history.prompt && (
                          <div className="mb-2">
                            <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                              {history.prompt.title}
                            </span>
                            {history.prompt.category && (
                              <span className="ml-2 inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                                {history.prompt.category.name}
                              </span>
                            )}
                          </div>
                        )}
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>{formatDate(history.created_at)}</span>
                          <span className="inline-flex items-center rounded bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                            {history.model}
                          </span>
                          {history.usage && (
                            <span className="text-xs">
                              {history.usage.total_tokens}
                              {' '}
                              tokens
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Expand/Collapse Icon */}
                      <div className="ml-4">
                        <svg
                          className={`size-5 text-gray-400 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </button>

                    {/* Collapsible Content */}
                    {isExpanded && (
                      <div className="border-t bg-gray-50 p-4">
                        <div className="prose prose-sm max-w-none">
                          <TipTapViewer content={history.generated_result} />
                        </div>
                      </div>
                    )}
                  </div>
                );
              })
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-8 flex justify-center">
              <nav className="flex items-center gap-2">
                <button
                  type="button"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  Previous
                </button>

                <span className="px-4 py-2 text-sm text-gray-700">
                  {t('page')}
                  {' '}
                  {currentPage}
                  {' '}
                  {t('of')}
                  {' '}
                  {totalPages}
                </span>

                <button
                  type="button"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  Next
                </button>
              </nav>
            </div>
          )}
        </>
      )}
    </div>
  );
}
