'use client';

import { useLocale, useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { type PromptsResponse, promptsService, type SimplifiedPrompt } from '@/services/prompts';

export default function PromptsCategoryPage() {
  const t = useTranslations('Prompts');
  const locale = useLocale();
  const params = useParams();
  const categoryId = Number.parseInt(params?.id as string);

  const [prompts, setPrompts] = useState<SimplifiedPrompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [jumpToPage, setJumpToPage] = useState('');

  useEffect(() => {
    const fetchPrompts = async () => {
      try {
        setLoading(true);
        const data: PromptsResponse = await promptsService.getPromptsByCategory({
          page: currentPage,
          pageSize,
          category_id: categoryId,
          search_text: searchText || undefined,
          lang: locale,
        });

        setPrompts(data.data);
        setTotal(data.pagination.total);
        setTotalPages(data.pagination.totalPages);

        // Category name will be fetched separately if needed
      } catch (error) {
        console.error('Error fetching prompts:', error);
        toast.error('Failed to load prompts');
      } finally {
        setLoading(false);
      }
    };

    fetchPrompts();
  }, [categoryId, currentPage, pageSize, searchText]);

  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Handle jump to page
  const handleJumpToPage = () => {
    const pageNumber = Number.parseInt(jumpToPage);
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      setJumpToPage('');
    } else {
      toast.error(`Please enter a page number between 1 and ${totalPages}`);
    }
  };

  if (loading && prompts.length === 0) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="size-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8">
        <Link
          href="/prompts"
          className="mb-4 inline-flex items-center text-blue-600 hover:text-blue-800"
        >
          <svg className="mr-2 size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
          </svg>
          {t('back_to_categories')}
        </Link>

        <h1 className="mb-2 text-3xl font-bold text-gray-900">
          Prompt AI
        </h1>
        <p className="text-gray-600">
          Khám phá các Prompt AI tốt nhất cho ChatGPT được thiết kế để tăng cường doanh nghiệp của bạn và nâng cao năng suất làm việc.
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8 space-y-4">
        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="flex-1">
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <svg className="size-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                className="block w-full rounded-md border border-gray-300 bg-white py-2 pl-10 pr-3 leading-5 placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:placeholder:text-gray-400"
                placeholder={t('search_placeholder')}
                value={searchText}
                onChange={e => handleSearch(e.target.value)}
              />
            </div>
          </div>

        </div>

        {/* Filter Tags */}
        <div className="flex flex-wrap gap-2">
          <span className="rounded-full bg-purple-100 px-3 py-1 text-sm font-medium text-purple-800">
            {t('free')}
          </span>
          <span className="rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800">
            {t('premium')}
          </span>
          <span className="rounded-full bg-yellow-100 px-3 py-1 text-sm font-medium text-yellow-800">
            {t('master_prompter')}
          </span>
        </div>
      </div>

      {/* Prompts Grid */}
      <div className="relative mb-8">
        {/* Loading Overlay */}
        {loading && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/80 backdrop-blur-sm">
            <div className="flex flex-col items-center space-y-3">
              <div className="size-12 animate-spin rounded-full border-b-2 border-purple-600"></div>
              <p className="text-sm text-gray-600">Loading prompts...</p>
            </div>
          </div>
        )}

        <div className={`grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 ${loading ? 'opacity-50' : ''}`}>
          {prompts.map(prompt => (
            <div key={prompt.id} className="rounded-lg bg-white shadow-md transition-shadow duration-300 hover:shadow-lg">
              <div className="p-6">
                {/* Header */}
                <div className="mb-4 flex items-start justify-between">
                  <div className="flex size-10 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500 to-blue-500">
                    <svg className="size-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="flex gap-1">
                    <span className="rounded bg-red-100 px-2 py-1 text-xs font-medium text-red-800">
                      {t('new')}
                    </span>
                    <button type="button" className="rounded p-1 hover:bg-gray-100">
                      <svg className="size-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Content */}
                <h3 className="mb-2 line-clamp-2 font-semibold text-gray-900">
                  {prompt.title}
                </h3>

                {prompt.short_description && (
                  <p className="mb-4 line-clamp-3 text-sm text-gray-600">
                    {prompt.short_description}
                  </p>
                )}

                {/* Footer */}
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-500">
                    Prompt ID:
                    {' '}
                    {prompt.id}
                  </div>
                </div>

                {/* Action Button */}
                <Link
                  href={`/prompts/${prompt.id}`}
                  className="mt-4 block w-full rounded-lg bg-purple-100 px-4 py-2 text-center font-medium text-purple-800 transition-colors duration-200 hover:bg-purple-200"
                >
                  {t('view_prompt')}
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Enhanced Pagination */}
      {totalPages > 1 && (
        <div className="mt-8 space-y-4">
          {/* Results Info and Jump to Page */}
          <div className="flex flex-col items-center justify-between space-y-4 sm:flex-row sm:space-y-0">
            <div className="text-sm text-gray-700">
              Showing
              {' '}
              <span className="font-medium">{((currentPage - 1) * pageSize) + 1}</span>
              {' '}
              to
              {' '}
              <span className="font-medium">{Math.min(currentPage * pageSize, total)}</span>
              {' '}
              of
              {' '}
              <span className="font-medium">{total}</span>
              {' '}
              results
            </div>

            {/* Jump to Page and Items per Page */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">Go to page:</span>
                <input
                  type="number"
                  min="1"
                  max={totalPages}
                  value={jumpToPage}
                  onChange={e => setJumpToPage(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleJumpToPage();
                    }
                  }}
                  className="w-16 rounded border border-gray-300 px-2 py-1 text-center text-sm focus:border-purple-500 focus:outline-none focus:ring-1 focus:ring-purple-500"
                  placeholder={currentPage.toString()}
                />
                <button
                  type="button"
                  onClick={handleJumpToPage}
                  disabled={!jumpToPage || loading}
                  className="rounded bg-purple-500 px-3 py-1 text-sm font-medium text-white hover:bg-purple-600 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  Go
                </button>
              </div>

              <select
                value={pageSize}
                onChange={e => handlePageSizeChange(Number.parseInt(e.target.value))}
                disabled={loading}
                className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value={12}>
                  12
                  {' '}
                  {t('items_per_page')}
                </option>
                <option value={24}>
                  24
                  {' '}
                  {t('items_per_page')}
                </option>
                <option value={48}>
                  48
                  {' '}
                  {t('items_per_page')}
                </option>
              </select>
            </div>
          </div>

          {/* Pagination Controls */}
          <div className="flex items-center justify-center space-x-1">
            {/* Previous Button */}
            <button
              type="button"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1 || loading}
              className="flex items-center rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <svg className="mr-1 size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
              </svg>
              Previous
            </button>

            {/* Page Numbers */}
            <div className="flex items-center space-x-1">
              {/* First page */}
              {currentPage > 3 && (
                <>
                  <button
                    type="button"
                    onClick={() => setCurrentPage(1)}
                    disabled={loading}
                    className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    1
                  </button>
                  {currentPage > 4 && (
                    <span className="px-2 text-gray-500">...</span>
                  )}
                </>
              )}

              {/* Current page and neighbors */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                if (pageNum > totalPages) {
                  return null;
                }

                return (
                  <button
                    key={pageNum}
                    type="button"
                    onClick={() => setCurrentPage(pageNum)}
                    disabled={loading}
                    className={`rounded-lg border px-3 py-2 text-sm font-medium ${
                      pageNum === currentPage
                        ? 'border-purple-500 bg-purple-500 text-white'
                        : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'
                    } disabled:cursor-not-allowed disabled:opacity-50`}
                  >
                    {pageNum}
                  </button>
                );
              })}

              {/* Last page */}
              {currentPage < totalPages - 2 && (
                <>
                  {currentPage < totalPages - 3 && (
                    <span className="px-2 text-gray-500">...</span>
                  )}
                  <button
                    type="button"
                    onClick={() => setCurrentPage(totalPages)}
                    disabled={loading}
                    className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {totalPages}
                  </button>
                </>
              )}
            </div>

            {/* Next Button */}
            <button
              type="button"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages || loading}
              className="flex items-center rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
            >
              Next
              <svg className="ml-1 size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {prompts.length === 0 && !loading && (
        <div className="py-12 text-center">
          <div className="text-lg text-gray-400">
            {t('no_prompts')}
          </div>
        </div>
      )}
    </div>
  );
}
