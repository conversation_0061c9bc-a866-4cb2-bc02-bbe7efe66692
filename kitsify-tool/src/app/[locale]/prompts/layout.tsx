import { getTranslations, setRequestLocale } from 'next-intl/server';
import AuthenticatedLayoutWrapper from '@/components/layouts/AuthenticatedLayoutWrapper';

export async function generateMetadata(props: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Prompts',
  });

  return {
    title: t('meta_title') || 'AI Prompt Library',
    description: t('meta_description') || 'Discover professionally designed AI prompts',
  };
}

export default async function PromptsLayout(props: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  return (
    <AuthenticatedLayoutWrapper>
      {props.children}
    </AuthenticatedLayoutWrapper>
  );
}
