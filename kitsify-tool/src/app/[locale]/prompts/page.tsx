'use client';

import { useLocale, useTranslations } from 'next-intl';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { type PromptCategory, promptsService } from '@/services/prompts';

export default function PromptsPage() {
  const t = useTranslations('Prompts');
  const locale = useLocale();
  const [categories, setCategories] = useState<PromptCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const data = await promptsService.getPromptCategories(locale);
        setCategories(data);
      } catch (error) {
        console.error('Error fetching categories:', error);
        toast.error('Failed to load prompt categories');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [locale]);

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchText.toLowerCase())
    || (category.description && category.description.toLowerCase().includes(searchText.toLowerCase())),
  );

  if (loading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="size-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-12 text-center">
        <h1 className="mb-4 text-4xl font-bold text-gray-900">
          {t('page_title')}
        </h1>
        <p className="mx-auto max-w-3xl text-lg text-gray-600">
          {t('page_description')}
        </p>
      </div>

      {/* Search */}
      <div className="mx-auto mb-12 max-w-md">
        <div className="relative">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <svg className="size-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            className="block w-full rounded-md border border-gray-300 bg-white py-2 pl-10 pr-3 leading-5 placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:placeholder:text-gray-400"
            placeholder={t('search_placeholder')}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
          />
        </div>
      </div>

      {/* Categories Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredCategories.map(category => (
          <Link
            key={category.id}
            href={`/prompts/category/${category.id}`}
            className="group relative rounded-2xl bg-gradient-to-br from-purple-100 to-blue-100 p-6 transition-all duration-300 hover:scale-105 hover:shadow-lg"
          >
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-xl font-semibold text-gray-900 transition-colors group-hover:text-blue-600">
                {category.name}
              </h3>
              <svg
                className="size-6 text-gray-400 transition-colors group-hover:text-blue-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>

            {category.description && (
              <p className="mb-4 line-clamp-2 text-sm text-gray-600">
                {category.description}
              </p>
            )}

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">
                {category.prompt_count}
                {' '}
                prompts
              </span>
              {category.is_coming_soon && (
                <span className="rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
                  Coming Soon
                </span>
              )}
            </div>

            {/* Decorative elements */}
            <div className="absolute right-4 top-4 opacity-20">
              {category.image_url
                ? (
                    <img
                      src={category.image_url}
                      alt={category.name}
                      className="size-12 object-contain"
                    />
                  )
                : (
                    <div className="size-12 rounded-lg bg-gradient-to-br from-purple-200 to-blue-200"></div>
                  )}
            </div>
          </Link>
        ))}
      </div>

      {filteredCategories.length === 0 && !loading && (
        <div className="py-12 text-center">
          <div className="text-lg text-gray-400">
            {searchText ? 'No categories found matching your search.' : 'No categories available.'}
          </div>
        </div>
      )}
    </div>
  );
}
