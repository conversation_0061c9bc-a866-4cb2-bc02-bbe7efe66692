import { getTranslations, setRequestLocale } from 'next-intl/server';
import AuthenticatedLayoutWrapper from '@/components/layouts/AuthenticatedLayoutWrapper';

export async function generateMetadata(props: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Resources',
  });

  return {
    title: t('resources_title') || 'Resources',
  };
}

export default async function ResourcesLayout(props: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  // Server-side protection is handled by middleware
  // This is just a fallback in case middleware fails

  return (
    <AuthenticatedLayoutWrapper>
      {props.children}
    </AuthenticatedLayoutWrapper>
  );
}
