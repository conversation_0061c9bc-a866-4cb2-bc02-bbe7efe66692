import { getTranslations, setRequestLocale } from 'next-intl/server';

type ITshirtDesignsProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: ITshirtDesignsProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Resources',
  });

  return {
    title: t('tshirt_designs_title') || '3000+ Editable T-shirt Designs',
    description: t('tshirt_designs_description') || 'Access to over 3000 editable T-shirt designs for your e-commerce business',
  };
}

export default async function TshirtDesigns(props: ITshirtDesignsProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  return (
    <div className="mx-auto max-w-4xl p-6">
      {/* Main Header */}
      <h1 className="mb-4 text-4xl font-bold">3000+ Editable T-shirt Designs</h1>

      {/* Links Section */}
      <div className="mb-4">
        <p className="mb-1">
          <span className="font-medium">Link:</span>
          <a href="https://t.ly/3P2fc" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">https://t.ly/3P2fc ↗</a>
        </p>
        <p className="mb-6">
          <span className="font-medium">Link:</span>
          <a href="https://t.ly/gI0iW" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">https://t.ly/gI0iW ↗</a>
        </p>
      </div>

      {/* Editing Pack Section */}
      <h2 className="mb-4 text-3xl font-bold">50GB Editing Pack</h2>

      <p className="mb-2 font-medium">Download Links:</p>
      <div className="flex flex-col space-y-2">
        <a href="https://mega.nz/folder/JSFzlA7C#divyHtvEsLMHhIh_9KkboQ" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">https://mega.nz/folder/JSFzlA7C#divyHtvEsLMHhIh_9KkboQ ↗</a>

        <a href="https://drive.google.com/drive/folders/1CIvtJS3YmpwdAGPuWlgtjHD6m4p7TdAt" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">https://drive.google.com/drive/folders/1CIvtJS3YmpwdAGPuWlgtjHD6m4p7TdAt ↗</a>

        <a href="https://drive.google.com/drive/folders/1uSn53gmuxo80eCvIXuocx134ytfvLM2R" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">https://drive.google.com/drive/folders/1uSn53gmuxo80eCvIXuocx134ytfvLM2R ↗</a>

        <a href="https://drive.google.com/drive/folders/1539HnE_BdKUmTKPkIirTP3me02DFOIPi" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">https://drive.google.com/drive/folders/1539HnE_BdKUmTKPkIirTP3me02DFOIPi ↗</a>

        <a href="https://drive.google.com/drive/folders/1O5RYpzH40u8iXQiu4MrB3Z-HwKit5rFn" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">https://drive.google.com/drive/folders/1O5RYpzH40u8iXQiu4MrB3Z-HwKit5rFn ↗</a>

        <a href="https://drive.google.com/drive/folders/19TFWzyg8KEOiCn5e5hab-LoozEoJwcSW" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">https://drive.google.com/drive/folders/19TFWzyg8KEOiCn5e5hab-LoozEoJwcSW ↗</a>

        <a href="https://drive.google.com/drive/folders/1TpgqSOLhae8ErlN5oLFsfn_Y5pvygPUZ" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">https://drive.google.com/drive/folders/1TpgqSOLhae8ErlN5oLFsfn_Y5pvygPUZ ↗</a>

        <a href="https://drive.google.com/drive/folders/1BRYiVzoDk9THjnRvX8ZU6CHhqbhTco0D" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">https://drive.google.com/drive/folders/1BRYiVzoDk9THjnRvX8ZU6CHhqbhTco0D ↗</a>
      </div>
    </div>
  );
}
