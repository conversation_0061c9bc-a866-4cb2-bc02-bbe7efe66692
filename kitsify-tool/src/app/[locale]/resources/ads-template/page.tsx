import { getTranslations, setRequestLocale } from 'next-intl/server';

type IAdsTemplateProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IAdsTemplateProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Resources',
  });

  return {
    title: t('ads_template_title') || 'ADS Template Resources',
    description: t('ads_template_description') || 'Access to ADS template resources for your e-commerce business',
  };
}

export default async function AdsTemplate(props: IAdsTemplateProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  return (
    <div className="mx-auto max-w-4xl p-6">
      <h1 className="mb-4 text-4xl font-bold">ADS Template Resources</h1>
      <p className="mb-4">This page is under construction. Please check back later for ADS template resources.</p>
    </div>
  );
}
