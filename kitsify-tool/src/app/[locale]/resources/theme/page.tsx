import { getTranslations, setRequestLocale } from 'next-intl/server';

type IThemeProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IThemeProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Resources',
  });

  return {
    title: t('theme_title') || 'Theme Resources',
    description: t('theme_description') || 'Access to theme resources for your e-commerce business',
  };
}

export default async function Theme(props: IThemeProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);

  return (
    <div className="mx-auto max-w-4xl p-6">
      {/* Main Header */}
      <h1 className="mb-4 text-4xl font-bold">Shopify Theme Resources</h1>

      {/* HUNDREDS OF SHOPIFY THEMES Section */}
      <h2 className="mb-4 text-3xl font-bold">Hundreds of Shopify Themes</h2>
      <p className="mb-2 font-medium">Links:</p>
      <div className="mb-8 flex flex-col space-y-2">
        <a
          href="https://themelock.com/ecommer/shopify-archive"
          className="text-blue-600 hover:underline"
          target="_blank"
          rel="noopener noreferrer"
        >
          https://themelock.com/ecommer/shopify-archive ↗
        </a>
        <a
          href="https://crackevil.com"
          className="text-blue-600 hover:underline"
          target="_blank"
          rel="noopener noreferrer"
        >
          https://crackevil.com ↗
        </a>
      </div>

      {/* 150+ SHOPIFY THEMES Section */}
      <h2 className="mb-4 text-3xl font-bold">150+ Shopify Themes</h2>
      <p className="mb-2 font-medium">Link:</p>
      <div className="mb-8 flex flex-col space-y-2">
        <a
          href="https://drive.google.com/drive/folders/13LPSgUBZj62AfTb81IK2Oc9uWRvdSMEt"
          className="text-blue-600 hover:underline"
          target="_blank"
          rel="noopener noreferrer"
        >
          https://drive.google.com/drive/folders/13LPSgUBZj62AfTb81IK2Oc9uWRvdSMEt ↗
        </a>
      </div>

      {/* BUNDLE THEMES Section */}
      <h2 className="mb-4 text-3xl font-bold">Bundle Themes</h2>
      <p className="mb-2 font-medium">Link:</p>
      <div className="mb-8 flex flex-col space-y-2">
        <a
          href="https://drive.google.com/drive/folders/13LPSgUBZj62AfTb81IK2Oc9uWRvdSMEt"
          className="text-blue-600 hover:underline"
          target="_blank"
          rel="noopener noreferrer"
        >
          https://drive.google.com/drive/folders/13LPSgUBZj62AfTb81IK2Oc9uWRvdSMEt ↗
        </a>
      </div>
    </div>
  );
}
