'use client';

import type { Product, ProductDuration } from '@/services/products';
import { useTranslations } from 'next-intl';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { toast } from 'react-toastify';

export type CartItem = {
  product: Product;
  quantity: number;
  selectedDuration?: ProductDuration;
};

type CartContextType = {
  cart: CartItem[];
  addToCart: (product: Product, quantity?: number, selectedDuration?: ProductDuration) => void;
  removeFromCart: (productId: number, durationId?: number) => void;
  updateQuantity: (productId: number, quantity: number, durationId?: number) => void;
  clearCart: () => void;
  cartCount: number;
  totalPrice: number;
};

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: React.ReactNode }) {
  const t = useTranslations('Shop');
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  useEffect(() => {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      try {
        setCart(JSON.parse(savedCart));
      } catch (err) {
        console.error('Error parsing cart data:', err);
        setCart([]);
      }
    }
    setIsInitialLoad(false);
  }, []);

  useEffect(() => {
    if (!isInitialLoad) {
      // Optimize cart data for localStorage - only keep essential fields
      const optimizedCart = cart.map(item => ({
        product: {
          id: item.product.id,
          name: item.product.name,
          image_url: item.product.image_url,
          description: item.product.description,
          // Only keep these pricing fields for backward compatibility
          original_price: item.product.original_price,
          discount_price: item.product.discount_price,
        },
        quantity: item.quantity,
        selectedDuration: item.selectedDuration
          ? {
              id: item.selectedDuration.id,
              product_id: item.selectedDuration.product_id,
              duration_days: item.selectedDuration.duration_days,
              original_price: item.selectedDuration.original_price,
              discount_price: item.selectedDuration.discount_price,
              discount_percent: item.selectedDuration.discount_percent,
            }
          : undefined,
      }));
      localStorage.setItem('cart', JSON.stringify(optimizedCart));
    }
  }, [cart, isInitialLoad]);

  const addToCart = useCallback(
    (product: Product, quantity: number = 1, selectedDuration?: ProductDuration) => {
      setCart((prevCart) => {
        // Nếu có duration, tìm item với cả product ID và duration ID
        if (selectedDuration) {
          const existingItem = prevCart.find(
            item => item.product.id === product.id
              && item.selectedDuration?.id === selectedDuration.id,
          );

          if (existingItem) {
            return prevCart.map(item =>
              item.product.id === product.id
              && item.selectedDuration?.id === selectedDuration.id
                ? { ...item, quantity: item.quantity + quantity }
                : item,
            );
          }

          // Thêm mới với duration
          return [...prevCart, { product, quantity, selectedDuration }];
        } else {
          // Xử lý như cũ nếu không có duration
          const existingItem = prevCart.find(
            item => item.product.id === product.id && !item.selectedDuration,
          );

          if (existingItem) {
            return prevCart.map(item =>
              item.product.id === product.id && !item.selectedDuration
                ? { ...item, quantity: item.quantity + quantity }
                : item,
            );
          }

          return [...prevCart, { product, quantity }];
        }
      });

      const durationText = selectedDuration
        ? ` (${selectedDuration.duration_days} ngày)`
        : '';

      toast.success(t('productAddedToCart', {
        productName: `${product.name}${durationText}`,
      }));
    },
    [t],
  );

  const removeFromCart = useCallback((productId: number, durationId?: number) => {
    setCart(prevCart =>
      prevCart.filter((item) => {
        if (durationId) {
          // Nếu có durationId, chỉ xóa item có cả productId và durationId
          return !(item.product.id === productId && item.selectedDuration?.id === durationId);
        }
        // Nếu không có durationId, xóa item có productId
        return item.product.id !== productId;
      }),
    );
  }, []);

  const updateQuantity = useCallback((productId: number, quantity: number, durationId?: number) => {
    if (quantity < 1) {
      return;
    }
    setCart(prevCart =>
      prevCart.map((item) => {
        if (durationId) {
          // Nếu có durationId, chỉ cập nhật item có cả productId và durationId
          return (item.product.id === productId && item.selectedDuration?.id === durationId)
            ? { ...item, quantity }
            : item;
        }
        // Nếu không có durationId, cập nhật item có productId
        return item.product.id === productId ? { ...item, quantity } : item;
      }),
    );
  }, []);

  const clearCart = useCallback(() => {
    setCart([]);
  }, []);

  const cartCount = useMemo(() => {
    return cart.reduce((count, item) => count + item.quantity, 0);
  }, [cart]);

  const totalPrice = useMemo(() => {
    return cart.reduce((total, item) => {
      let price = 0;

      if (item.selectedDuration) {
        // Nếu có duration, lấy giá từ duration
        const discountPrice = Number(item.selectedDuration.discount_price) || 0;
        const originalPrice = Number(item.selectedDuration.original_price) || 0;
        price = discountPrice > 0 ? discountPrice : originalPrice;
      } else {
        // Nếu không có duration, sử dụng giá từ product (cho tương thích ngược)
        const discountPrice = Number(item.product?.discount_price) || 0;
        const originalPrice = Number(item.product?.original_price) || 0;
        price = discountPrice > 0 ? discountPrice : originalPrice;
      }

      return total + price * item.quantity;
    }, 0);
  }, [cart]);

  const value = useMemo(
    () => ({
      cart,
      addToCart,
      removeFromCart,
      updateQuantity,
      clearCart,
      cartCount,
      totalPrice,
    }),
    [
      cart,
      addToCart,
      removeFromCart,
      updateQuantity,
      clearCart,
      cartCount,
      totalPrice,
    ],
  );

  return <CartContext value={value}>{children}</CartContext>;
}

// Tách hàm useCart ra để tránh lỗi Fast Refresh
// eslint-disable-next-line react-refresh/only-export-components
export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
