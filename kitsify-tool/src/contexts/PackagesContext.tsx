'use client';

import type { ReactNode } from 'react';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { packageService, type PackageUser, type UserPackagesResponse } from '@/services/packages';

type PackagesContextType = {
  userPackages: UserPackagesResponse | null;
  loading: boolean;
  error: string | null;
  refreshPackages: () => Promise<void>;
  // Helper functions
  hasActivePackage: () => boolean;
  hasFeatureAccess: (featureKey: 'has_prompt_library' | 'has_prompt_video') => boolean;
  hasTrialAccess: () => boolean;
};

const PackagesContext = createContext<PackagesContextType | undefined>(undefined);

type PackagesProviderProps = {
  children: ReactNode;
};

export const PackagesProvider: React.FC<PackagesProviderProps> = ({ children }) => {
  const [userPackages, setUserPackages] = useState<UserPackagesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserPackages = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await packageService.fetchUserPackages();
      setUserPackages(response);
    } catch (err) {
      console.error('Error fetching user packages:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch packages');
      setUserPackages(null);
    } finally {
      setLoading(false);
    }
  };

  const refreshPackages = async () => {
    await fetchUserPackages();
  };

  // Helper function to check if user has active package or product
  const hasActivePackage = (): boolean => {
    if (!userPackages?.data) {
      return false;
    }

    const hasActivePackage = userPackages.data.packages?.some(
      (packageUser: PackageUser) => packageUser.status === 'active',
    ) ?? false;

    const hasActiveProduct = userPackages.data.products?.some(
      (productUser: PackageUser) => productUser.status === 'active',
    ) ?? false;

    return hasActivePackage || hasActiveProduct;
  };

  // Helper function to check feature access
  const hasFeatureAccess = (featureKey: 'has_prompt_library' | 'has_prompt_video'): boolean => {
    if (!userPackages?.data) {
      return false;
    }

    const hasPackageFeature = userPackages.data.packages?.some(
      (packageUser: PackageUser) =>
        packageUser.status === 'active' && packageUser[featureKey] === true,
    ) ?? false;

    const hasProductFeature = userPackages.data.products?.some(
      (productUser: PackageUser) =>
        productUser.status === 'active' && productUser[featureKey] === true,
    ) ?? false;

    return hasPackageFeature || hasProductFeature;
  };

  // Helper function to check trial access (for backward compatibility)
  const hasTrialAccess = (): boolean => {
    // For now, we'll consider trial access based on having any active package/product
    // You can modify this logic based on your specific trial package identification
    return hasActivePackage();
  };

  useEffect(() => {
    fetchUserPackages();
  }, []);

  const contextValue: PackagesContextType = {
    userPackages,
    loading,
    error,
    refreshPackages,
    hasActivePackage,
    hasFeatureAccess,
    hasTrialAccess,
  };

  return (
    <PackagesContext value={contextValue}>
      {children}
    </PackagesContext>
  );
};

export const usePackages = (): PackagesContextType => {
  const context = useContext(PackagesContext);
  if (context === undefined) {
    throw new Error('usePackages must be used within a PackagesProvider');
  }
  return context;
};
