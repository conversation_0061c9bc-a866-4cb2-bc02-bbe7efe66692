@tailwind base;
@tailwind components;
@tailwind utilities;

/* TipTap Editor Styles */
.tiptap-viewer .ProseMirror {
  outline: none;
}

.tiptap-viewer .prose {
  max-width: none;
}

.tiptap-viewer .prose h1,
.tiptap-viewer .prose h2,
.tiptap-viewer .prose h3,
.tiptap-viewer .prose h4,
.tiptap-viewer .prose h5,
.tiptap-viewer .prose h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.tiptap-viewer .prose p {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.tiptap-viewer .prose ul,
.tiptap-viewer .prose ol {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
  padding-left: 1.5em;
}

.tiptap-viewer .prose li {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

.tiptap-viewer .prose strong {
  font-weight: 600;
}

.tiptap-viewer .prose em {
  font-style: italic;
}

.tiptap-viewer .prose code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.tiptap-viewer .prose pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
}

.tiptap-viewer .prose blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1.5em 0;
  font-style: italic;
  color: #6b7280;
}
