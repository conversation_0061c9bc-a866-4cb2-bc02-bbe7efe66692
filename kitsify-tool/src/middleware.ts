import type { NextRequest } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import { NextResponse } from 'next/server';
import { routing } from './libs/i18nNavigation';
import { resourcesProtectionMiddleware } from './middleware/resources-protection';
import { detectLocale } from './utils/localeDetection';

const intlMiddleware = createMiddleware(routing);

export default async function middleware(
  request: NextRequest,
) {
  // First check if this is a resources route and apply protection
  const resourcesResponse = await resourcesProtectionMiddleware(request);

  // If the resources middleware returned a response, use it
  if (resourcesResponse.status !== 200) {
    return resourcesResponse;
  }

  // Check if we need to redirect to detected locale
  const pathname = request.nextUrl.pathname;
  const pathnameHasLocale = routing.locales.some(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`,
  );

  // Only redirect for root path without locale, and don't interfere with explicit locale routes
  if (!pathnameHasLocale && pathname === '/') {
    const detectedLocale = detectLocale(request);

    // Only redirect if detected locale is different from default
    // and we don't have an explicit locale in the URL
    if (detectedLocale !== routing.defaultLocale) {
      const redirectUrl = new URL(`/${detectedLocale}`, request.url);
      const response = NextResponse.redirect(redirectUrl);

      // Set cookie to remember user's preference
      response.cookies.set('preferred-locale', detectedLocale, {
        maxAge: 60 * 60 * 24 * 365, // 1 year
        httpOnly: false, // Allow client-side access
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
      });

      return response;
    }
  }

  // Apply internationalization middleware
  const response = intlMiddleware(request);

  // If user explicitly navigates to a locale route, update their cookie preference
  if (pathnameHasLocale) {
    const localeFromPath = pathname.split('/')[1];
    if (localeFromPath && routing.locales.includes(localeFromPath)) {
      const currentCookie = request.cookies.get('preferred-locale')?.value;
      // Only update cookie if it's different from current preference
      if (currentCookie !== localeFromPath) {
        response.cookies.set('preferred-locale', localeFromPath, {
          maxAge: 60 * 60 * 24 * 365, // 1 year
          httpOnly: false, // Allow client-side access
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
        });
      }
    }
  }

  return response;
}

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|monitoring|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
