import type { PackageUser, UserPackagesResponse } from '@/services/packages';

/**
 * Shared utility functions for package permissions
 * Used by both middleware (server-side) and React context (client-side)
 */

/**
 * Check if user has active package or product
 */
export function hasActivePackage(userPackages: UserPackagesResponse | null): boolean {
  if (!userPackages?.data) {
    return false;
  }

  const hasActivePackage = userPackages.data.packages?.some(
    (packageUser: PackageUser) => packageUser.status === 'active',
  ) ?? false;

  const hasActiveProduct = userPackages.data.products?.some(
    (productUser: PackageUser) => productUser.status === 'active',
  ) ?? false;

  return hasActivePackage || hasActiveProduct;
}

/**
 * Check if user has access to a specific feature
 */
export function hasFeatureAccess(
  userPackages: UserPackagesResponse | null,
  featureKey: 'has_prompt_library' | 'has_prompt_video',
): boolean {
  if (!userPackages?.data) {
    return false;
  }

  const hasPackageFeature = userPackages.data.packages?.some(
    (packageUser: PackageUser) =>
      packageUser.status === 'active' && packageUser[featureKey] === true,
  ) ?? false;

  const hasProductFeature = userPackages.data.products?.some(
    (productUser: PackageUser) =>
      productUser.status === 'active' && productUser[featureKey] === true,
  ) ?? false;

  return hasPackageFeature || hasProductFeature;
}

/**
 * Feature permission configuration
 */
export const FEATURE_ROUTE_CONFIG = {
  '/prompts': 'has_prompt_library',
  '/prompt-video': 'has_prompt_video',
} as const;

export type FeatureKey = typeof FEATURE_ROUTE_CONFIG[keyof typeof FEATURE_ROUTE_CONFIG];

/**
 * Get required feature for a given pathname
 */
export function getRequiredFeature(pathname: string): FeatureKey | null {
  // Remove locale prefix from pathname (e.g., /en/prompts -> /prompts)
  const cleanPath = pathname.replace(/^\/[a-z]{2}(?=\/|$)/, '');

  for (const routePattern of Object.keys(FEATURE_ROUTE_CONFIG)) {
    if (cleanPath.startsWith(routePattern)) {
      return FEATURE_ROUTE_CONFIG[routePattern as keyof typeof FEATURE_ROUTE_CONFIG];
    }
  }

  return null;
}

/**
 * Check if user has permission for a specific route
 */
export function hasRoutePermission(
  userPackages: UserPackagesResponse | null,
  pathname: string,
): boolean {
  const requiredFeature = getRequiredFeature(pathname);

  if (!requiredFeature) {
    return true; // No feature requirement for this route
  }

  return hasFeatureAccess(userPackages, requiredFeature);
}
