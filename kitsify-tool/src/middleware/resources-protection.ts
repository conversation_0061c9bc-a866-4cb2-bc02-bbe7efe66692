import type { NextRequest } from 'next/server';
import type { UserPackagesResponse } from '@/services/packages';
import { AxiosError } from 'axios';
import { NextResponse } from 'next/server';
import { api } from '@/services/api';
import { packageService } from '@/services/packages';

/**
 * Set cookies for API requests in middleware context
 */
function setApiCookiesFromRequest(request: NextRequest): void {
  const cookies = request.headers.get('cookie');
  if (cookies) {
    // Set the cookie header for subsequent API calls
    api.defaults.headers.common.Cookie = cookies;
  }
}

/**
 * Clean up API cookies after middleware execution
 */
function cleanupApiCookies(): void {
  delete api.defaults.headers.common.Cookie;
}

/**
 * Get locale from request with fallback
 */
function getLocale(request: NextRequest): string {
  return request.nextUrl.locale || 'en';
}

/**
 * Create redirect response to sign-in page
 */
function redirectToSignIn(request: NextRequest): NextResponse {
  const locale = getLocale(request);
  return NextResponse.redirect(new URL(`/${locale}/sign-in`, request.url));
}

/**
 * Create redirect response to tools page
 */
function redirectToTools(request: NextRequest): NextResponse {
  const locale = getLocale(request);
  return NextResponse.redirect(new URL(`/${locale}/tools`, request.url));
}

/**
 * Check if user has active package or product
 */
function hasActivePackage(userPackages: UserPackagesResponse): boolean {
  const hasActivePackage = userPackages.data?.packages?.some(
    packageUser => packageUser.status === 'active',
  ) ?? false;

  const hasActiveProduct = userPackages.data?.products?.some(
    productUser => productUser.status === 'active',
  ) ?? false;

  return hasActivePackage || hasActiveProduct;
}

/**
 * Feature permission configuration
 * Maps route patterns to required package features
 *
 * To add new feature routes:
 * 1. Add the route pattern and corresponding package feature flag here
 * 2. Ensure the package entity has the feature flag column
 * 3. The middleware will automatically protect the new route
 *
 * Example: '/new-feature': 'has_new_feature'
 */
const FEATURE_ROUTE_CONFIG = {
  '/prompts': 'has_prompt_library',
  '/prompt-video': 'has_prompt_video',
} as const;

type FeatureKey = typeof FEATURE_ROUTE_CONFIG[keyof typeof FEATURE_ROUTE_CONFIG];

/**
 * Check if user has access to a specific feature
 */
function hasFeatureAccess(
  userPackages: UserPackagesResponse,
  featureKey: FeatureKey,
): boolean {
  const hasPackageFeature = userPackages.data?.packages?.some(
    packageUser => packageUser.status === 'active' && packageUser[featureKey] === true,
  ) ?? false;

  const hasProductFeature = userPackages.data?.products?.some(
    productUser => productUser.status === 'active' && productUser[featureKey] === true,
  ) ?? false;

  return hasPackageFeature || hasProductFeature;
}

/**
 * Check if the request path is for resources
 */
function isResourcesRoute(pathname: string): boolean {
  return pathname.includes('/resources');
}

/**
 * Check if the request path requires feature permission
 */
function getRequiredFeature(pathname: string): FeatureKey | null {
  // Remove locale prefix from pathname (e.g., /en/prompts -> /prompts)
  const cleanPath = pathname.replace(/^\/[a-z]{2}(?=\/|$)/, '');

  for (const routePattern of Object.keys(FEATURE_ROUTE_CONFIG)) {
    if (cleanPath.startsWith(routePattern)) {
      return FEATURE_ROUTE_CONFIG[routePattern as keyof typeof FEATURE_ROUTE_CONFIG];
    }
  }

  return null;
}

/**
 * Check if user has permission for the requested route
 */
function hasRoutePermission(
  userPackages: UserPackagesResponse,
  pathname: string,
): boolean {
  const requiredFeature = getRequiredFeature(pathname);

  if (!requiredFeature) {
    return true; // No feature requirement for this route
  }

  return hasFeatureAccess(userPackages, requiredFeature);
}

/**
 * Handle authentication errors
 */
function handleAuthError(error: unknown, request: NextRequest): NextResponse | null {
  if (error instanceof AxiosError) {
    const status = error.response?.status;
    if (status === 401 || status === 403) {
      return redirectToSignIn(request);
    }
  }
  return null;
}

/**
 * Middleware to protect resources routes and feature-specific routes
 * This middleware checks:
 * 1. Resources routes - allows users with active packages/products
 * 2. Feature routes - allows users with required package features
 *
 * Note: This middleware runs server-side and cannot use React Context,
 * so it maintains its own API call. However, the logic is consistent
 * with the client-side PackagesContext.
 */
export async function resourcesProtectionMiddleware(request: NextRequest): Promise<NextResponse> {
  const pathname = request.nextUrl.pathname;

  // Check if this route requires feature permission
  const requiredFeature = getRequiredFeature(pathname);
  const isResourceRoute = isResourcesRoute(pathname);

  // Early return if not a protected route
  if (!isResourceRoute && !requiredFeature) {
    return NextResponse.next();
  }

  try {
    // Check for access token
    const accessToken = request.cookies.get('access_token');
    if (!accessToken?.value) {
      return redirectToSignIn(request);
    }

    // Set cookies for API requests in middleware context
    setApiCookiesFromRequest(request);

    // Fetch user packages using the existing service
    const userPackages = await packageService.fetchUserPackages();

    // Clean up API cookies
    cleanupApiCookies();

    if (!userPackages) {
      return redirectToSignIn(request);
    }

    // Handle resources route protection - only allow users with active packages/products
    if (isResourceRoute) {
      // Check if user has active package or product
      if (!hasActivePackage(userPackages)) {
        return redirectToTools(request);
      }
    }

    // Handle feature-specific route protection
    if (requiredFeature) {
      if (!hasRoutePermission(userPackages, pathname)) {
        // User doesn't have required feature access, redirect to tools page
        return redirectToTools(request);
      }
    }

    return NextResponse.next();
  } catch (error) {
    console.error('Error in resources protection middleware:', error);

    // Clean up API cookies in case of error
    cleanupApiCookies();

    // Handle authentication errors
    const authErrorResponse = handleAuthError(error, request);
    if (authErrorResponse) {
      return authErrorResponse;
    }

    // For other errors, allow the request to continue
    // The layout component will handle authentication
    return NextResponse.next();
  }
}
