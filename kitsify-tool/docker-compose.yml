version: '3.8'

services:
  kitsify-tool:
    image: node:20-alpine
    working_dir: /app
    ports:
      - '3001:3001'
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-https://admin.kitsify.com}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL:-https://kitsify.com}
      - NEXT_PUBLIC_PAYPAL_CLIENT_ID=${NEXT_PUBLIC_PAYPAL_CLIENT_ID}
    volumes:
      - .:/app
    command: node .next/standalone/server.js
    restart: unless-stopped
    healthcheck:
      test: wget --spider -q http://localhost:3001 || exit 1
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
