#!/bin/bash

# Exit on error
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Kitsify Tool Deployment Script${NC}"
echo "=============================="

# Check if .env.product file exists
if [ ! -f .env.product ]; then
    echo -e "${RED}Error: .env.product file not found.${NC}"
    echo -e "${YELLOW}Please create an .env.product file with your environment variables.${NC}"
    exit 1
fi

# Load environment variables from .env.product
echo -e "${YELLOW}Loading environment variables from .env.product...${NC}"
export $(grep -v '^#' .env.product | xargs)

# Display the environment variables being used
echo -e "${YELLOW}Using environment variables:${NC}"
echo -e "${YELLOW}NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-https://admin.kitsify.com}${NC}"
echo -e "${YELLOW}NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL:-https://kitsify.com}${NC}"

# Build the application with the environment variables
echo -e "${GREEN}Building the application...${NC}"
yarn build

# Start the Docker container
echo -e "${GREEN}Starting Docker container...${NC}"
docker-compose up -d

# Check if container is running
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Container is now running!${NC}"
    echo -e "${GREEN}Frontend: http://localhost:3001${NC}"
    echo -e "${GREEN}API URL: ${NEXT_PUBLIC_API_URL}${NC}"
    echo -e "${YELLOW}Note: Make sure your backend API is running at the URL specified in your .env.product file.${NC}"
    echo -e "${YELLOW}To view logs: docker-compose logs -f${NC}"
    echo -e "${YELLOW}To stop container: docker-compose down${NC}"
else
    echo -e "${RED}Error: Failed to start container. Check the logs for more information.${NC}"
    echo -e "${YELLOW}To view logs: docker-compose logs${NC}"
    exit 1
fi
