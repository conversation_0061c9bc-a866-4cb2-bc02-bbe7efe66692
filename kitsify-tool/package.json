{"name": "next-js-boilerplate", "version": "3.60.5", "author": "Ixartz (https://github.com/ixartz)", "scripts": {"dev:spotlight": "spotlight-sidecar", "dev:next": "next dev -p 3001", "dev": "run-p dev:*", "build": "next build --no-lint", "postbuild": "next-sitemap", "start": "next start -p 3001", "build-stats": "cross-env ANALYZE=true npm run build", "clean": "rimraf .next out coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "check-types": "tsc --noEmit --pretty", "test": "vitest run", "test:e2e": "playwright test", "commit": "cz", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:serve": "http-server storybook-static --port 6006 --silent", "serve-storybook": "run-s storybook:*", "test-storybook:ci": "start-server-and-test serve-storybook http://127.0.0.1:6006 test-storybook"}, "dependencies": {"@clerk/localizations": "^3.9.0", "@clerk/nextjs": "^6.9.0", "@hookform/resolvers": "^3.9.1", "@t3-oss/env-nextjs": "^0.11.1", "@tiptap/extension-color": "^2.1.16", "@tiptap/extension-text-style": "^2.1.16", "@tiptap/react": "^2.1.16", "@tiptap/starter-kit": "^2.1.16", "@types/marked": "^5.0.2", "axios": "^1.9.0", "marked": "^15.0.12", "next": "^15.1.0", "next-intl": "^3.26.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.0", "react-icons": "^5.4.0", "react-intersection-observer": "^9.14.0", "react-toastify": "^11.0.5", "swiper": "11.1.15", "zod": "^3.24.0"}, "devDependencies": {"@antfu/eslint-config": "^3.11.2", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@commitlint/cz-commitlint": "^19.5.0", "@eslint-react/eslint-plugin": "^1.19.0", "@faker-js/faker": "^9.3.0", "@next/bundle-analyzer": "^15.1.0", "@next/eslint-plugin-next": "^15.1.0", "@percy/cli": "1.30.4", "@percy/playwright": "^1.0.7", "@playwright/test": "^1.49.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/addon-onboarding": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/nextjs": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/test": "^8.4.7", "@storybook/test-runner": "^0.20.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/node": "^22.10.1", "@types/pg": "^8.11.10", "@types/react": "^19.0.1", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^2.1.8", "@vitest/expect": "^2.1.8", "autoprefixer": "^10.4.20", "commitizen": "^4.3.1", "cross-env": "^7.0.3", "cssnano": "^7.0.6", "dotenv-cli": "^7.4.4", "eslint": "^9.16.0", "eslint-plugin-format": "^0.1.3", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-playwright": "^2.1.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-testing-library": "^7.1.1", "http-server": "^14.1.1", "jsdom": "^25.0.1", "next-sitemap": "^4.2.3", "npm-run-all": "^4.1.5", "postcss": "^8.4.49", "rimraf": "^6.0.1", "semantic-release": "^24.2.0", "start-server-and-test": "^2.0.8", "storybook": "^8.4.7", "tailwindcss": "^3.4.16", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.1.8", "vitest-fail-on-console": "^0.7.1"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "release": {"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits"}], "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/git", "@semantic-release/github"]}}