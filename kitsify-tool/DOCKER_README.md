# Kitsify Tool Docker Setup

This document provides instructions for running the Kitsify Tool frontend application using Docker. The setup uses Yarn as the package manager.

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/)

## Getting Started

### 1. Environment Variables

Copy the production environment file and update it with your configuration:

```bash
cp .env.production .env.product
```

Edit the `.env.product` file and fill in the required values:

- `NEXT_PUBLIC_API_URL`: URL of your backend API (default: https://admin.kitsify.com)
- `NEXT_PUBLIC_APP_URL`: URL of your frontend application (default: https://kitsify.com)
- `NEXT_PUBLIC_PAYPAL_CLIENT_ID`: Your PayPal client ID for payment processing

### 2. Build and Start the Container

You can use the provided setup script:

```bash
./docker-setup.sh
```

Or manually with Docker Compose:

```bash
docker-compose up -d
```

These commands will:
- Build the Docker image for the kitsify-tool frontend
- Start the service defined in the docker-compose.yml file

### 3. Access the Application

Once the container is running, you can access the frontend at:

- http://localhost:3001

Note: You'll need to have your backend API running separately at the URL specified in `NEXT_PUBLIC_API_URL`.

## Common Commands

### View Container Logs

```bash
# View logs
docker-compose logs

# Follow logs in real-time
docker-compose logs -f
```

### Stop Container

```bash
docker-compose down
```

### Rebuild Container

If you make changes to the application code, you'll need to rebuild the container:

```bash
docker-compose up -d --build
```

## Troubleshooting

### Container Not Starting

Check the logs for errors:

```bash
docker-compose logs
```

### Environment Variables Not Applied

If environment variables aren't being applied correctly:

1. Check that your `.env.product` file is in the correct location
2. Verify that the docker-compose.yml file is correctly mounting the .env.product file
3. Restart the container:

```bash
docker-compose down
docker-compose up -d
```

## Production Deployment

For production deployment, consider:

1. Using a reverse proxy like Nginx for SSL termination
2. Setting up proper monitoring and logging solutions
3. Configuring a CI/CD pipeline for automated deployments
