import withBundleAnalyzer from '@next/bundle-analyzer';
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/libs/i18n.ts');

const bundleAnalyzer = withBundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

/** @type {import('next').NextConfig} */
export default
bundleAnalyzer(
  withNextIntl({
    output: 'standalone',
    eslint: {
      dirs: ['.'],
    },
    poweredByHeader: false,
    reactStrictMode: true,

    // API rewrites to proxy frontend API calls to backend
    async rewrites() {
      return [
        {
          source: '/api/:path*',
          destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/:path*`,
        },
      ];
    },

    // Performance optimizations
    compiler: {
      removeConsole: process.env.NODE_ENV === 'production',
    },

    // Turbopack configuration (stable in Next.js 15)
    turbopack: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },

    // Experimental features for better performance
    experimental: {
      optimizePackageImports: [
        'react-icons',
        'swiper',
        '@clerk/nextjs',
        'react-hook-form',
        'zod',
      ],
    },

    // Webpack optimizations
    webpack: (config, { dev, isServer }) => {
      // Optimize bundle splitting
      if (!dev && !isServer) {
        config.optimization.splitChunks = {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              priority: 10,
              reuseExistingChunk: true,
            },
            common: {
              name: 'common',
              minChunks: 2,
              priority: 5,
              reuseExistingChunk: true,
            },
          },
        };
      }

      config.optimization.sideEffects = false;

      return config;
    },

    images: {
      remotePatterns: [
        {
          protocol: 'https',
          hostname: 'randomuser.me',
          pathname: '/**',
        },
        {
          protocol: 'http',
          hostname: 'localhost',
          pathname: '/**',
        },
        {
          protocol: 'https',
          hostname: 'admin.kitsify.com',
          pathname: '/**',
        },
      ],
      formats: ['image/webp', 'image/avif'],
      minimumCacheTTL: 60,
    },
  }),
);
