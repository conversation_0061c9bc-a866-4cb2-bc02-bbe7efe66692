import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemovePriceFieldsFromProducts1950000000002
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove price-related columns from products table
    await queryRunner.query(`
      ALTER TABLE "products" 
      DROP COLUMN IF EXISTS "original_price",
      DROP COLUMN IF EXISTS "discount_price",
      DROP COLUMN IF EXISTS "quantity"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back price-related columns to products table
    await queryRunner.query(`
      ALTER TABLE "products" 
      ADD COLUMN "original_price" DECIMAL(10,2) NOT NULL DEFAULT 0,
      ADD COLUMN "discount_price" DECIMAL(10,2) NOT NULL DEFAULT 0,
      ADD COLUMN "quantity" INTEGER NOT NULL DEFAULT 0
    `);

    // Restore data from product_durations if it exists
    await queryRunner.query(`
      UPDATE products p
      SET 
        original_price = COALESCE((SELECT original_price FROM product_durations pd WHERE pd.product_id = p.id ORDER BY pd.id LIMIT 1), 0),
        discount_price = COALESCE((SELECT discount_price FROM product_durations pd WHERE pd.product_id = p.id ORDER BY pd.id LIMIT 1), 0),
        quantity = COALESCE((SELECT quantity FROM product_durations pd WHERE pd.product_id = p.id ORDER BY pd.id LIMIT 1), 0)
      WHERE EXISTS (SELECT 1 FROM product_durations pd WHERE pd.product_id = p.id)
    `);
  }
}
