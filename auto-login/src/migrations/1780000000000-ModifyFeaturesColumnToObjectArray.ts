import { MigrationInterface, QueryRunner } from 'typeorm';

export class ModifyFeaturesColumnToObjectArray1780000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, drop the existing features column
    await queryRunner.query(`
      ALTER TABLE "packages" 
      DROP COLUMN IF EXISTS "features"
    `);

    // Then, add the new features column as a JSONB array
    await queryRunner.query(`
      ALTER TABLE "packages" 
      ADD COLUMN "features" JSONB DEFAULT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert back to the original text array column
    await queryRunner.query(`
      ALTER TABLE "packages" 
      DROP COLUMN IF EXISTS "features"
    `);

    await queryRunner.query(`
      ALTER TABLE "packages" 
      ADD COLUMN "features" TEXT[] DEFAULT NULL
    `);
  }
}
