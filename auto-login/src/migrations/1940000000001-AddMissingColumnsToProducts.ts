import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMissingColumnsToProducts1940000000001
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add image_url and features columns to products table
    await queryRunner.query(`
      ALTER TABLE "products"
      ADD COLUMN "image_url" VARCHAR(255) NULL,
      ADD COLUMN "features" JSONB NOT NULL DEFAULT '[]'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the columns if migration is reverted
    await queryRunner.query(`
      ALTER TABLE "products"
      DROP COLUMN "image_url",
      DROP COLUMN "features"
    `);
  }
}
