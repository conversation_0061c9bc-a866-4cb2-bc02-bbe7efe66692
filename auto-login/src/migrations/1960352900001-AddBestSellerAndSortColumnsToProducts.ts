import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBestSellerAndSortColumnsToProducts1960352900001
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add is_best_seller and sort columns to products table
    await queryRunner.query(`
      ALTER TABLE "products"
      ADD COLUMN "is_best_seller" BOOLEAN NOT NULL DEFAULT false,
      ADD COLUMN "sort" INTEGER NOT NULL DEFAULT 0
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the columns if migration is reverted
    await queryRunner.query(`
      ALTER TABLE "products"
      DROP COLUMN "is_best_seller",
      DROP COLUMN "sort"
    `);
  }
}
