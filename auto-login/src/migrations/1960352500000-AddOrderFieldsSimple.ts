import { MigrationInterface, QueryRunner } from 'typeorm';

export class Add<PERSON>rderFieldsSimple1960352500000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "orders" 
      ADD COLUMN "payment_method" character varying,
      ADD COLUMN "subtotal" numeric(10,2) NOT NULL DEFAULT '0';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "orders" 
      DROP COLUMN "payment_method",
      DROP COLUMN "subtotal";
    `);
  }
}
