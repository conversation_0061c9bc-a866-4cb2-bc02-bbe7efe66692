import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateProductUsers1960352800000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create product_users table
    await queryRunner.query(`
      CREATE TABLE "product_users" (
        "id" SERIAL PRIMARY KEY,
        "user_id" INTEGER NOT NULL,
        "product_id" INTEGER NOT NULL,
        "start_date" TIMESTAMP NOT NULL DEFAULT now(),
        "expires_at" TIMESTAMP NOT NULL,
        "status" VARCHAR(50) NOT NULL DEFAULT 'active',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE,
        FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE
      )
    `);

    // Add indexes for faster lookups
    await queryRunner.query(`
      CREATE INDEX "idx_product_users_user_id" ON "product_users"("user_id");
      CREATE INDEX "idx_product_users_product_id" ON "product_users"("product_id");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "product_users"`);
  }
}
