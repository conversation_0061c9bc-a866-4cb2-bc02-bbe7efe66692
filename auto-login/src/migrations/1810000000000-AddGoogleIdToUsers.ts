import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddGoogleIdToUsers1810000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add google_id column to users table
    await queryRunner.query(`
      ALTER TABLE "users" 
      ADD COLUMN "google_id" VARCHAR(255) UNIQUE DEFAULT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove google_id column from users table
    await queryRunner.query(`
      ALTER TABLE "users" 
      DROP COLUMN "google_id"
    `);
  }
}
