import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddProductDurationIdToPayments1950000000003
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add product_duration_id column to payments table
    await queryRunner.query(`
      ALTER TABLE "payments" 
      ADD COLUMN "product_duration_id" INTEGER NULL
    `);

    // Add foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "payments"
      ADD CONSTRAINT "FK_payments_product_durations"
      FOREIGN KEY ("product_duration_id") 
      REFERENCES "product_durations"("id")
      ON DELETE SET NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "payments" 
      DROP CONSTRAINT IF EXISTS "FK_payments_product_durations"
    `);

    // Drop product_duration_id column
    await queryRunner.query(`
      ALTER TABLE "payments" 
      DROP COLUMN IF EXISTS "product_duration_id"
    `);
  }
}
