import { MigrationInterface, QueryRunner } from 'typeorm';

export class ReplaceProductIdWithPackageDurationId1740000000002
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add package_duration_id column
    await queryRunner.query(`
      ALTER TABLE "payments" 
      ADD COLUMN IF NOT EXISTS "package_duration_id" INTEGER NULL
    `);

    // Add foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "payments"
      ADD CONSTRAINT "FK_payments_package_durations"
      FOREIGN KEY ("package_duration_id") 
      REFERENCES "package_durations"("id")
      ON DELETE SET NULL
    `);

    // Drop product_id foreign key constraint if it exists
    await queryRunner.query(`
      DO $$
      BEGIN
        IF EXISTS (
          SELECT 1 FROM information_schema.table_constraints 
          WHERE constraint_name = 'FK_payments_products' 
          AND table_name = 'payments'
        ) THEN
          ALTER TABLE "payments" DROP CONSTRAINT "FK_payments_products";
        END IF;
      END $$;
    `);

    // Drop product_id column
    await queryRunner.query(`
      ALTER TABLE "payments" 
      DROP COLUMN IF EXISTS "product_id"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add product_id column back
    await queryRunner.query(`
      ALTER TABLE "payments" 
      ADD COLUMN IF NOT EXISTS "product_id" INTEGER NULL
    `);

    // Try to restore data from package_duration_id to product_id
    await queryRunner.query(`
      UPDATE "payments" p
      SET "product_id" = pd.package_id
      FROM "package_durations" pd
      WHERE p."package_duration_id" = pd.id
    `);

    // Add foreign key constraint back
    await queryRunner.query(`
      ALTER TABLE "payments"
      ADD CONSTRAINT "FK_payments_products"
      FOREIGN KEY ("product_id") 
      REFERENCES "products"("id")
      ON DELETE SET NULL
    `);

    // Drop package_duration_id foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "payments" 
      DROP CONSTRAINT IF EXISTS "FK_payments_package_durations"
    `);

    // Drop package_duration_id column
    await queryRunner.query(`
      ALTER TABLE "payments" 
      DROP COLUMN IF EXISTS "package_duration_id"
    `);
  }
}
