import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsSingleToolToPackages1930000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add is_single_tool column to packages table
    await queryRunner.query(`
      ALTER TABLE "packages" 
      ADD COLUMN "is_single_tool" BOOLEAN NOT NULL DEFAULT false
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove is_single_tool column from packages table
    await queryRunner.query(`
      ALTER TABLE "packages" 
      DROP COLUMN "is_single_tool"
    `);
  }
}
