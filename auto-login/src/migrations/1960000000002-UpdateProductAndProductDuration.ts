import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateProductAndProductDuration1960000000002
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Xóa các trường không cần thiết từ bảng products
    await queryRunner.query(`
      ALTER TABLE products
      DROP COLUMN IF EXISTS original_price,
      DROP COLUMN IF EXISTS discount_price,
      DROP COLUMN IF EXISTS quantity;
    `);

    // Kiểm tra xem bảng product_durations có tồn tại không
    const tableExists = await queryRunner.hasTable('product_durations');
    if (!tableExists) {
      // Bỏ qua các thao tác với bảng product_durations vì bảng chưa tồn tại
      // Bảng sẽ được tạo trong migration tiếp theo
      return;
    }

    // Đảm bảo bảng product_durations có các trường cần thiết
    // Kiểm tra xem các trường đã tồn tại chưa trước khi thêm
    const columns = await queryRunner.query(
      `SELECT column_name FROM information_schema.columns 
       WHERE table_name = 'product_durations' AND table_schema = 'public'`,
    );

    const columnNames = columns.map((column: any) => column.column_name);

    if (!columnNames.includes('original_price')) {
      await queryRunner.query(`
        ALTER TABLE product_durations
        ADD COLUMN IF NOT EXISTS original_price DECIMAL(10, 2) NOT NULL DEFAULT 0
      `);
    }

    if (!columnNames.includes('discount_price')) {
      await queryRunner.query(`
        ALTER TABLE product_durations
        ADD COLUMN IF NOT EXISTS discount_price DECIMAL(10, 2) NOT NULL DEFAULT 0
      `);
    }

    if (!columnNames.includes('quantity')) {
      await queryRunner.query(`
        ALTER TABLE product_durations
        ADD COLUMN IF NOT EXISTS quantity INTEGER NOT NULL DEFAULT 0
      `);
    }

    // Kiểm tra xem bảng order_items có tồn tại không
    const orderItemsTableExists = await queryRunner.hasTable('order_items');
    if (!orderItemsTableExists) {
      // Bỏ qua các thao tác với bảng order_items vì bảng chưa tồn tại
      return;
    }

    // Kiểm tra và thêm trường product_duration_id vào bảng order_items
    const orderItemsColumns = await queryRunner.query(
      `SELECT column_name FROM information_schema.columns 
       WHERE table_name = 'order_items' AND table_schema = 'public'`,
    );

    const orderItemsColumnNames = orderItemsColumns.map(
      (column: any) => column.column_name,
    );

    if (!orderItemsColumnNames.includes('product_duration_id')) {
      await queryRunner.query(`
        ALTER TABLE order_items
        ADD COLUMN IF NOT EXISTS product_duration_id INTEGER,
        ADD CONSTRAINT fk_product_duration
        FOREIGN KEY (product_duration_id) 
        REFERENCES product_durations(id)
        ON DELETE SET NULL
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Khôi phục các trường trong bảng products
    const productsTableExists = await queryRunner.hasTable('products');
    if (productsTableExists) {
      await queryRunner.query(`
        ALTER TABLE products
        ADD COLUMN IF NOT EXISTS original_price DECIMAL(10, 2) DEFAULT 0,
        ADD COLUMN IF NOT EXISTS discount_price DECIMAL(10, 2) DEFAULT 0,
        ADD COLUMN IF NOT EXISTS quantity INTEGER DEFAULT 0;
      `);
    }

    // Xóa trường product_duration_id từ bảng order_items nếu có
    const orderItemsTableExists = await queryRunner.hasTable('order_items');
    if (orderItemsTableExists) {
      await queryRunner.query(`
        ALTER TABLE order_items
        DROP COLUMN IF EXISTS product_duration_id;
      `);
    }
  }
}
