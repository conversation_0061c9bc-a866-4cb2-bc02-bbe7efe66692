import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddExpiresAtToSubscriptions1840000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'subscriptions',
      new TableColumn({
        name: 'expires_at',
        type: 'timestamp',
        isNullable: true,
      }),
    );

    // Update existing subscriptions to set expires_at based on start_date and package duration
    await queryRunner.query(`
      UPDATE subscriptions 
      SET expires_at = start_date + INTERVAL '1 day' * (
        SELECT duration_days 
        FROM package_durations 
        WHERE package_durations.id = subscriptions.package_duration_id
      )
      WHERE expires_at IS NULL AND package_duration_id IS NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('subscriptions', 'expires_at');
  }
}
