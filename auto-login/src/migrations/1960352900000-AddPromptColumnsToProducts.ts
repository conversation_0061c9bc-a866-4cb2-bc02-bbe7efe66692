import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPromptColumnsToProducts1960352900000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add has_prompt_library and has_prompt_video columns to products table
    await queryRunner.query(`
      ALTER TABLE "products"
      ADD COLUMN "has_prompt_library" BOOLEAN NOT NULL DEFAULT false,
      ADD COLUMN "has_prompt_video" BOOLEAN NOT NULL DEFAULT false
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the columns if migration is reverted
    await queryRunner.query(`
      ALTER TABLE "products"
      DROP COLUMN "has_prompt_library",
      DROP COLUMN "has_prompt_video"
    `);
  }
}
