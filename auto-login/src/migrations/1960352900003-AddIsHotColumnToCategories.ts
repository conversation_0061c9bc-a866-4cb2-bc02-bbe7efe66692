import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsHotColumnToCategories1960352900003
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add is_hot column to categories table
    await queryRunner.query(`
      ALTER TABLE "categories"
      ADD COLUMN "is_hot" BOOLEAN NOT NULL DEFAULT false
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the column if migration is reverted
    await queryRunner.query(`
      ALTER TABLE "categories"
      DROP COLUMN "is_hot"
    `);
  }
}
