import { MigrationInterface, QueryRunner } from 'typeorm';

export class ModifyPromptTablesForDataImport1850000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // <PERSON> phép insert ID cụ thể vào các bảng prompt
    // Bằng cách tạm thời alter các bảng để có thể insert ID custom

    // Thêm comment để đánh dấu việc import data
    await queryRunner.query(`
      COMMENT ON TABLE "prompt_categories" IS 'Modified for data import - allows custom ID insertion';
    `);

    await queryRunner.query(`
      COMMENT ON TABLE "topics" IS 'Modified for data import - allows custom ID insertion';
    `);

    await queryRunner.query(`
      COMMENT ON TABLE "prompts" IS 'Modified for data import - allows custom ID insertion';
    `);

    // Note: PostgreSQL SERIAL columns can accept custom IDs
    // We just need to reset the sequences after import
    console.log(
      'Tables modified for data import. Remember to reset sequences after import.',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove comments
    await queryRunner.query(`
      COMMENT ON TABLE "prompt_categories" IS NULL;
    `);

    await queryRunner.query(`
      COMMENT ON TABLE "topics" IS NULL;
    `);

    await queryRunner.query(`
      COMMENT ON TABLE "prompts" IS NULL;
    `);
  }
}
