import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPromptColumnsToPackages1910000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add has_prompt_library column
    await queryRunner.query(`
      ALTER TABLE "packages" 
      ADD COLUMN "has_prompt_library" BOOLEAN NOT NULL DEFAULT false
    `);

    // Add has_prompt_video column
    await queryRunner.query(`
      ALTER TABLE "packages" 
      ADD COLUMN "has_prompt_video" BOOLEAN NOT NULL DEFAULT false
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove columns in reverse order
    await queryRunner.query(`
      ALTER TABLE "packages" 
      DROP COLUMN IF EXISTS "has_prompt_video"
    `);

    await queryRunner.query(`
      ALTER TABLE "packages" 
      DROP COLUMN IF EXISTS "has_prompt_library"
    `);
  }
}
