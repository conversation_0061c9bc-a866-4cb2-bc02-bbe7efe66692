import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCampaignsTable1920000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create campaigns table
    await queryRunner.query(`
      CREATE TABLE "campaigns" (
        "id" SERIAL PRIMARY KEY,
        "name" VARCHAR(255) NOT NULL,
        "subject" VARCHAR(500) NOT NULL,
        "html_template" TEXT NOT NULL,
        "target_audience" VARCHAR(50) NOT NULL DEFAULT 'all',
        "status" VARCHAR(50) NOT NULL DEFAULT 'draft',
        "scheduled_at" TIMESTAMP,
        "sent_at" TIMESTAMP,
        "total_recipients" INTEGER DEFAULT 0,
        "sent_count" INTEGER DEFAULT 0,
        "failed_count" INTEGER DEFAULT 0,
        "created_by" INTEGER NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE CASCADE
      )
    `);

    // Create campaign_recipients table for tracking individual sends
    await queryRunner.query(`
      CREATE TABLE "campaign_recipients" (
        "id" SERIAL PRIMARY KEY,
        "campaign_id" INTEGER NOT NULL,
        "user_id" INTEGER NOT NULL,
        "email" VARCHAR(255) NOT NULL,
        "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
        "sent_at" TIMESTAMP,
        "error_message" TEXT,
        "opened_at" TIMESTAMP,
        "tracking_id" VARCHAR(255) UNIQUE,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        FOREIGN KEY ("campaign_id") REFERENCES "campaigns"("id") ON DELETE CASCADE,
        FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
      )
    `);

    // Create indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "idx_campaigns_status" ON "campaigns"("status");
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_campaigns_created_by" ON "campaigns"("created_by");
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_campaign_recipients_campaign_id" ON "campaign_recipients"("campaign_id");
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_campaign_recipients_status" ON "campaign_recipients"("status");
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_campaign_recipients_tracking_id" ON "campaign_recipients"("tracking_id");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'DROP TABLE IF EXISTS "campaign_recipients" CASCADE',
    );
    await queryRunner.query('DROP TABLE IF EXISTS "campaigns" CASCADE');
  }
}
