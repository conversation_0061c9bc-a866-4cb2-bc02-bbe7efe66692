import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInitialSchema1703664000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create Users table
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" SERIAL PRIMARY KEY,
        "email" VARCHAR(255) NOT NULL UNIQUE,
        "password" VARCHAR(255) NOT NULL,
        "status" VARCHAR(50) NOT NULL DEFAULT 'inactive',
        "discord_id" VARCHAR(255) UNIQUE,
        "role" VARCHAR(50) NOT NULL DEFAULT 'user',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // Create Accounts table
    await queryRunner.query(`
      CREATE TABLE "accounts" (
        "id" SERIAL PRIMARY KEY,
        "website_url" VARCHAR(255) NOT NULL,
        "username" VARCHAR(255) NOT NULL,
        "password" VARCHAR(255) NOT NULL,
        "last_login" TIMESTAMP,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // Create Products table
    await queryRunner.query(`
      CREATE TABLE "products" (
        "id" SERIAL PRIMARY KEY,
        "name" VARCHAR(255) NOT NULL,
        "description" TEXT,
        "price" DECIMAL(10,2) NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // Create Coupons table
    await queryRunner.query(`
      CREATE TABLE "coupons" (
        "id" SERIAL PRIMARY KEY,
        "code" VARCHAR(255) NOT NULL UNIQUE,
        "discount" DECIMAL(5,2) NOT NULL,
        "valid_from" TIMESTAMP NOT NULL,
        "valid_to" TIMESTAMP NOT NULL,
        "product_id" INTEGER NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE
      )
    `);

    // Create Payments table
    await queryRunner.query(`
      CREATE TABLE "payments" (
        "id" SERIAL PRIMARY KEY,
        "user_id" INTEGER NOT NULL,
        "product_id" INTEGER NOT NULL,
        "amount" DECIMAL(10,2) NOT NULL,
        "status" VARCHAR(50) NOT NULL,
        "coupon_id" INTEGER,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE,
        FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE,
        FOREIGN KEY ("coupon_id") REFERENCES "coupons"("id") ON DELETE SET NULL
      )
    `);

    // Create Sessions table
    await queryRunner.query(`
      CREATE TABLE "sessions" (
        "id" SERIAL PRIMARY KEY,
        "account_id" INTEGER NOT NULL,
        "user_id" INTEGER NOT NULL,
        "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
        "user_agent" VARCHAR(255) NOT NULL,
        "proxy" VARCHAR(255) NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        FOREIGN KEY ("account_id") REFERENCES "accounts"("id") ON DELETE CASCADE,
        FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE IF EXISTS "sessions" CASCADE');
    await queryRunner.query('DROP TABLE IF EXISTS "payments" CASCADE');
    await queryRunner.query('DROP TABLE IF EXISTS "coupons" CASCADE');
    await queryRunner.query('DROP TABLE IF EXISTS "products" CASCADE');
    await queryRunner.query('DROP TABLE IF EXISTS "accounts" CASCADE');
    await queryRunner.query('DROP TABLE IF EXISTS "users" CASCADE');
  }
}
