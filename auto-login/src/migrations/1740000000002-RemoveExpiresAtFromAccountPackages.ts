import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveExpiresAtFromAccountPackages1740000000002
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "account_packages" 
      DROP COLUMN IF EXISTS "expires_at"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "account_packages" 
      ADD COLUMN IF NOT EXISTS "expires_at" TIMESTAMP NOT NULL DEFAULT now()
    `);
  }
}
