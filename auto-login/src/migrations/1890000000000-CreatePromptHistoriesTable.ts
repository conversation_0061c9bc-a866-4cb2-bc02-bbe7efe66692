import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePromptHistoriesTable1890000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create prompt_histories table
    await queryRunner.query(`
      CREATE TABLE "prompt_histories" (
        "id" SERIAL PRIMARY KEY,
        "user_id" INTEGER NOT NULL,
        "prompt_id" INTEGER,
        "generated_result" TEXT NOT NULL,
        "model" VARCHAR(255) NOT NULL,
        "usage" JSONB,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "fk_prompt_histories_user"
          FOREIGN KEY ("user_id")
          REFERENCES "users"("id")
          ON DELETE CASCADE,
        CONSTRAINT "fk_prompt_histories_prompt"
          FOREIGN KEY ("prompt_id")
          REFERENCES "prompts"("id")
          ON DELETE SET NULL
      )
    `);

    // Create indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "idx_prompt_histories_user_id"
      ON "prompt_histories"("user_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_prompt_histories_prompt_id"
      ON "prompt_histories"("prompt_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_prompt_histories_created_at"
      ON "prompt_histories"("created_at")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_prompt_histories_created_at"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_prompt_histories_prompt_id"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_prompt_histories_user_id"`,
    );

    // Drop table
    await queryRunner.query(`DROP TABLE IF EXISTS "prompt_histories"`);
  }
}
