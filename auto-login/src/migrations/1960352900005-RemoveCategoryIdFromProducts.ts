import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveCategoryIdFromProducts1960352900005
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove the foreign key constraint first
    await queryRunner.query(`
      ALTER TABLE "products" DROP CONSTRAINT IF EXISTS "FK_products_category_id"
    `);

    // Drop the category_id column
    await queryRunner.query(`
      ALTER TABLE "products" DROP COLUMN IF EXISTS "category_id"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back the category_id column
    await queryRunner.query(`
      ALTER TABLE "products" ADD COLUMN "category_id" INTEGER
    `);

    // Add back the foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "products" 
      ADD CONSTRAINT "FK_products_category_id" 
      FOREIGN KEY ("category_id") REFERENCES "categories"("id")
    `);

    // Restore category_id values from product_categories table
    // Take the first category if a product has multiple categories
    await queryRunner.query(`
      UPDATE "products" p
      SET "category_id" = (
        SELECT pc."category_id"
        FROM "product_categories" pc
        WHERE pc."product_id" = p."id"
        ORDER BY pc."id" ASC
        LIMIT 1
      )
      WHERE EXISTS (
        SELECT 1 FROM "product_categories" pc2
        WHERE pc2."product_id" = p."id"
      )
    `);
  }
}
