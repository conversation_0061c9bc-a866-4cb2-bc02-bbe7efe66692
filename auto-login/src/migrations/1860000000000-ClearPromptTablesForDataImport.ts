import { MigrationInterface, QueryRunner } from 'typeorm';

export class ClearPromptTablesForDataImport1860000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Clear existing data from prompt tables to avoid conflicts
    // This should be run before importing new data

    console.log('Clearing existing prompt data for fresh import...');

    // Delete in reverse order due to foreign key constraints
    await queryRunner.query(`
      DELETE FROM "prompts";
    `);

    await queryRunner.query(`
      DELETE FROM "topics";
    `);

    await queryRunner.query(`
      DELETE FROM "prompt_categories";
    `);

    // Reset sequences to 1
    await queryRunner.query(`
      ALTER SEQUENCE "prompts_id_seq" RESTART WITH 1;
    `);

    await queryRunner.query(`
      ALTER SEQUENCE "topics_id_seq" RESTART WITH 1;
    `);

    await queryRunner.query(`
      ALTER SEQUENCE "prompt_categories_id_seq" RESTART WITH 1;
    `);

    console.log('Prompt tables cleared and sequences reset.');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // This migration is destructive, we can't really undo it
    // Just reset sequences back to 1
    await queryRunner.query(`
      ALTER SEQUENCE "prompts_id_seq" RESTART WITH 1;
    `);

    await queryRunner.query(`
      ALTER SEQUENCE "topics_id_seq" RESTART WITH 1;
    `);

    await queryRunner.query(`
      ALTER SEQUENCE "prompt_categories_id_seq" RESTART WITH 1;
    `);

    console.log('Sequences reset - data cannot be restored');
  }
}
