import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePromptVideoHistoryTable1900000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create prompt_video_histories table
    await queryRunner.query(`
      CREATE TABLE "prompt_video_histories" (
        "id" SERIAL PRIMARY KEY,
        "user_id" INTEGER NOT NULL,
        "prompt_data" JSONB NOT NULL,
        "generated_result" TEXT NOT NULL,
        "model" VARCHAR(255) NOT NULL DEFAULT 'gpt-4',
        "usage" JSONB,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "fk_prompt_video_histories_user"
          FOREIGN KEY ("user_id") 
          REFERENCES "users"("id") 
          ON DELETE CASCADE
      )
    `);

    // Create indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "idx_prompt_video_histories_user_id" 
      ON "prompt_video_histories"("user_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_prompt_video_histories_created_at" 
      ON "prompt_video_histories"("created_at")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_prompt_video_histories_created_at"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_prompt_video_histories_user_id"`,
    );

    // Drop table
    await queryRunner.query(`DROP TABLE IF EXISTS "prompt_video_histories"`);
  }
}
