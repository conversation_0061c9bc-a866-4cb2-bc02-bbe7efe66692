import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPkImgToPackages1940000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add pk_img column to packages table
    await queryRunner.query(`
      ALTER TABLE "packages" 
      ADD COLUMN "pk_img" TEXT
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove pk_img column from packages table
    await queryRunner.query(`
      ALTER TABLE "packages" 
      DROP COLUMN IF EXISTS "pk_img"
    `);
  }
}
