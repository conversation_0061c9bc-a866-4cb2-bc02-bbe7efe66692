import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateProductDurationsTable1950000000001
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create product_durations table
    await queryRunner.query(`
      CREATE TABLE "product_durations" (
        "id" SERIAL PRIMARY KEY,
        "product_id" INTEGER NOT NULL,
        "duration_days" INTEGER NOT NULL,
        "original_price" DECIMAL(10,2) NOT NULL,
        "discount_price" DECIMAL(10,2) DEFAULT 0,
        "quantity" INTEGER DEFAULT 0,
        "discount_percent" DECIMAL(5,2) DEFAULT 0,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE
      )
    `);

    // Add index for better performance
    await queryRunner.query(`
      CREATE INDEX "idx_product_durations_product_id" ON "product_durations"("product_id");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Restore data back to products table before dropping product_durations
    await queryRunner.query(`
      UPDATE products p
      SET
        original_price = (SELECT original_price FROM product_durations pd WHERE pd.product_id = p.id ORDER BY pd.id LIMIT 1),
        discount_price = (SELECT discount_price FROM product_durations pd WHERE pd.product_id = p.id ORDER BY pd.id LIMIT 1),
        quantity = (SELECT quantity FROM product_durations pd WHERE pd.product_id = p.id ORDER BY pd.id LIMIT 1)
      WHERE EXISTS (SELECT 1 FROM product_durations pd WHERE pd.product_id = p.id)
    `);

    // Drop index
    await queryRunner.query(`
      DROP INDEX IF EXISTS "idx_product_durations_product_id";
    `);

    // Drop product_durations table
    await queryRunner.query(`
      DROP TABLE IF EXISTS "product_durations" CASCADE
    `);
  }
}
