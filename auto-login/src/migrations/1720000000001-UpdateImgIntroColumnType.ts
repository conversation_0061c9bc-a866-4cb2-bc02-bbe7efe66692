import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateImgIntroColumnType1720000000001
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Modify img_intro column to use TEXT data type which can store large Base64 strings
    await queryRunner.query(`
      ALTER TABLE "accounts" 
      ALTER COLUMN "img_intro" TYPE TEXT
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert back to VARCHAR(255)
    await queryRunner.query(`
      ALTER TABLE "accounts" 
      ALTER COLUMN "img_intro" TYPE VARCHAR(255)
    `);
  }
}
