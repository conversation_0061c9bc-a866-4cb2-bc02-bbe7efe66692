import { MigrationInterface, QueryRunner } from 'typeorm';
import * as bcrypt from 'bcrypt';

export class SeedInitialData1703664100000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const hashedPassword = await bcrypt.hash('password123', 10);

    // Insert test users
    await queryRunner.query(`
      INSERT INTO users (email, password, status, discord_id)
      VALUES 
        ('<EMAIL>', '${hashedPassword}', 'active', '123456789'),
        ('<EMAIL>', '${hashedPassword}', 'active', '987654321'),
        ('<EMAIL>', '${hashedPassword}', 'inactive', null);
    `);

    // Insert test products
    await queryRunner.query(`
      INSERT INTO products (name, description, price)
      VALUES 
        ('Basic Plan', 'Basic auto-login service', 9.99),
        ('Premium Plan', 'Premium auto-login service with priority support', 19.99),
        ('Enterprise Plan', 'Custom auto-login solution for businesses', 49.99);
    `);

    // Insert test coupons
    await queryRunner.query(`
      INSERT INTO coupons (code, discount, product_id, valid_from, valid_to)
      VALUES 
        ('WELCOME10', 10.00, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days'),
        ('PREMIUM20', 20.00, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM coupons;
      DELETE FROM products;
      DELETE FROM users;
    `);
  }
}
