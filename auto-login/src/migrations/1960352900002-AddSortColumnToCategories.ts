import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSortColumnToCategories1960352900002
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add sort column to categories table
    await queryRunner.query(`
      ALTER TABLE "categories"
      ADD COLUMN "sort" INTEGER NOT NULL DEFAULT 0
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the column if migration is reverted
    await queryRunner.query(`
      ALTER TABLE "categories"
      DROP COLUMN "sort"
    `);
  }
}
