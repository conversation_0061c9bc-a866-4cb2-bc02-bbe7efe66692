import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateProductCategoriesTable1960352900004
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "product_categories" (
        "id" SERIAL PRIMARY KEY,
        "product_id" INTEGER NOT NULL,
        "category_id" INTEGER NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "FK_product_categories_product_id" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE,
        CONSTRAINT "FK_product_categories_category_id" FOREIGN KEY ("category_id") REFERENCES "categories"("id") ON DELETE CASCADE,
        CONSTRAINT "UQ_product_category" UNIQUE ("product_id", "category_id")
      )
    `);

    // Migrate existing product-category relationships
    await queryRunner.query(`
      INSERT INTO "product_categories" ("product_id", "category_id", "created_at", "updated_at")
      SELECT "id", "category_id", "created_at", "updated_at"
      FROM "products"
      WHERE "category_id" IS NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Restore category_id in products table
    await queryRunner.query(`
      UPDATE "products" p
      SET "category_id" = pc."category_id"
      FROM "product_categories" pc
      WHERE p."id" = pc."product_id"
      AND NOT EXISTS (
        SELECT 1 FROM "product_categories" pc2
        WHERE pc2."product_id" = pc."product_id"
        AND pc2."id" != pc."id"
      )
    `);

    await queryRunner.query(`DROP TABLE "product_categories"`);
  }
}
