import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePromptCategoriesTranslations1870000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create prompt_categories_translations table
    await queryRunner.query(`
      CREATE TABLE "prompt_categories_translations" (
        "id" SERIAL PRIMARY KEY,
        "category_id" INTEGER NOT NULL,
        "lang" VARCHAR(10) NOT NULL,
        "title" VARCHAR(255) NOT NULL,
        "short_description" TEXT,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "fk_prompt_categories_translations_category"
          FOREIGN KEY ("category_id") 
          REFERENCES "prompt_categories"("id") 
          ON DELETE CASCADE,
        CONSTRAINT "uq_prompt_categories_translations_category_lang"
          UNIQUE ("category_id", "lang")
      )
    `);

    // Create indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "idx_prompt_categories_translations_category_id" ON "prompt_categories_translations"("category_id");
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_prompt_categories_translations_lang" ON "prompt_categories_translations"("lang");
    `);

    console.log('Created prompt_categories_translations table with indexes');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_prompt_categories_translations_lang"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_prompt_categories_translations_category_id"`,
    );

    // Drop table
    await queryRunner.query(
      `DROP TABLE IF EXISTS "prompt_categories_translations"`,
    );

    console.log('Dropped prompt_categories_translations table and indexes');
  }
}
