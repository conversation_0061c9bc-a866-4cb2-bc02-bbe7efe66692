import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePackageDiscounts1960352900007 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create package_discounts table
    await queryRunner.query(`
      CREATE TABLE "package_discounts" (
        "id" SERIAL NOT NULL,
        "package_id" INTEGER NOT NULL,
        "payment_method_id" INTEGER NOT NULL,
        "discount_percent" DECIMAL(5,2) NOT NULL DEFAULT '0',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_package_discounts_package_payment" UNIQUE ("package_id", "payment_method_id"),
        CONSTRAINT "PK_package_discounts" PRIMARY KEY ("id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "package_discounts" 
      ADD CONSTRAINT "FK_package_discounts_package" 
      FOREIGN KEY ("package_id") REFERENCES "packages"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "package_discounts" 
      ADD CONSTRAINT "FK_package_discounts_payment_method" 
      FOREIGN KEY ("payment_method_id") REFERENCES "payment_methods"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints first
    await queryRunner.query(`
      ALTER TABLE "package_discounts" DROP CONSTRAINT "FK_package_discounts_payment_method"
    `);

    await queryRunner.query(`
      ALTER TABLE "package_discounts" DROP CONSTRAINT "FK_package_discounts_package"
    `);

    // Drop table
    await queryRunner.query(`DROP TABLE "package_discounts"`);
  }
}
