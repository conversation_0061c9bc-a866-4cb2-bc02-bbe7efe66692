import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAccountProductsTable1950000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create account_products table (many-to-many relationship between accounts and products)
    await queryRunner.query(`
      CREATE TABLE "account_products" (
        "account_id" INTEGER NOT NULL,
        "product_id" INTEGER NOT NULL,
        "assigned_at" TIMESTAMP NOT NULL DEFAULT now(),
        PRIMARY KEY ("account_id", "product_id"),
        FOREIGN KEY ("account_id") REFERENCES "accounts"("id") ON DELETE CASCADE,
        FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE
      )
    `);

    // Add indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "idx_account_products_account_id" ON "account_products"("account_id");
      CREATE INDEX "idx_account_products_product_id" ON "account_products"("product_id");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`
      DROP INDEX IF EXISTS "idx_account_products_account_id";
      DROP INDEX IF EXISTS "idx_account_products_product_id";
    `);

    // Drop account_products table
    await queryRunner.query(`
      DROP TABLE IF EXISTS "account_products" CASCADE
    `);
  }
}
