import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropUnnecessaryTables1730000100000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop cookie_allowed_members table
    await queryRunner.query(`
      DROP TABLE IF EXISTS "cookie_allowed_members" CASCADE
    `);

    // Drop cookies table
    await queryRunner.query(`
      DROP TABLE IF EXISTS "cookies" CASCADE
    `);

    // We no longer drop account_packages table as it's used for the many-to-many relationship
    // between accounts and packages
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Recreate account_packages table
    await queryRunner.query(`
      CREATE TABLE "account_packages" (
        "account_id" INTEGER NOT NULL,
        "package_id" INTEGER NOT NULL,
        "assigned_at" TIMESTAMP NOT NULL DEFAULT now(),
        "expires_at" TIMESTAMP NOT NULL,
        PRIMARY KEY ("account_id", "package_id"),
        FOREI<PERSON>N KEY ("account_id") REFERENCES "accounts"("id") ON DELETE CASCADE,
        FOREIG<PERSON> KEY ("package_id") REFERENCES "packages"("id") ON DELETE CASCADE
      )
    `);

    // Recreate cookies table
    await queryRunner.query(`
      CREATE TABLE "cookies" (
        "id" SERIAL PRIMARY KEY,
        "website_url" VARCHAR(255) NOT NULL,
        "encrypted_cookie" TEXT NOT NULL,
        "last_updated" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // Recreate cookie_allowed_members table
    await queryRunner.query(`
      CREATE TABLE "cookie_allowed_members" (
        "cookieId" INTEGER NOT NULL,
        "userId" INTEGER NOT NULL,
        PRIMARY KEY ("cookieId", "userId"),
        FOREIGN KEY ("cookieId") REFERENCES "cookies"("id") ON DELETE CASCADE,
        FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE
      )
    `);

    // Migrate data back from accounts to cookies
    await queryRunner.query(`
      INSERT INTO "cookies" ("website_url", "encrypted_cookie")
      SELECT "website_url", "cookie_data"
      FROM "accounts"
      WHERE "cookie_data" IS NOT NULL
    `);

    // We no longer need to migrate data back to account_packages as it's now maintained
    // through the entity relationships
  }
}
