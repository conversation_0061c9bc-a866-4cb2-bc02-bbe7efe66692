import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';

config();

const configService = new ConfigService();

const dataSource = new DataSource({
  type: 'postgres',
  host: configService.get('database.host') || 'localhost',
  port: configService.get('database.port')
    ? parseInt(configService.get('database.port'))
    : 5432,
  username: configService.get('database.username') || 'postgres',
  password: configService.get('database.password') || 'postgres',
  database: configService.get('database.database') || 'auto_login',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  migrations: [__dirname + '/../migrations/*{.ts,.js}'],
  migrationsRun: false,
  synchronize: false,
});
export default dataSource;
