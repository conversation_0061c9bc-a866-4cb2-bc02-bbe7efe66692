// getRedisConfig.ts
import { RedisOptions } from 'ioredis';
import { ConfigService } from '@nestjs/config';

export const getRedisConfig = (configService: ConfigService): RedisOptions => ({
  host: configService.get('REDIS_HOST') || 'localhost',
  port: parseInt(configService.get('REDIS_PORT') || '6379', 10),
  password: configService.get('REDIS_PASSWORD'),
  // add additional properties as desired
});
