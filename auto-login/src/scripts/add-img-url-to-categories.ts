import { DataSource } from 'typeorm';
import { config } from 'dotenv';

// Load environment variables
config();

// Create a new DataSource
const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: process.env.DB_DATABASE || 'kitsify',
  synchronize: false,
  logging: true,
});

async function main() {
  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log('Data source initialized');

    // Execute the SQL query to add the img_url column
    await AppDataSource.query(
      `ALTER TABLE "categories" ADD COLUMN IF NOT EXISTS "img_url" varchar(255) NULL`,
    );
    console.log('Added img_url column to categories table');

    // Close the connection
    await AppDataSource.destroy();
    console.log('Connection closed');
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
