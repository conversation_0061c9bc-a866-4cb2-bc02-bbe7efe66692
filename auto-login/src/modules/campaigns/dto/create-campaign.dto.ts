import {
  IsString,
  IsEnum,
  IsOptional,
  IsDateString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { TargetAudience } from '../entities/campaign.entity';

export class CreateCampaignDto {
  @IsString()
  @MinLength(1)
  @MaxLength(255)
  name: string;

  @IsString()
  @MinLength(1)
  @MaxLength(500)
  subject: string;

  @IsString()
  @MinLength(1)
  html_template: string;

  @IsEnum(TargetAudience)
  target_audience: TargetAudience;

  @IsOptional()
  @IsDateString()
  scheduled_at?: string;
}
