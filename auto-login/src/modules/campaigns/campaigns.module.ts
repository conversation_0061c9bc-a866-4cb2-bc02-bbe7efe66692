import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { MailerModule } from '@nestjs-modules/mailer';

import { CampaignsService } from './campaigns.service';
import {
  CampaignsController,
  CampaignTrackingController,
} from './campaigns.controller';
import { Campaign } from './entities/campaign.entity';
import { CampaignRecipient } from './entities/campaign-recipient.entity';
import { User } from '../users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Campaign, CampaignRecipient, User]),
    ScheduleModule.forRoot(),
    MailerModule,
  ],
  controllers: [CampaignsController, CampaignTrackingController],
  providers: [CampaignsService],
  exports: [CampaignsService],
})
export class CampaignsModule {}
