import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { CampaignRecipient } from './campaign-recipient.entity';

export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  SENDING = 'sending',
  SENT = 'sent',
  CANCELLED = 'cancelled',
}

export enum TargetAudience {
  ALL = 'all',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  TRIAL = 'trial',
  EXCLUDE_TRIAL = 'exclude_trial',
  EXCLUDE_ACTIVE = 'exclude_active',
  EXCLUDE_INACTIVE = 'exclude_inactive',
}

@Entity('campaigns')
export class Campaign {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  name: string;

  @Column({ length: 500 })
  subject: string;

  @Column({ type: 'text' })
  html_template: string;

  @Column({
    type: 'enum',
    enum: TargetAudience,
    default: TargetAudience.ALL,
  })
  target_audience: TargetAudience;

  @Column({
    type: 'enum',
    enum: CampaignStatus,
    default: CampaignStatus.DRAFT,
  })
  status: CampaignStatus;

  @Column({ type: 'timestamp', nullable: true })
  scheduled_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  sent_at: Date;

  @Column({ type: 'integer', default: 0 })
  total_recipients: number;

  @Column({ type: 'integer', default: 0 })
  sent_count: number;

  @Column({ type: 'integer', default: 0 })
  failed_count: number;

  @Column()
  created_by: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @OneToMany(() => CampaignRecipient, (recipient) => recipient.campaign)
  recipients: CampaignRecipient[];
}
