import { Controller, Post, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import {
  PromptTranslationCronService,
  TranslationStats,
} from './services/prompt-translation-cron.service';

@Controller('admin/prompt-translation')
@UseGuards(JwtAuthGuard)
export class PromptTranslationCronController {
  constructor(
    private readonly promptTranslationCronService: PromptTranslationCronService,
  ) {}

  /**
   * Manually trigger prompt translation job
   * POST /admin/prompt-translation/trigger
   */
  @Post('trigger')
  async triggerTranslation(): Promise<{
    success: boolean;
    message: string;
    stats?: TranslationStats;
    error?: string;
  }> {
    try {
      const stats =
        await this.promptTranslationCronService.triggerManualTranslation();
      return {
        success: true,
        message: 'Translation job completed successfully',
        stats,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        error: error.stack,
      };
    }
  }

  /**
   * Get translation job status with detailed statistics
   * GET /admin/prompt-translation/status
   */
  @Get('status')
  async getStatus() {
    const status = await this.promptTranslationCronService.getStatus();
    return {
      success: true,
      data: status,
    };
  }

  /**
   * Health check endpoint
   * GET /admin/prompt-translation/health
   */
  @Get('health')
  async healthCheck() {
    return {
      success: true,
      message: 'Prompt Translation Cron Service is healthy',
      timestamp: new Date().toISOString(),
    };
  }
}
