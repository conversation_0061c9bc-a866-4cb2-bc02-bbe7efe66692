import { IsNotEmpty, IsString, IsOptional, IsObject } from 'class-validator';

export class GeneratePromptVideoDto {
  @IsNotEmpty()
  @IsObject()
  prompt_data: {
    location?: string;
    time?: string;
    weather?: string;
    lightingDetails?: string;
    characters?: any[];
    visualStyle?: string[];
    lightingStyle?: string[];
    dominantColors?: string[];
    postProcessingEffects?: string[];
    motionEffects?: string[];
    creativeEffects?: string[];
    surrealElements?: string;
    composition?: string[];
    cameraAngle?: string[];
    cameraMotion?: string[];
    focusPoint?: string[];
    editingPace?: string[];
    transitions?: string[];
    mood?: string[];
    backgroundMusic?: string[];
    soundEffects?: string[];
    typography?: string;
    subtitleInstructions?: string[];
    resolution?: string[];
    aspectRatio?: string[];
    duration?: string[];
  };

  @IsOptional()
  @IsString()
  model?: string;
}
