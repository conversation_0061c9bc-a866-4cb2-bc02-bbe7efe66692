import { IsNotEmpty, IsString, IsOptional, IsObject } from 'class-validator';

export class CreatePromptVideoHistoryDto {
  @IsNotEmpty()
  @IsObject()
  prompt_data: any;

  @IsNotEmpty()
  @IsString()
  generated_result: string;

  @IsOptional()
  @IsString()
  model?: string;

  @IsOptional()
  @IsObject()
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}
