import { IsString, IsOptional, IsBoolean, IsN<PERSON>ber } from 'class-validator';

export class CreatePromptCategoryDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  image_url?: string;

  @IsOptional()
  @IsString()
  image_card_url?: string;

  @IsOptional()
  @IsNumber()
  prompt_count?: number;

  @IsOptional()
  @IsBoolean()
  is_coming_soon?: boolean;
}
