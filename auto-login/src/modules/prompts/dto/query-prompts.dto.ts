import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Min } from 'class-validator';
import { Transform } from 'class-transformer';

export class QueryPromptsDto {
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  pageSize?: number = 12;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsString()
  search_text?: string;

  @IsOptional()
  @IsString()
  lang?: string;
}
