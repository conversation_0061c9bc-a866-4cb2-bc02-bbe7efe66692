import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class AnalyzeContextImageDto {
  @IsNotEmpty()
  @IsString()
  image_data: string; // Base64 encoded image data

  @IsNotEmpty()
  @IsString()
  mime_type: string; // Image MIME type (e.g., 'image/jpeg', 'image/png')

  @IsOptional()
  @IsString()
  model?: string; // OpenRouter model to use for analysis
}

export interface ContextAnalysisResult {
  location: string;
  time: string;
  weather: string;
  lightingDetails: string;
}

export class AnalyzeCharacterImageDto {
  @IsNotEmpty()
  @IsString()
  image_data: string; // Base64 encoded image data

  @IsNotEmpty()
  @IsString()
  mime_type: string; // Image MIME type (e.g., 'image/jpeg', 'image/png')

  @IsOptional()
  @IsString()
  model?: string; // OpenRouter model to use for analysis
}

export interface CharacterAnalysisResult {
  description: string;
  appearance: string;
  emotion: string;
  mainAction: string;
  relatedObject: string;
  dialogue: string;
}
