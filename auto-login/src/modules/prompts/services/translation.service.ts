import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

export interface TranslationRequest {
  title: string;
  short_description?: string;
  targetLanguage: string;
}

export interface TranslationResponse {
  title: string;
  short_description?: string;
}

export interface FullTranslationRequest {
  title: string;
  short_description?: string;
  optimization_guide?: string;
  targetLanguage: string;
}

export interface FullTranslationResponse {
  title: string;
  short_description?: string;
  optimization_guide?: string;
}

@Injectable()
export class TranslationService {
  private readonly logger = new Logger(TranslationService.name);
  private readonly openRouterApiKey = process.env.OPENROUTER_API_KEY;
  private readonly openRouterUrl =
    'https://openrouter.ai/api/v1/chat/completions';

  // Use only the deepseek model for translations
  private readonly translationModel = 'openai/gpt-4o-mini';

  constructor() {
    if (!this.openRouterApiKey) {
      this.logger.warn('OPENROUTER_API_KEY not found in environment variables');
    }
  }

  async translatePromptFields(
    request: TranslationRequest,
  ): Promise<TranslationResponse> {
    if (!this.openRouterApiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    const languageNames = {
      en: 'English',
      th: 'Thai',
      fr: 'French',
      vi: 'Vietnamese',
    };

    const targetLanguageName =
      languageNames[request.targetLanguage] || request.targetLanguage;

    // Create the translation prompt
    const translationPrompt = this.buildTranslationPrompt(
      request,
      targetLanguageName,
    );

    // Use the single deepseek model for translation
    try {
      this.logger.log(
        `Attempting translation with model: ${this.translationModel}`,
      );

      const response = await axios.post(
        this.openRouterUrl,
        {
          model: this.translationModel,
          messages: [
            {
              role: 'user',
              content: translationPrompt,
            },
          ],
          temperature: 0.3, // Lower temperature for more consistent translations
          max_tokens: 2048,
        },
        {
          headers: {
            Authorization: `Bearer ${this.openRouterApiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.APP_URL || 'http://localhost:3000',
            'X-Title': 'Kitsify Prompt Translation',
          },
          timeout: 30000, // 30 second timeout
        },
      );

      const responseData = response.data as any;
      const translatedContent = responseData.choices?.[0]?.message?.content;
      if (!translatedContent) {
        throw new Error('No translation content received from OpenRouter');
      }

      this.logger.log(
        `Translation successful with model: ${this.translationModel}`,
      );
      return this.parseTranslationResponse(translatedContent);
    } catch (error) {
      this.logger.error(
        `Translation failed with model ${this.translationModel}:`,
        error.response?.data?.error?.message || error.message,
      );
      throw new Error(
        `Translation failed: ${error.response?.data?.error?.message || error.message}`,
      );
    }
  }

  private buildTranslationPrompt(
    request: TranslationRequest,
    targetLanguageName: string,
  ): string {
    let prompt = `You are a professional translator. Translate the following prompt fields from Vietnamese to ${targetLanguageName}.

CRITICAL INSTRUCTIONS:
1. Only translate the title and short_description fields
2. RESPOND ONLY with valid JSON - no explanations, no markdown code blocks, no additional text
3. Start your response directly with { and end with }
4. IMPORTANT: Properly escape all JSON special characters:
   - Escape quotes with \\"
   - Escape newlines with \\n
   - Escape backslashes with \\\\
   - Escape tabs with \\t

Fields to translate:
`;

    prompt += `\nTITLE: ${request.title}`;

    if (request.short_description) {
      prompt += `\nSHORT_DESCRIPTION: ${request.short_description}`;
    }

    prompt += `\n\nRESPOND WITH ONLY THIS JSON FORMAT (no other text):
{
  "title": "translated title here",
  "short_description": "translated short description here (or null if not provided)"
}

CRITICAL JSON REQUIREMENTS:
- Do NOT wrap the JSON in markdown code blocks
- Do NOT add any explanatory text before or after the JSON
- Do NOT include "Here is the translation" or similar phrases
- Start your response immediately with the opening brace {
- ALL strings must be properly escaped for JSON:
  * Replace " with \"
  * Replace newlines with \\n
  * Replace tabs with \\t
  * Replace backslashes with \\\\
- Use null (not "null") for empty fields
- Ensure the JSON is valid and parseable`;

    return prompt;
  }

  async translatePromptFieldsWithOptimizationGuide(
    request: FullTranslationRequest,
  ): Promise<FullTranslationResponse> {
    if (!this.openRouterApiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    const languageNames = {
      en: 'English',
      vi: 'Vietnamese',
    };

    const targetLanguageName =
      languageNames[request.targetLanguage] || request.targetLanguage;

    // Create the translation prompt
    const translationPrompt = this.buildFullTranslationPrompt(
      request,
      targetLanguageName,
    );

    // Use the single deepseek model for full translation
    try {
      this.logger.log(
        `Attempting full translation with model: ${this.translationModel}`,
      );

      const response = await axios.post(
        this.openRouterUrl,
        {
          model: this.translationModel,
          messages: [
            {
              role: 'user',
              content: translationPrompt,
            },
          ],
          temperature: 0.3, // Lower temperature for more consistent translations
          max_tokens: 4096, // Increased for optimization_guide
        },
        {
          headers: {
            Authorization: `Bearer ${this.openRouterApiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.APP_URL || 'http://localhost:3000',
            'X-Title': 'Kitsify Full Prompt Translation',
          },
          timeout: 60000, // Increased timeout for longer content
        },
      );

      const responseData = response.data as any;
      const translatedContent = responseData.choices?.[0]?.message?.content;
      if (!translatedContent) {
        throw new Error('No translation content received from OpenRouter');
      }

      this.logger.log(
        `Full translation successful with model: ${this.translationModel}`,
      );
      return this.parseFullTranslationResponse(translatedContent);
    } catch (error) {
      this.logger.error(
        `Full translation failed with model ${this.translationModel}:`,
        error.response?.data?.error?.message || error.message,
      );
      throw new Error(
        `Full translation failed: ${error.response?.data?.error?.message || error.message}`,
      );
    }
  }

  private buildFullTranslationPrompt(
    request: FullTranslationRequest,
    targetLanguageName: string,
  ): string {
    let prompt = `You are a professional translator. Translate the following prompt fields from Vietnamese to ${targetLanguageName}.

CRITICAL INSTRUCTIONS:
1. Translate the title, short_description, and optimization_guide fields
2. For optimization_guide, preserve ALL HTML structure and formatting (HTML tags, attributes, etc.)
3. IMPORTANT: Keep all newline characters (\\n) exactly as they are in the original HTML content
4. RESPOND ONLY with valid JSON - no explanations, no markdown code blocks, no additional text
5. Start your response directly with { and end with }
6. IMPORTANT: Properly escape all JSON special characters:
   - Escape quotes with \\"
   - Escape newlines with \\n
   - Escape backslashes with \\\\
   - Escape tabs with \\t

Fields to translate:
`;

    prompt += `\nTITLE: ${request.title}`;

    if (request.short_description) {
      prompt += `\nSHORT_DESCRIPTION: ${request.short_description}`;
    }

    if (request.optimization_guide) {
      prompt += `\nOPTIMIZATION_GUIDE: ${request.optimization_guide}`;
    }

    prompt += `\n\nRESPOND WITH ONLY THIS JSON FORMAT (no other text):
{
  "title": "translated title here",
  "short_description": "translated short description here (or null if not provided)",
  "optimization_guide": "translated optimization guide here with preserved HTML structure and \\n characters (or null if not provided)"
}

CRITICAL JSON REQUIREMENTS:
- Do NOT wrap the JSON in markdown code blocks
- Do NOT add any explanatory text before or after the JSON
- Do NOT include "Here is the translation" or similar phrases
- Start your response immediately with the opening brace {
- ALL strings must be properly escaped for JSON:
  * Replace " with \"
  * Replace newlines with \\n
  * Replace tabs with \\t
  * Replace backslashes with \\\\
- Use null (not "null") for empty fields
- Ensure the JSON is valid and parseable
- For optimization_guide: Keep all HTML tags intact (<p>, <div>, etc.) and preserve \\n characters exactly as they appear`;

    return prompt;
  }

  private parseTranslationResponse(content: string): TranslationResponse {
    try {
      this.logger.debug('Raw translation response:', content);

      // Clean the content by removing markdown code blocks and extra text
      let cleanedContent = content.trim();

      // Remove markdown code blocks (```json or ``` at start/end)
      cleanedContent = cleanedContent.replace(/^```(?:json)?\s*\n?/gm, '');
      cleanedContent = cleanedContent.replace(/\n?```\s*$/gm, '');

      // Remove any leading text before the JSON (like "Here is the translation...")
      const jsonStartIndex = cleanedContent.indexOf('{');
      if (jsonStartIndex > 0) {
        cleanedContent = cleanedContent.substring(jsonStartIndex);
      }

      // Find the last closing brace to handle incomplete JSON
      const jsonEndIndex = cleanedContent.lastIndexOf('}');
      if (jsonEndIndex > 0) {
        cleanedContent = cleanedContent.substring(0, jsonEndIndex + 1);
      }

      this.logger.debug('Cleaned content for parsing:', cleanedContent);

      // Try to parse the cleaned JSON
      const parsed = JSON.parse(cleanedContent);

      // Validate required fields
      if (!parsed.title) {
        throw new Error('Missing required field: title');
      }

      return {
        title: parsed.title || '',
        short_description: parsed.short_description || null,
      };
    } catch (error) {
      this.logger.error('Failed to parse translation response:', {
        error: error.message,
        content: content.substring(0, 500) + '...', // Log first 500 chars
      });

      // Try alternative parsing methods
      return this.fallbackParseTranslation(content);
    }
  }

  private fallbackParseTranslation(content: string): TranslationResponse {
    this.logger.log('Attempting fallback parsing methods');

    try {
      // Method 1: Try to extract JSON using more flexible regex
      const jsonMatches = content.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g);
      if (jsonMatches && jsonMatches.length > 0) {
        // Try the largest JSON object found
        const largestJson = jsonMatches.reduce((a, b) =>
          a.length > b.length ? a : b,
        );

        this.logger.debug(
          'Fallback method 1 - trying largest JSON:',
          largestJson,
        );
        const parsed = JSON.parse(largestJson);

        if (parsed.title) {
          return {
            title: parsed.title || '',
            short_description: parsed.short_description || null,
          };
        }
      }

      // Method 2: Try to extract fields using regex patterns (handle multiline)
      const titleMatch = content.match(/"title"\s*:\s*"((?:[^"\\]|\\.)*)"/s);
      const shortDescMatch = content.match(
        /"short_description"\s*:\s*"((?:[^"\\]|\\.)*)"/s,
      );

      // Method 3: Handle null values
      const shortDescNullMatch = content.match(
        /"short_description"\s*:\s*null/,
      );

      if (titleMatch) {
        this.logger.log('Fallback method 2 - extracted fields using regex');
        return {
          title: titleMatch[1] || '',
          short_description: shortDescMatch
            ? shortDescMatch[1]
            : shortDescNullMatch
              ? null
              : null,
        };
      }

      throw new Error('All parsing methods failed');
    } catch (fallbackError) {
      this.logger.error('Fallback parsing also failed:', fallbackError.message);

      // Return a default response to prevent complete failure
      return {
        title: 'Translation Failed',
        short_description: 'Unable to parse AI translation response',
      };
    }
  }

  private parseFullTranslationResponse(
    content: string,
  ): FullTranslationResponse {
    try {
      this.logger.debug('Raw full translation response:', content);

      // Clean the content by removing markdown code blocks and extra text
      let cleanedContent = content.trim();

      // Remove markdown code blocks (```json or ``` at start/end)
      cleanedContent = cleanedContent.replace(/^```(?:json)?\s*\n?/gm, '');
      cleanedContent = cleanedContent.replace(/\n?```\s*$/gm, '');

      // Remove any leading text before the JSON (like "Here is the translation...")
      const jsonStartIndex = cleanedContent.indexOf('{');
      if (jsonStartIndex > 0) {
        cleanedContent = cleanedContent.substring(jsonStartIndex);
      }

      // Find the last closing brace to handle incomplete JSON
      const jsonEndIndex = cleanedContent.lastIndexOf('}');
      if (jsonEndIndex > 0) {
        cleanedContent = cleanedContent.substring(0, jsonEndIndex + 1);
      }

      this.logger.debug('Cleaned content for full parsing:', cleanedContent);

      // Try to parse the cleaned JSON
      const parsed = JSON.parse(cleanedContent);

      // Validate required fields
      if (!parsed.title) {
        throw new Error('Missing required field: title');
      }

      return {
        title: parsed.title || '',
        short_description: parsed.short_description || null,
        optimization_guide: parsed.optimization_guide || null,
      };
    } catch (error) {
      this.logger.error('Failed to parse full translation response:', {
        error: error.message,
        content: content.substring(0, 500) + '...', // Log first 500 chars
      });

      // Try alternative parsing methods
      return this.fallbackParseFullTranslation(content);
    }
  }

  private fallbackParseFullTranslation(
    content: string,
  ): FullTranslationResponse {
    this.logger.log('Attempting fallback full parsing methods');

    try {
      // Method 1: Try to extract JSON using more flexible regex
      const jsonMatches = content.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g);
      if (jsonMatches && jsonMatches.length > 0) {
        // Try the largest JSON object found
        const largestJson = jsonMatches.reduce((a, b) =>
          a.length > b.length ? a : b,
        );

        this.logger.debug(
          'Fallback method 1 - trying largest JSON:',
          largestJson,
        );
        const parsed = JSON.parse(largestJson);

        if (parsed.title) {
          return {
            title: parsed.title || '',
            short_description: parsed.short_description || null,
            optimization_guide: parsed.optimization_guide || null,
          };
        }
      }

      // Method 2: Try to extract fields using regex patterns (handle multiline)
      const titleMatch = content.match(/"title"\s*:\s*"((?:[^"\\]|\\.)*)"/s);
      const shortDescMatch = content.match(
        /"short_description"\s*:\s*"((?:[^"\\]|\\.)*)"/s,
      );
      const optimizationGuideMatch = content.match(
        /"optimization_guide"\s*:\s*"((?:[^"\\]|\\.)*)"/s,
      );

      // Method 3: Handle null values
      const shortDescNullMatch = content.match(
        /"short_description"\s*:\s*null/,
      );
      const optimizationGuideNullMatch = content.match(
        /"optimization_guide"\s*:\s*null/,
      );

      if (titleMatch) {
        this.logger.log('Fallback method 2 - extracted fields using regex');
        return {
          title: titleMatch[1] || '',
          short_description: shortDescMatch
            ? shortDescMatch[1]
            : shortDescNullMatch
              ? null
              : null,
          optimization_guide: optimizationGuideMatch
            ? optimizationGuideMatch[1]
            : optimizationGuideNullMatch
              ? null
              : null,
        };
      }

      throw new Error('All parsing methods failed');
    } catch (fallbackError) {
      this.logger.error(
        'Fallback full parsing also failed:',
        fallbackError.message,
      );

      // Return a default response to prevent complete failure
      return {
        title: 'Translation Failed',
        short_description: 'Unable to parse AI translation response',
        optimization_guide: null,
      };
    }
  }

  /**
   * Translate individual field for cronjob usage
   * This method translates a single field instead of using object request
   */
  async translateSingleField(
    fieldValue: string,
    fieldName: string,
    targetLanguage: string,
  ): Promise<string> {
    if (!this.openRouterApiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    if (!fieldValue || fieldValue.trim() === '') {
      return '';
    }

    const languageNames = {
      en: 'English',
      th: 'Thai',
      fr: 'French',
      vi: 'Vietnamese',
    };

    const targetLanguageName = languageNames[targetLanguage] || targetLanguage;

    // Create the translation prompt for single field
    const translationPrompt = this.buildSingleFieldTranslationPrompt(
      fieldValue,
      fieldName,
      targetLanguageName,
    );

    try {
      this.logger.log(
        `Attempting ${fieldName} translation with model: ${this.translationModel}`,
      );

      const response = await axios.post(
        this.openRouterUrl,
        {
          model: this.translationModel,
          messages: [
            {
              role: 'user',
              content: translationPrompt,
            },
          ],
          temperature: 0.3,
          max_tokens: fieldName === 'optimization_guide' ? 4096 : 2048,
        },
        {
          headers: {
            Authorization: `Bearer ${this.openRouterApiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.APP_URL || 'http://localhost:3000',
            'X-Title': `Kitsify ${fieldName} Translation`,
          },
          timeout: fieldName === 'optimization_guide' ? 60000 : 30000,
        },
      );

      const responseData = response.data as any;
      const translatedContent = responseData.choices?.[0]?.message?.content;
      if (!translatedContent) {
        throw new Error('No translation content received from OpenRouter');
      }

      this.logger.log(
        `${fieldName} translation successful with model: ${this.translationModel}`,
      );

      // Parse the response to extract the translated text
      return this.parseSingleFieldResponse(translatedContent);
    } catch (error) {
      this.logger.error(
        `${fieldName} translation failed with model ${this.translationModel}:`,
        error.response?.data?.error?.message || error.message,
      );
      throw new Error(
        `${fieldName} translation failed: ${error.response?.data?.error?.message || error.message}`,
      );
    }
  }

  /**
   * Build translation prompt for single field
   */
  private buildSingleFieldTranslationPrompt(
    fieldValue: string,
    fieldName: string,
    targetLanguageName: string,
  ): string {
    const fieldDisplayName =
      {
        title: 'Title',
        short_description: 'Short Description',
        optimization_guide: 'Optimization Guide',
      }[fieldName] || fieldName;

    let prompt = `You are a professional translator. Translate the following ${fieldDisplayName} from Vietnamese to ${targetLanguageName}.

CRITICAL INSTRUCTIONS:
1. Translate ONLY the provided text content
2. RESPOND ONLY with the translated text - no explanations, no quotes, no additional formatting
3. Do NOT add any prefixes like "Here is the translation:" or similar phrases
4. Start your response directly with the translated content`;

    if (fieldName === 'optimization_guide') {
      prompt += `
5. IMPORTANT: Preserve ALL HTML structure and formatting (HTML tags, attributes, etc.)
6. Keep all HTML tags intact (<p>, <div>, etc.) and preserve \\n characters exactly as they appear
7. Do NOT convert HTML to markdown or any other format`;
    }

    prompt += `

Content to translate:
${fieldValue}

REMEMBER: Respond with ONLY the translated content, nothing else.`;

    return prompt;
  }

  /**
   * Parse single field translation response
   */
  private parseSingleFieldResponse(content: string): string {
    try {
      // Clean the content by removing any extra formatting
      let cleanedContent = content.trim();

      // Remove any leading/trailing quotes if present
      if (
        (cleanedContent.startsWith('"') && cleanedContent.endsWith('"')) ||
        (cleanedContent.startsWith("'") && cleanedContent.endsWith("'"))
      ) {
        cleanedContent = cleanedContent.slice(1, -1);
      }

      // Remove any markdown code blocks if present
      cleanedContent = cleanedContent.replace(/^```(?:.*?)?\s*\n?/gm, '');
      cleanedContent = cleanedContent.replace(/\n?```\s*$/gm, '');

      return cleanedContent.trim();
    } catch (error) {
      this.logger.error('Failed to parse single field response:', {
        error: error.message,
        content: content.substring(0, 200) + '...',
      });
      return content.trim(); // Return original content if parsing fails
    }
  }

  /**
   * Bulk translate multiple prompts in a single API call for maximum efficiency
   * This is the most optimized method for translating lists of prompts
   */
  async translatePromptsBulk(
    prompts: Array<{ id: number; title: string; short_description: string }>,
    targetLanguage: string,
  ): Promise<Array<{ id: number; title: string; short_description: string }>> {
    if (!this.openRouterApiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    if (prompts.length === 0) {
      return [];
    }

    const languageNames = {
      en: 'English',
      th: 'Thai',
      fr: 'French',
      vi: 'Vietnamese',
    };

    const targetLanguageName = languageNames[targetLanguage] || targetLanguage;

    // Build bulk translation prompt
    const bulkPrompt = this.buildBulkTranslationPrompt(
      prompts,
      targetLanguageName,
    );

    try {
      this.logger.log(
        `Attempting bulk translation of ${prompts.length} prompts with model: ${this.translationModel}`,
      );

      const response = await axios.post(
        this.openRouterUrl,
        {
          model: this.translationModel,
          messages: [
            {
              role: 'user',
              content: bulkPrompt,
            },
          ],
          temperature: 0.3,
          max_tokens: Math.min(8192, prompts.length * 200), // Dynamic token limit
        },
        {
          headers: {
            Authorization: `Bearer ${this.openRouterApiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.APP_URL || 'http://localhost:3000',
            'X-Title': 'Kitsify Bulk Prompt Translation',
          },
          timeout: 60000, // Increased timeout for bulk operations
        },
      );

      const responseData = response.data as any;
      const translatedContent = responseData.choices?.[0]?.message?.content;
      if (!translatedContent) {
        throw new Error('No translation content received from OpenRouter');
      }

      this.logger.log(
        `Bulk translation successful for ${prompts.length} prompts with model: ${this.translationModel}`,
      );

      return this.parseBulkTranslationResponse(translatedContent, prompts);
    } catch (error) {
      this.logger.error(
        `Bulk translation failed with model ${this.translationModel}:`,
        error.response?.data?.error?.message || error.message,
      );
      throw new Error(
        `Bulk translation failed: ${error.response?.data?.error?.message || error.message}`,
      );
    }
  }

  private buildBulkTranslationPrompt(
    prompts: Array<{ id: number; title: string; short_description: string }>,
    targetLanguageName: string,
  ): string {
    const promptsJson = prompts.map((p) => ({
      id: p.id,
      title: p.title,
      short_description: p.short_description,
    }));

    return `You are a professional translator. Translate the following prompts from Vietnamese to ${targetLanguageName}.

CRITICAL INSTRUCTIONS:
1. Translate ONLY the title and short_description fields for each prompt
2. Keep the same ID for each prompt
3. RESPOND ONLY with valid JSON array - no explanations, no markdown code blocks, no additional text
4. Start your response directly with [ and end with ]
5. IMPORTANT: Properly escape all JSON special characters:
   - Escape quotes with \\"
   - Escape newlines with \\n
   - Escape backslashes with \\\\
   - Escape tabs with \\t
6. Maintain the exact same array structure and order

Input prompts to translate:
${JSON.stringify(promptsJson, null, 2)}

REMEMBER: Respond with ONLY the JSON array containing translated prompts, nothing else.`;
  }

  private parseBulkTranslationResponse(
    content: string,
    originalPrompts: Array<{
      id: number;
      title: string;
      short_description: string;
    }>,
  ): Array<{ id: number; title: string; short_description: string }> {
    try {
      this.logger.debug(
        'Raw bulk translation response:',
        content.substring(0, 500),
      );

      // Clean the content
      let cleanedContent = content.trim();

      // Remove markdown code blocks
      cleanedContent = cleanedContent.replace(/^```(?:json)?\s*\n?/gm, '');
      cleanedContent = cleanedContent.replace(/\n?```\s*$/gm, '');

      // Find JSON array boundaries
      const arrayStartIndex = cleanedContent.indexOf('[');
      const arrayEndIndex = cleanedContent.lastIndexOf(']');

      if (arrayStartIndex >= 0 && arrayEndIndex > arrayStartIndex) {
        cleanedContent = cleanedContent.substring(
          arrayStartIndex,
          arrayEndIndex + 1,
        );
      }

      const parsed = JSON.parse(cleanedContent);

      if (!Array.isArray(parsed)) {
        throw new Error('Response is not an array');
      }

      // Validate and map results
      const results = originalPrompts.map((original) => {
        const translated = parsed.find((p) => p.id === original.id);
        if (translated && translated.title) {
          return {
            id: original.id,
            title: translated.title,
            short_description:
              translated.short_description || original.short_description,
          };
        }
        // Fallback to original if translation failed
        return original;
      });

      this.logger.log(
        `Successfully parsed ${results.length} bulk translations`,
      );
      return results;
    } catch (error) {
      this.logger.error(
        'Failed to parse bulk translation response:',
        error.message,
      );
      // Fallback: return original prompts
      return originalPrompts;
    }
  }
}
