import { Injectable, Logger } from '@nestjs/common';
// import { Cron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Not } from 'typeorm';
import { Prompt } from '../entities/prompt.entity';
import { PromptTranslation } from '../entities/prompt-translation.entity';
import { TranslationService } from './translation.service';
import { ConfigService } from '@nestjs/config';

interface TranslationBatch {
  prompts: Prompt[];
  language: string;
}

export interface TranslationStats {
  totalPrompts: number;
  successfulTranslations: number;
  failedTranslations: number;
  skippedTranslations: number;
  processingTime: number;
}

@Injectable()
export class PromptTranslationCronService {
  private readonly logger = new Logger(PromptTranslationCronService.name);
  private isRunning = false;

  // Configuration from environment variables
  private readonly batchSize: number;
  private readonly maxRetries: number;
  private readonly retryDelay: number;
  private readonly supportedLanguages: string[];
  private readonly enableCron: boolean;
  private readonly parallelBatches: number;
  private readonly maxPromptsPerRun: number;
  private readonly useOptimizedBatching: boolean;

  constructor(
    @InjectRepository(Prompt)
    private readonly promptRepository: Repository<Prompt>,
    @InjectRepository(PromptTranslation)
    private readonly promptTranslationRepository: Repository<PromptTranslation>,
    private readonly translationService: TranslationService,
    private readonly configService: ConfigService,
    private readonly dataSource: DataSource,
  ) {
    // Load configuration with optimized defaults for large datasets
    this.batchSize = parseInt(
      this.configService.get('TRANSLATION_BATCH_SIZE', '50'), // Increased from 10 to 50
    );
    this.maxRetries = parseInt(
      this.configService.get('TRANSLATION_MAX_RETRIES', '3'),
    );
    this.retryDelay = parseInt(
      this.configService.get('TRANSLATION_RETRY_DELAY', '3000'), // Reduced from 5000 to 3000
    );
    this.supportedLanguages = (
      this.configService.get('TRANSLATION_LANGUAGES', 'en,th') || 'en'
    ).split(',');
    this.enableCron =
      this.configService.get('ENABLE_TRANSLATION_CRON', 'true') === 'true';
    this.parallelBatches = parseInt(
      this.configService.get('TRANSLATION_PARALLEL_BATCHES', '6'), // Increased from 3 to 6
    );
    this.maxPromptsPerRun = parseInt(
      this.configService.get('TRANSLATION_MAX_PROMPTS_PER_RUN', '1000'), // Process 1000 prompts per run
    );
    this.useOptimizedBatching =
      this.configService.get('TRANSLATION_USE_OPTIMIZED_BATCHING', 'true') ===
      'true';

    this.logger.log(`Translation Cron Service initialized with config:`, {
      batchSize: this.batchSize,
      maxRetries: this.maxRetries,
      retryDelay: this.retryDelay,
      supportedLanguages: this.supportedLanguages,
      enableCron: this.enableCron,
      parallelBatches: this.parallelBatches,
      maxPromptsPerRun: this.maxPromptsPerRun,
      useOptimizedBatching: this.useOptimizedBatching,
    });
  }

  // Production schedule (uncomment for production use):
  // @Cron('0 0 2 * * *', {
  //   name: 'prompt-translation',
  //   timeZone: 'Asia/Ho_Chi_Minh',
  // })
  // run at 11:16:00 AM every day
  // @Cron('0 34 16 * * *', {
  //   name: 'prompt-translation',
  //   timeZone: 'Asia/Ho_Chi_Minh',
  // })
  async handlePromptTranslationCron() {
    if (!this.enableCron) {
      return;
    }

    if (this.isRunning) {
      this.logger.warn(
        '⚠️ Translation cron is already running, skipping this execution',
      );
      return;
    }

    this.logger.log('✅ Starting scheduled prompt translation job...');
    try {
      await this.runTranslationJob();
      this.logger.log(
        '🎉 Scheduled prompt translation job completed successfully',
      );
    } catch (error) {
      this.logger.error('❌ Scheduled prompt translation job failed:', error);
    }
  }

  /**
   * Manual trigger for translation job (for testing and manual execution)
   */
  async triggerManualTranslation(): Promise<TranslationStats> {
    if (this.isRunning) {
      throw new Error('Translation job is already running');
    }

    this.logger.log('Starting manual prompt translation job...');
    return await this.runTranslationJob();
  }

  /**
   * Main translation job execution
   */
  private async runTranslationJob(): Promise<TranslationStats> {
    const startTime = Date.now();
    this.isRunning = true;

    const stats: TranslationStats = {
      totalPrompts: 0,
      successfulTranslations: 0,
      failedTranslations: 0,
      skippedTranslations: 0,
      processingTime: 0,
    };

    try {
      this.logger.log('Analyzing prompts requiring translation...');

      // Get translation batches for all languages
      const translationBatches = await this.getTranslationBatches();

      if (translationBatches.length === 0) {
        this.logger.log('No prompts require translation at this time');
        return stats;
      }

      stats.totalPrompts = translationBatches.reduce(
        (total, batch) => total + batch.prompts.length,
        0,
      );

      this.logger.log(
        `Found ${stats.totalPrompts} prompts requiring translation across ${translationBatches.length} language batches`,
      );

      // Process batches in parallel groups
      await this.processBatchesInParallel(translationBatches, stats);

      stats.processingTime = Date.now() - startTime;

      this.logger.log('Translation job completed successfully', {
        stats,
        duration: `${(stats.processingTime / 1000).toFixed(2)}s`,
      });

      return stats;
    } catch (error) {
      stats.processingTime = Date.now() - startTime;
      this.logger.error('Translation job failed:', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Get batches of prompts that need translation for each language (OPTIMIZED)
   */
  private async getTranslationBatches(): Promise<TranslationBatch[]> {
    const batches: TranslationBatch[] = [];

    for (const language of this.supportedLanguages) {
      // Skip Vietnamese as it's the source language
      if (language === 'vi') {
        continue;
      }

      this.logger.log(
        `Fetching untranslated prompts for language: ${language}`,
      );

      // Optimized query: Use NOT EXISTS for better performance with large datasets
      const unTranslatedPrompts = await this.promptRepository
        .createQueryBuilder('prompt')
        .where('prompt.title IS NOT NULL')
        .andWhere('prompt.title != :empty', { empty: '' })
        .andWhere('prompt.short_description IS NOT NULL')
        .andWhere(
          `NOT EXISTS (
            SELECT 1 FROM prompt_translations pt
            WHERE pt.prompt_id = prompt.id
            AND pt.lang = :lang
          )`,
          { lang: language },
        )
        .orderBy('prompt.id', 'ASC')
        .limit(this.maxPromptsPerRun) // Use configurable limit
        .getMany();

      this.logger.log(
        `Found ${unTranslatedPrompts.length} untranslated prompts for ${language}`,
      );

      if (unTranslatedPrompts.length > 0) {
        // Create optimized batches based on content complexity
        const optimizedBatches = this.useOptimizedBatching
          ? this.createOptimizedBatches(unTranslatedPrompts, language)
          : this.createStandardBatches(unTranslatedPrompts, language);

        batches.push(...optimizedBatches);
      }
    }

    return batches;
  }

  /**
   * Create optimized batches based on content complexity
   */
  private createOptimizedBatches(
    prompts: Prompt[],
    language: string,
  ): TranslationBatch[] {
    const batches: TranslationBatch[] = [];
    const sortedPrompts = [...prompts].sort((a, b) => {
      // Sort by content complexity (shorter content first for faster processing)
      const aComplexity = this.calculateContentComplexity(a);
      const bComplexity = this.calculateContentComplexity(b);
      return aComplexity - bComplexity;
    });

    // Create batches with mixed complexity for balanced processing
    for (let i = 0; i < sortedPrompts.length; i += this.batchSize) {
      const batchPrompts = sortedPrompts.slice(i, i + this.batchSize);
      batches.push({
        prompts: batchPrompts,
        language,
      });
    }

    return batches;
  }

  /**
   * Create standard batches (simple sequential batching)
   */
  private createStandardBatches(
    prompts: Prompt[],
    language: string,
  ): TranslationBatch[] {
    const batches: TranslationBatch[] = [];

    for (let i = 0; i < prompts.length; i += this.batchSize) {
      const batchPrompts = prompts.slice(i, i + this.batchSize);
      batches.push({
        prompts: batchPrompts,
        language,
      });
    }

    return batches;
  }

  /**
   * Calculate content complexity for batching optimization
   */
  private calculateContentComplexity(prompt: Prompt): number {
    let complexity = 0;

    // Base complexity from title length
    complexity += (prompt.title?.length || 0) * 0.5;

    // Add complexity from description
    complexity += (prompt.short_description?.length || 0) * 1;

    // Add significant complexity from optimization guide
    complexity += (prompt.optimization_guide?.length || 0) * 2;

    return Math.floor(complexity);
  }

  /**
   * Process translation batches in parallel groups
   */
  private async processBatchesInParallel(
    batches: TranslationBatch[],
    stats: TranslationStats,
  ): Promise<void> {
    // Process batches in parallel groups to avoid overwhelming the API
    for (let i = 0; i < batches.length; i += this.parallelBatches) {
      const batchGroup = batches.slice(i, i + this.parallelBatches);

      this.logger.log(
        `Processing batch group ${Math.floor(i / this.parallelBatches) + 1}/${Math.ceil(batches.length / this.parallelBatches)} (${batchGroup.length} batches)`,
      );

      // Process batches in parallel
      const batchPromises = batchGroup.map((batch) =>
        this.processBatch(batch, stats),
      );

      const results = await Promise.allSettled(batchPromises);

      // Log results for this batch group
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          this.logger.error(`Batch ${i + index + 1} failed:`, result.reason);
        }
      });

      // Reduced delay between batch groups for faster processing
      if (i + this.parallelBatches < batches.length) {
        await this.delay(1000); // Reduced from 2000ms to 1000ms
      }
    }
  }

  /**
   * Process a single translation batch
   */
  private async processBatch(
    batch: TranslationBatch,
    stats: TranslationStats,
  ): Promise<void> {
    this.logger.log(
      `Processing batch: ${batch.prompts.length} prompts for language '${batch.language}'`,
    );

    const translations: PromptTranslation[] = [];

    for (const prompt of batch.prompts) {
      try {
        const translation = await this.translatePromptWithRetry(
          prompt,
          batch.language,
        );
        if (translation) {
          translations.push(translation);
          stats.successfulTranslations++;
        } else {
          stats.skippedTranslations++;
        }
      } catch (error) {
        this.logger.error(
          `Failed to translate prompt ${prompt.id} to ${batch.language}:`,
          error.message,
        );
        stats.failedTranslations++;
      }

      // Reduced delay between individual translations for faster processing
      await this.delay(500); // Reduced from 1000ms to 500ms
    }

    // Bulk save translations
    if (translations.length > 0) {
      await this.bulkSaveTranslations(translations);
      this.logger.log(
        `Successfully saved ${translations.length} translations for language '${batch.language}'`,
      );
    }
  }

  /**
   * Translate a single prompt with retry logic
   * Now translates each field individually instead of using object request
   */
  private async translatePromptWithRetry(
    prompt: Prompt,
    targetLanguage: string,
    attempt: number = 1,
  ): Promise<PromptTranslation | null> {
    try {
      // Check if translation already exists (race condition protection)
      const existingTranslation =
        await this.promptTranslationRepository.findOne({
          where: { prompt_id: prompt.id, lang: targetLanguage },
        });

      if (existingTranslation) {
        this.logger.debug(
          `Translation already exists for prompt ${prompt.id} in ${targetLanguage}`,
        );
        return null;
      }

      this.logger.debug(
        `Starting optimized translation for prompt ${prompt.id} to ${targetLanguage}`,
      );

      // Use single API call for all fields (more efficient)
      const translationResponse =
        await this.translationService.translatePromptFieldsWithOptimizationGuide(
          {
            title: prompt.title,
            short_description: prompt.short_description,
            optimization_guide: prompt.optimization_guide,
            targetLanguage,
          },
        );

      this.logger.debug(
        `Completed optimized translation for prompt ${prompt.id} to ${targetLanguage}`,
      );

      // Create translation entity
      const translation = new PromptTranslation();
      translation.prompt_id = prompt.id;
      translation.lang = targetLanguage;
      translation.title = translationResponse.title;
      translation.short_description = translationResponse.short_description;
      translation.optimization_guide = translationResponse.optimization_guide;

      return translation;
    } catch (error) {
      if (attempt < this.maxRetries) {
        this.logger.warn(
          `Translation attempt ${attempt} failed for prompt ${prompt.id} (${targetLanguage}), retrying...`,
        );
        await this.delay(this.retryDelay * attempt); // Exponential backoff
        return this.translatePromptWithRetry(
          prompt,
          targetLanguage,
          attempt + 1,
        );
      }
      throw error;
    }
  }

  /**
   * Bulk save translations using optimized database transaction with upsert
   */
  private async bulkSaveTranslations(
    translations: PromptTranslation[],
  ): Promise<void> {
    if (translations.length === 0) {
      return;
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Use upsert for better performance and conflict handling
      await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(PromptTranslation)
        .values(translations)
        .orUpdate(
          ['title', 'short_description', 'optimization_guide', 'updated_at'],
          ['prompt_id', 'lang'],
        )
        .execute();

      await queryRunner.commitTransaction();

      this.logger.debug(
        `Successfully bulk saved ${translations.length} translations`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error('Bulk save translations failed:', error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get translation job status with detailed statistics
   */
  async getStatus(): Promise<{
    isRunning: boolean;
    supportedLanguages: string[];
    statistics: {
      totalPrompts: number;
      translatedPrompts: Record<string, number>;
      remainingPrompts: Record<string, number>;
      completionPercentage: Record<string, number>;
    };
  }> {
    const totalPrompts = await this.promptRepository.count({
      where: {
        title: Not(''),
      },
    });

    const statistics = {
      totalPrompts,
      translatedPrompts: {} as Record<string, number>,
      remainingPrompts: {} as Record<string, number>,
      completionPercentage: {} as Record<string, number>,
    };

    // Calculate statistics for each supported language
    for (const language of this.supportedLanguages) {
      if (language === 'vi') continue; // Skip source language

      const translatedCount = await this.promptTranslationRepository.count({
        where: { lang: language },
      });

      statistics.translatedPrompts[language] = translatedCount;
      statistics.remainingPrompts[language] = totalPrompts - translatedCount;
      statistics.completionPercentage[language] =
        totalPrompts > 0
          ? Math.round((translatedCount / totalPrompts) * 100)
          : 0;
    }

    return {
      isRunning: this.isRunning,
      supportedLanguages: this.supportedLanguages,
      statistics,
    };
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
