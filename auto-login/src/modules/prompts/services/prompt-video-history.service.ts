import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PromptVideoHistory } from '../entities/prompt-video-history.entity';
import { CreatePromptVideoHistoryDto } from '../dto/create-prompt-video-history.dto';
import { QueryPromptVideoHistoriesDto } from '../dto/query-prompt-video-histories.dto';
import { GeneratePromptVideoDto } from '../dto/generate-prompt-video.dto';

@Injectable()
export class PromptVideoHistoryService {
  constructor(
    @InjectRepository(PromptVideoHistory)
    private promptVideoHistoryRepository: Repository<PromptVideoHistory>,
  ) {}

  async generatePromptVideo(
    generateDto: GeneratePromptVideoDto,
    userId: number,
  ) {
    try {
      // Build the prompt from the form data
      const promptParts = [];
      const { prompt_data } = generateDto;

      if (prompt_data.location || prompt_data.time || prompt_data.weather) {
        promptParts.push(
          `Setting: ${[prompt_data.location, prompt_data.time, prompt_data.weather].filter(Boolean).join(', ')}`,
        );
      }

      if (prompt_data.characters && prompt_data.characters.length > 0) {
        const characterDescriptions = prompt_data.characters
          .map((char, index) => {
            const details = [
              char.description,
              char.appearance,
              char.emotion,
              char.mainAction,
            ].filter(Boolean);
            return `Character ${index + 1}: ${details.join(', ')}`;
          })
          .filter((desc) => desc.includes(':'));
        if (characterDescriptions.length > 0) {
          promptParts.push(`Characters: ${characterDescriptions.join('. ')}`);
        }
      }

      if (prompt_data.visualStyle && prompt_data.visualStyle.length > 0) {
        promptParts.push(`Visual Style: ${prompt_data.visualStyle.join(', ')}`);
      }

      if (prompt_data.lightingStyle && prompt_data.lightingStyle.length > 0) {
        promptParts.push(`Lighting: ${prompt_data.lightingStyle.join(', ')}`);
      }

      if (prompt_data.cameraAngle && prompt_data.cameraAngle.length > 0) {
        promptParts.push(`Camera Angle: ${prompt_data.cameraAngle.join(', ')}`);
      }

      if (prompt_data.mood && prompt_data.mood.length > 0) {
        promptParts.push(`Mood: ${prompt_data.mood.join(', ')}`);
      }

      if (
        (prompt_data.resolution && prompt_data.resolution.length > 0) ||
        (prompt_data.aspectRatio && prompt_data.aspectRatio.length > 0) ||
        (prompt_data.duration && prompt_data.duration.length > 0)
      ) {
        const techSpecs = [
          prompt_data.resolution && prompt_data.resolution.length > 0
            ? `Resolution: ${prompt_data.resolution.join(', ')}`
            : '',
          prompt_data.aspectRatio && prompt_data.aspectRatio.length > 0
            ? `Aspect Ratio: ${prompt_data.aspectRatio.join(', ')}`
            : '',
          prompt_data.duration && prompt_data.duration.length > 0
            ? `Duration: ${prompt_data.duration.join(', ')} seconds`
            : '',
        ].filter(Boolean);
        promptParts.push(`Technical Specs: ${techSpecs.join(', ')}`);
      }

      const generatedPromptText =
        promptParts.length > 0
          ? `Create a professional video with the following specifications:\n\n${promptParts.join('\n\n')}\n\nEnsure the video maintains high production quality with smooth transitions, professional cinematography, and engaging visual storytelling that captures the intended mood and atmosphere.`
          : 'Please fill in the form fields to generate a comprehensive video prompt.';

      // Save to history
      const historyData: CreatePromptVideoHistoryDto = {
        prompt_data: generateDto.prompt_data,
        generated_result: generatedPromptText,
        model: generateDto.model || 'gpt-4',
        usage: {
          prompt_tokens: Math.floor(
            JSON.stringify(generateDto.prompt_data).length / 4,
          ),
          completion_tokens: Math.floor(generatedPromptText.length / 4),
          total_tokens: Math.floor(
            (JSON.stringify(generateDto.prompt_data).length +
              generatedPromptText.length) /
              4,
          ),
        },
      };

      await this.createPromptVideoHistory(historyData, userId);

      return {
        result: generatedPromptText,
        model: generateDto.model || 'gpt-4',
        usage: historyData.usage,
      };
    } catch (error) {
      throw new Error(`Failed to generate prompt video: ${error.message}`);
    }
  }

  async createPromptVideoHistory(
    createDto: CreatePromptVideoHistoryDto,
    userId: number,
  ): Promise<PromptVideoHistory> {
    const history = this.promptVideoHistoryRepository.create({
      ...createDto,
      user_id: userId,
    });

    return await this.promptVideoHistoryRepository.save(history);
  }

  async findPromptVideoHistoriesByUser(
    userId: number,
    queryDto: QueryPromptVideoHistoriesDto,
  ) {
    const { page = 1, pageSize = 20, search_text } = queryDto;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.promptVideoHistoryRepository
      .createQueryBuilder('history')
      .where('history.user_id = :userId', { userId });

    if (search_text && search_text.trim()) {
      queryBuilder.andWhere('history.generated_result ILIKE :search', {
        search: `%${search_text.trim()}%`,
      });
    }

    const [histories, total] = await queryBuilder
      .orderBy('history.created_at', 'DESC')
      .skip(skip)
      .take(pageSize)
      .getManyAndCount();

    return {
      data: histories.map((history) => ({
        id: history.id,
        user_id: history.user_id,
        prompt_data: history.prompt_data,
        generated_result: history.generated_result,
        model: history.model,
        usage: history.usage,
        created_at: history.created_at,
      })),
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    };
  }

  async findPromptVideoHistoryById(
    id: number,
    userId: number,
  ): Promise<PromptVideoHistory> {
    const history = await this.promptVideoHistoryRepository.findOne({
      where: { id, user_id: userId },
    });

    if (!history) {
      throw new NotFoundException(
        `Prompt video history with ID ${id} not found`,
      );
    }

    return history;
  }

  async deletePromptVideoHistory(id: number, userId: number): Promise<void> {
    const history = await this.findPromptVideoHistoryById(id, userId);
    await this.promptVideoHistoryRepository.remove(history);
  }
}
