import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  UseGuards,
  Req,
} from '@nestjs/common';
import { PromptVideoHistoryService } from './services/prompt-video-history.service';
import { GeneratePromptVideoDto } from './dto/generate-prompt-video.dto';
import { QueryPromptVideoHistoriesDto } from './dto/query-prompt-video-histories.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@Controller()
@UseGuards(JwtAuthGuard)
export class PromptVideoHistoryController {
  constructor(
    private readonly promptVideoHistoryService: PromptVideoHistoryService,
  ) {}

  // POST /prompt-video/generate - Generate video prompt and save to history
  @Post('prompt-video/generate')
  async generatePromptVideo(
    @Body() generateDto: GeneratePromptVideoDto,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      return await this.promptVideoHistoryService.generatePromptVideo(
        generateDto,
        userId,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to generate video prompt: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // GET /prompt-video-histories - Get user's prompt video histories
  @Get('prompt-video-histories')
  async getPromptVideoHistories(
    @Query() queryDto: QueryPromptVideoHistoriesDto,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      return await this.promptVideoHistoryService.findPromptVideoHistoriesByUser(
        userId,
        queryDto,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve prompt video histories: ' +
          (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // GET /prompt-video-histories/:id - Get specific prompt video history
  @Get('prompt-video-histories/:id')
  async getPromptVideoHistoryById(
    @Param('id', ParseIntPipe) id: number,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      return await this.promptVideoHistoryService.findPromptVideoHistoryById(
        id,
        userId,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve prompt video history: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // DELETE /prompt-video-histories/:id - Delete specific prompt video history
  @Delete('prompt-video-histories/:id')
  async deletePromptVideoHistory(
    @Param('id', ParseIntPipe) id: number,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      await this.promptVideoHistoryService.deletePromptVideoHistory(id, userId);
      return { message: 'Prompt video history deleted successfully' };
    } catch (error) {
      throw new HttpException(
        'Failed to delete prompt video history: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
