import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { Prompt } from './entities/prompt.entity';
import { PromptCategory } from './entities/prompt-category.entity';
import { PromptCategoryTranslation } from './entities/prompt-category-translation.entity';
import { Topic } from './entities/topic.entity';
import { PromptHistory } from './entities/prompt-history.entity';
import { PromptVideoHistory } from './entities/prompt-video-history.entity';
import { PromptTranslation } from './entities/prompt-translation.entity';
import { PromptsController } from './prompts.controller';
import { PromptVideoHistoryController } from './prompt-video-history.controller';
import { PromptTranslationCronController } from './prompt-translation-cron.controller';
import { PromptCategoryTranslationCronController } from './prompt-category-translation-cron.controller';
import { PromptsService } from './prompts.service';
import { PromptVideoHistoryService } from './services/prompt-video-history.service';
import { TranslationService } from './services/translation.service';
import { PromptTranslationCronService } from './services/prompt-translation-cron.service';
import { PromptCategoryTranslationCronService } from './services/prompt-category-translation-cron.service';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Prompt,
      PromptCategory,
      PromptCategoryTranslation,
      Topic,
      PromptHistory,
      PromptVideoHistory,
      PromptTranslation,
    ]),
    ScheduleModule.forRoot(),
    AuthModule,
  ],
  controllers: [
    PromptsController,
    PromptVideoHistoryController,
    PromptTranslationCronController,
    PromptCategoryTranslationCronController,
  ],
  providers: [
    PromptsService,
    PromptVideoHistoryService,
    TranslationService,
    PromptTranslationCronService,
    PromptCategoryTranslationCronService,
  ],
  exports: [
    PromptsService,
    PromptVideoHistoryService,
    PromptTranslationCronService,
    PromptCategoryTranslationCronService,
  ],
})
export class PromptsModule {}
