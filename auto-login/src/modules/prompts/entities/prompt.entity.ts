import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { PromptCategory } from './prompt-category.entity';
import { Topic } from './topic.entity';
import { PromptTranslation } from './prompt-translation.entity';

@Entity('prompts')
export class Prompt {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 500 })
  title: string;

  @Column({ type: 'text', nullable: true })
  short_description: string;

  @Column({ type: 'text', nullable: true })
  content: string;

  @Column({ type: 'text', nullable: true })
  prompt_text: string;

  @Column({ type: 'text', nullable: true })
  optimization_guide: string;

  @Column({ nullable: true })
  category_id: number;

  @Column({ nullable: true })
  topic_id: number;

  @Column({ type: 'smallint', default: 1 })
  is_type: number;

  @Column({ type: 'smallint', nullable: true })
  sub_type: number;

  @Column({ type: 'text', nullable: true })
  what_field: string;

  @Column({ type: 'text', nullable: true })
  tips_field: string;

  @Column({ type: 'text', nullable: true })
  how_field: string;

  @Column({ type: 'text', nullable: true })
  input_field: string;

  @Column({ type: 'text', nullable: true })
  output_field: string;

  @Column({ type: 'text', nullable: true })
  add_tip: string;

  @Column({ type: 'text', nullable: true })
  additional_information: string;

  @Column({ type: 'integer', default: 0 })
  view_count: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => PromptCategory, (category) => category.prompts)
  @JoinColumn({ name: 'category_id' })
  category: PromptCategory;

  @ManyToOne(() => Topic, (topic) => topic.prompts)
  @JoinColumn({ name: 'topic_id' })
  topic: Topic;

  @OneToMany(() => PromptTranslation, (translation) => translation.prompt)
  translations: PromptTranslation[];
}
