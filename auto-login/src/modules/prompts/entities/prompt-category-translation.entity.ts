import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { PromptCategory } from './prompt-category.entity';

@Entity('prompt_categories_translations')
@Index(['category_id', 'lang'], { unique: true }) // Ensure unique combination of category_id and lang
export class PromptCategoryTranslation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  category_id: number;

  @Column({ length: 10 })
  lang: string; // Language code: 'en', 'vi', 'th', 'fr'

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  short_description: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => PromptCategory, (category) => category.translations)
  @JoinColumn({ name: 'category_id' })
  category: PromptCategory;
}
