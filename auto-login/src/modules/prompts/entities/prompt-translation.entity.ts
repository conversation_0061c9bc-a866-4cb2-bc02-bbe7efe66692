import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Prompt } from './prompt.entity';

@Entity('prompt_translations')
@Index(['prompt_id', 'lang'], { unique: true }) // Ensure unique combination of prompt_id and lang
export class PromptTranslation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  prompt_id: number;

  @Column({ length: 10 })
  lang: string; // Language code: 'en', 'vi', 'th', 'fr'

  @Column({ length: 500 })
  title: string;

  @Column({ type: 'text', nullable: true })
  short_description: string;

  @Column({ type: 'text', nullable: true })
  optimization_guide: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => Prompt, (prompt) => prompt.translations)
  @JoinColumn({ name: 'prompt_id' })
  prompt: Prompt;
}
