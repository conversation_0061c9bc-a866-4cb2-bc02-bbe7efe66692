import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Prompt } from './prompt.entity';

@Entity('prompt_histories')
export class PromptHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  user_id: number;

  @Column({ nullable: true })
  prompt_id: number;

  @Column({ type: 'text' })
  generated_result: string;

  @Column({ length: 255 })
  model: string;

  @Column({ type: 'jsonb', nullable: true })
  usage: any;

  @CreateDateColumn()
  created_at: Date;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Prompt, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'prompt_id' })
  prompt: Prompt;
}
