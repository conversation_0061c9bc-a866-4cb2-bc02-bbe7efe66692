import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('prompt_video_histories')
export class PromptVideoHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  user_id: number;

  @Column({ type: 'jsonb' })
  prompt_data: any;

  @Column({ type: 'text' })
  generated_result: string;

  @Column({ length: 255, default: 'gpt-4' })
  model: string;

  @Column({ type: 'jsonb', nullable: true })
  usage: any;

  @CreateDateColumn()
  created_at: Date;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;
}
