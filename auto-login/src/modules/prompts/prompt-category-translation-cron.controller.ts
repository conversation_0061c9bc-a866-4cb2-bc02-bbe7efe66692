import { Controller, Post, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import {
  PromptCategoryTranslationCronService,
  CategoryTranslationStats,
} from './services/prompt-category-translation-cron.service';

@Controller('admin/category-translation')
@UseGuards(JwtAuthGuard)
export class PromptCategoryTranslationCronController {
  constructor(
    private readonly promptCategoryTranslationCronService: PromptCategoryTranslationCronService,
  ) {}

  /**
   * Manually trigger category translation job
   * POST /admin/category-translation/trigger
   */
  @Post('trigger')
  async triggerTranslation(): Promise<{
    success: boolean;
    message: string;
    stats?: CategoryTranslationStats;
    error?: string;
  }> {
    try {
      const stats =
        await this.promptCategoryTranslationCronService.triggerManualCategoryTranslation();
      return {
        success: true,
        message: 'Category translation job completed successfully',
        stats,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        error: error.stack,
      };
    }
  }

  /**
   * Get category translation job status
   * GET /admin/category-translation/status
   */
  @Get('status')
  async getStatus() {
    const status = this.promptCategoryTranslationCronService.getStatus();
    const circuitBreakerStatus =
      this.promptCategoryTranslationCronService.getCircuitBreakerStatus();

    return {
      success: true,
      data: {
        ...status,
        circuitBreaker: circuitBreakerStatus,
      },
    };
  }

  /**
   * Health check endpoint
   * GET /admin/category-translation/health
   */
  @Get('health')
  async healthCheck() {
    return {
      success: true,
      message: 'Category Translation Cron Service is healthy',
      timestamp: new Date().toISOString(),
    };
  }
}
