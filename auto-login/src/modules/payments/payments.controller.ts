import {
  Controller,
  Post,
  Body,
  Param,
  Get,
  UseGuards,
  Request,
  UnauthorizedException,
} from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ProductsService } from '../products/products.service';

@Controller('payments')
@UseGuards(JwtAuthGuard)
export class PaymentsController {
  constructor(
    private readonly paymentsService: PaymentsService,
    private readonly productsService: ProductsService,
  ) {}

  @Post('create-order')
  async createPaypalOrder(
    @Request() req: { user: { sub: number } },
    @Body() createPaymentDto: CreatePaymentDto,
  ) {
    if (!req.user?.sub) {
      throw new UnauthorizedException('User not authenticated');
    }
    createPaymentDto.user_id = req.user.sub;

    // Kiểm tra xem có product_duration_id hay không
    if (createPaymentDto.product_duration_id) {
      // <PERSON><PERSON>m tra số lượng sản phẩm còn lại
      const productDuration = await this.productsService.findProductDuration(
        createPaymentDto.product_duration_id,
      );

      if (productDuration.quantity <= 0) {
        throw new UnauthorizedException('Sản phẩm đã hết hàng');
      }
    }

    return this.paymentsService.createPaypalOrder(createPaymentDto);
  }

  @Post('capture/:orderId/:paymentId')
  async capturePaypalPayment(
    @Param('orderId') orderId: string,
    @Param('paymentId') paymentId: number,
  ) {
    return this.paymentsService.capturePaypalPayment(orderId, paymentId);
  }

  @Get(':id')
  async getPayment(@Param('id') id: number) {
    return this.paymentsService.getPaymentById(id);
  }
}
