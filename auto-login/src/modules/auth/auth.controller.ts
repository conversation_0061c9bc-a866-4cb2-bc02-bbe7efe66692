import {
  Controller,
  Post,
  Body,
  Get,
  Render,
  UseGuards,
  Req,
  Query,
  Res,
  UnauthorizedException,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ConfigService } from '@nestjs/config';
import { JwtAuthGuard } from './jwt-auth.guard';
import { RolesGuard } from './roles.guard';
import { Roles } from './roles.decorator';

@Controller()
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {}

  @Get('login')
  @Render('auth/login')
  showLogin() {
    return {};
  }

  @Post('register')
  register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('login')
  async login(@Body() loginDto: LoginDto, @Res() res) {
    const { access_token, refresh_token } =
      await this.authService.login(loginDto);

    // Set HTTP-only cookies
    // Access token cookie - short lived
    res.cookie('access_token', access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      maxAge: 3600000, // 1 hour
      domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
    });

    // Refresh token cookie - longer lived
    res.cookie('refresh_token', refresh_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      maxAge: 7 * 24 * 3600000, // 7 days
      path: '/auth', // Only available to /auth routes for security
      domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
    });

    return res.json({ success: true });
  }

  @Post('auth/refresh')
  async refreshToken(@Req() req, @Res() res) {
    try {
      const refreshToken = req.cookies['refresh_token'];

      if (!refreshToken) {
        throw new UnauthorizedException('Refresh token not found');
      }

      const { access_token, refresh_token } =
        await this.authService.refreshToken(refreshToken);

      // Set new HTTP-only cookies
      res.cookie('access_token', access_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 3600000, // 1 hour
        domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
      });

      res.cookie('refresh_token', refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 7 * 24 * 3600000, // 7 days
        path: '/auth', // Only available to /auth routes for security
        domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
      });

      return res.json({ success: true });
    } catch (error) {
      // Clear cookies on error with same options as when setting them
      res.clearCookie('access_token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        domain: process.env.COOKIE_DOMAIN || undefined,
      });

      res.clearCookie('refresh_token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        path: '/auth',
        domain: process.env.COOKIE_DOMAIN || undefined,
      });

      return res.status(401).json({
        success: false,
        message: error.message || 'Invalid refresh token',
      });
    }
  }

  @Get('auth/status')
  async getAuthStatus(@Req() req, @Res() res) {
    try {
      const token = req.cookies['access_token'];
      if (!token) {
        return res.json({
          isAuthenticated: false,
          user: null,
          status: null,
        });
      }

      const decoded = await this.authService.verifyToken(token);
      return res.json({
        isAuthenticated: true,
        user: decoded,
      });
    } catch (error) {
      return res.json({
        isAuthenticated: false,
        user: null,
        status: null,
      });
    }
  }

  @Get('auth/google')
  @UseGuards(AuthGuard('google'))
  async googleAuth(@Query('return_url') returnUrl?: string) {
    // Store return URL in session for callback
    if (returnUrl) {
      // You might want to validate the return URL here
      return { returnUrl };
    }
  }

  @Get('auth/discord')
  @UseGuards(AuthGuard('discord'))
  async discordAuth(@Query('return_url') returnUrl?: string) {
    // Store return URL in session for callback
    if (returnUrl) {
      // You might want to validate the return URL here
      return { returnUrl };
    }
  }

  @Post('logout')
  async logout(@Res() res) {
    // Clear both cookies with same options as when setting them
    res.clearCookie('access_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      domain: process.env.COOKIE_DOMAIN || undefined,
    });

    res.clearCookie('refresh_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      path: '/auth',
      domain: process.env.COOKIE_DOMAIN || undefined,
    });

    return res.json({ success: true });
  }

  @Get('auth/google/callback')
  @UseGuards(AuthGuard('google'))
  async googleAuthCallback(@Req() req, @Res() res) {
    try {
      const { access_token, refresh_token } =
        await this.authService.validateGoogleLogin(req.user);

      // Set HTTP-only cookies
      // Access token cookie - short lived
      res.cookie('access_token', access_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 3600000, // 1 hour
        domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
      });

      // Refresh token cookie - longer lived
      res.cookie('refresh_token', refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 7 * 24 * 3600000, // 7 days
        path: '/auth', // Only available to /auth routes for security
        domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
      });

      return res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Successful</title>
          </head>
          <body>
            <script>
              // Send success message to opener window
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({ type: 'AUTH_SUCCESS' }, '*');
              }

              // Close this window
              window.close();
            </script>
          </body>
        </html>
      `);
    } catch (error) {
      return res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Failed</title>
          </head>
          <body>
            <script>
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({ type: 'AUTH_FAILED' }, '*');
              }

              window.close();
            </script>
          </body>
        </html>
      `);
    }
  }

  @Get('auth/discord/callback')
  @UseGuards(AuthGuard('discord'))
  async discordAuthCallback(@Req() req, @Res() res) {
    try {
      const { access_token, refresh_token } =
        await this.authService.validateDiscordLogin(req.user);

      // Set HTTP-only cookies
      // Access token cookie - short lived
      res.cookie('access_token', access_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 3600000, // 1 hour
        domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
      });

      // Refresh token cookie - longer lived
      res.cookie('refresh_token', refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 7 * 24 * 3600000, // 7 days
        path: '/auth', // Only available to /auth routes for security
        domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
      });

      return res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Successful</title>
          </head>
          <body>
            <script>
              // Send success message to opener window
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({ type: 'AUTH_SUCCESS' }, '*');
              }

              // Close this window
              window.close();
            </script>
          </body>
        </html>
      `);
    } catch (error) {
      return res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Failed</title>
          </head>
          <body>
            <script>
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({ type: 'AUTH_FAILED' }, '*');
              }

              window.close();
            </script>
          </body>
        </html>
      `);
    }
  }

  @Get('change-password')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('auth/change-password')
  showChangePassword() {
    return {};
  }

  @Post('change-password')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      return await this.authService.changePassword(userId, changePasswordDto);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to change password',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
