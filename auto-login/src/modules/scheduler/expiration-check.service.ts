import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { PackageUser } from '../packages/entities/package-user.entity';
import { MailerService } from '@nestjs-modules/mailer';
import { UsersService } from '../users/users.service';
// import { DiscordService } from '../discord/discord.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ExpirationCheckService {
  private readonly logger = new Logger(ExpirationCheckService.name);

  constructor(
    @InjectRepository(PackageUser)
    private readonly packageUserRepository: Repository<PackageUser>,
    private readonly mailerService: MailerService,
    private readonly usersService: UsersService,
    // private readonly discordService: DiscordService,
    private readonly configService: ConfigService,
  ) {}

  // @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  // async checkExpiringSubscriptions() {
  //   const today = new Date();
  //   const threeDaysFromNow = new Date();
  //   threeDaysFromNow.setDate(today.getDate() + 3);

  //   try {
  //     // Find package users whose expiration date is within the next 3 days
  //     const expiringPackageUsers = await this.packageUserRepository
  //       .createQueryBuilder('packageUser')
  //       .leftJoinAndSelect('packageUser.user', 'user')
  //       .leftJoinAndSelect('packageUser.package', 'package')
  //       .where('packageUser.expires_at <= :threeDaysFromNow', {
  //         threeDaysFromNow,
  //       })
  //       .andWhere('packageUser.expires_at > :today', { today })
  //       .andWhere('packageUser.status = :status', { status: 'active' })
  //       .andWhere('user.status = :userStatus', { userStatus: 'active' })
  //       .getMany();

  //     this.logger.log(
  //       `Found ${expiringPackageUsers.length} packages with expiring subscriptions`,
  //     );

  //     // Group by user and expiration date
  //     const userExpirationMap = new Map<
  //       string, // key: userId_expirationDate
  //       {
  //         user: User;
  //         expirationDate: Date;
  //         packages: { id: number; name: string }[];
  //       }
  //     >();

  //     for (const packageUser of expiringPackageUsers) {
  //       const userId = packageUser.user.id;
  //       // Format date as YYYY-MM-DD to use as part of the key
  //       const expirationDateStr = packageUser.expires_at
  //         .toISOString()
  //         .split('T')[0];
  //       const key = `${userId}_${expirationDateStr}`;

  //       if (!userExpirationMap.has(key)) {
  //         userExpirationMap.set(key, {
  //           user: packageUser.user,
  //           expirationDate: packageUser.expires_at,
  //           packages: [
  //             { id: packageUser.package.id, name: packageUser.package.name },
  //           ],
  //         });
  //       } else {
  //         // Add this package to the existing entry
  //         userExpirationMap.get(key).packages.push({
  //           id: packageUser.package.id,
  //           name: packageUser.package.name,
  //         });
  //       }
  //     }

  //     // Send notifications to users
  //     for (const {
  //       user,
  //       expirationDate,
  //       packages,
  //     } of userExpirationMap.values()) {
  //       const daysUntilExpiration = Math.ceil(
  //         (expirationDate.getTime() - new Date().getTime()) /
  //           (1000 * 60 * 60 * 24),
  //       );

  //       if (daysUntilExpiration > 0) {
  //         await this.sendExpirationNotification(
  //           user,
  //           daysUntilExpiration,
  //           expirationDate,
  //           packages,
  //         );
  //       }
  //     }

  //     this.logger.log(
  //       `Sent expiration notifications for ${userExpirationMap.size} user-expiration combinations`,
  //     );
  //   } catch (error) {
  //     this.logger.error('Error checking expiring subscriptions:', error);
  //   }
  // }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async checkExpiredUsers() {
    this.logger.log('Starting check for expired users...');
    const today = new Date();

    try {
      // Find users with expired packages who are still active
      const expiredPackageUsers = await this.packageUserRepository
        .createQueryBuilder('packageUser')
        .leftJoinAndSelect('packageUser.user', 'user')
        .leftJoinAndSelect('packageUser.package', 'package')
        .where('packageUser.expires_at < :today', { today })
        .andWhere('packageUser.status = :status', { status: 'active' })
        .andWhere('user.status = :userStatus', { userStatus: 'active' })
        .getMany();

      // Group by user and expiration date
      const userExpirationMap = new Map<
        string, // key: userId_expirationDate
        {
          user: User;
          expirationDate: Date;
          packages: { id: number; name: string }[];
        }
      >();

      for (const packageUser of expiredPackageUsers) {
        const userId = packageUser.user.id;
        // Format date as YYYY-MM-DD to use as part of the key
        const expirationDateStr = packageUser.expires_at
          .toISOString()
          .split('T')[0];
        const key = `${userId}_${expirationDateStr}`;

        if (!userExpirationMap.has(key)) {
          userExpirationMap.set(key, {
            user: packageUser.user,
            expirationDate: packageUser.expires_at,
            packages: [
              { id: packageUser.package.id, name: packageUser.package.name },
            ],
          });
        } else {
          // Add this package to the existing entry
          userExpirationMap.get(key).packages.push({
            id: packageUser.package.id,
            name: packageUser.package.name,
          });
        }
      }

      // Get unique users from the map
      const uniqueUsers = new Set<number>();
      const userExpirationEntries = Array.from(userExpirationMap.values());

      for (const entry of userExpirationEntries) {
        uniqueUsers.add(entry.user.id);
      }

      const expiredUsers = Array.from(uniqueUsers).map(
        (userId) =>
          userExpirationEntries.find((entry) => entry.user.id === userId).user,
      );

      this.logger.log(
        `Found ${expiredUsers.length} expired users with ${userExpirationEntries.length} expiration entries`,
      );

      // Discord role management is disabled
      // Get the role IDs from config
      // const memberRoleId = this.configService.get<string>(
      //   'discord.memberRoleId',
      // );
      // const vipBuyerRoleId = this.configService.get<string>(
      //   'discord.vipBuyerRoleId',
      // );
      // const designBuyerRoleId = this.configService.get<string>(
      //   'discord.designBuyerRoleId',
      // );
      // const marketingBuyerRoleId = this.configService.get<string>(
      //   'discord.marketingBuyerRoleId',
      // );

      // if (!memberRoleId) {
      //   this.logger.error('Member role ID not found in configuration');
      //   return;
      // }

      // if (!vipBuyerRoleId || !designBuyerRoleId || !marketingBuyerRoleId) {
      //   this.logger.error(
      //     'One or more buyer role IDs not found in configuration',
      //   );
      //   return;
      // }

      // this.logger.log(
      //   `Using memberRoleId: ${memberRoleId}, vipBuyerRoleId: ${vipBuyerRoleId}, designBuyerRoleId: ${designBuyerRoleId}, marketingBuyerRoleId: ${marketingBuyerRoleId}`,
      // );

      // Process each expired user
      for (const user of expiredUsers) {
        try {
          // Update user status in database
          await this.usersService.updateStatus(user.id, 'inactive');

          // Update package user status to expired
          await this.packageUserRepository
            .createQueryBuilder()
            .update(PackageUser)
            .set({ status: 'expired' })
            .where('user_id = :userId', { userId: user.id })
            .andWhere('status = :status', { status: 'active' })
            .execute();

          // Discord role management is disabled
          // If user has Discord ID, update Discord role from any buyer role to member
          // if (user.discord_id) {
          //   try {
          //     // Try to remove all buyer roles and set to member role
          //     let roleChangeSuccess = false;

          //     // Try to change from VIP buyer role to member
          //     roleChangeSuccess = await this.discordService.changeUserRole(
          //       user.discord_id,
          //       vipBuyerRoleId,
          //       memberRoleId,
          //     );

          //     // Try to change from Design buyer role to member
          //     const designRoleChangeSuccess =
          //       await this.discordService.changeUserRole(
          //         user.discord_id,
          //         designBuyerRoleId,
          //         memberRoleId,
          //       );

          //     // Try to change from Marketing buyer role to member
          //     const marketingRoleChangeSuccess =
          //       await this.discordService.changeUserRole(
          //         user.discord_id,
          //         marketingBuyerRoleId,
          //         memberRoleId,
          //       );

          //     // Consider success if any role was changed successfully
          //     roleChangeSuccess =
          //       roleChangeSuccess ||
          //       designRoleChangeSuccess ||
          //       marketingRoleChangeSuccess;

          //     if (roleChangeSuccess) {
          //       this.logger.log(
          //         `Successfully changed Discord role for user ${user.email} (Discord ID: ${user.discord_id}) to member role`,
          //       );
          //     } else {
          //       this.logger.warn(
          //         `Failed to change Discord role for user ${user.email} (Discord ID: ${user.discord_id})`,
          //       );
          //     }
          //   } catch (discordError) {
          //     this.logger.error(
          //       `Error updating Discord role for user ${user.email} (Discord ID: ${user.discord_id}):`,
          //       discordError,
          //     );
          //   }
          // }

          // Get all expiration entries for this user and send notifications for each
          const userEntries = userExpirationEntries.filter(
            (entry) => entry.user.id === user.id,
          );

          for (const { expirationDate, packages } of userEntries) {
            // Send expiration notification for this specific date and packages
            await this.sendExpiredNotification(user, expirationDate, packages);
          }
        } catch (error) {
          this.logger.error(
            `Error processing expired user ${user.email}:`,
            error,
          );
        }
      }

      this.logger.log(
        `Completed processing ${expiredUsers.length} expired users with ${userExpirationEntries.length} expiration entries`,
      );
    } catch (error) {
      this.logger.error('Error checking expired users:', error);
    }
  }

  private async sendExpiredNotification(
    user: User,
    expirationDate: Date,
    packages?: { id: number; name: string }[],
  ) {
    const messageId = `<expiration-expired-${user.id}-${Date.now()}@kitsify.com>`;
    const subject = `[Kitsify] Service Expiration Notice`;

    // Create package list HTML if packages are provided
    let packagesHtml = '';
    if (packages && packages.length > 0) {
      packagesHtml = `
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
          <p style="color: #333; font-size: 16px; margin-bottom: 10px;">
            <strong>Expired service packages:</strong>
          </p>
          <ul style="color: #333; font-size: 16px; margin: 0; padding-left: 20px;">
            ${packages.map((pkg) => `<li>${pkg.name}</li>`).join('')}
          </ul>
        </div>
      `;
    }

    const html = `
      <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(to right, #ffffff, #f8f9fa); padding: 20px; border-radius: 8px; border: 1px solid #e9ecef;">
          <h2 style="color: #2b3481; margin-bottom: 20px; text-align: center;">Service Expiration Notice</h2>
          <p style="color: #333; font-size: 16px; line-height: 1.6;">Hello <strong>${user.email}</strong>,</p>
          <div style="background-color: #ffe5e5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="color: #333; font-size: 16px; margin: 0;">
              ⚠️ Your service expired on <strong style="color: #ff6b6b;">${expirationDate.toLocaleDateString('en-US')}</strong>
            </p>
          </div>
          ${packagesHtml}
          <p style="color: #333; font-size: 16px; line-height: 1.6;">Your access has been downgraded to basic membership. To continue using all features, please renew your service.</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="https://kitsify.com/#pricing"
               style="display: inline-block; background-color: #2b3481; color: white; padding: 14px 28px; text-decoration: none; border-radius: 5px; font-weight: 600; letter-spacing: 0.5px;">
              RENEW NOW
            </a>
          </div>
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
            <p style="color: #666; font-size: 14px; margin: 0;">
              💡 <strong>Note:</strong> If you need support, please contact us:
            </p>
            <ul style="color: #666; font-size: 14px; margin: 10px 0;">
              <li>Email: <a href="mailto:<EMAIL>" style="color: #2b3481;"><EMAIL></a></li>
            </ul>
          </div>
          <hr style="border: none; border-top: 1px solid #e9ecef; margin: 20px 0;">
          <p style="color: #666; font-size: 14px; text-align: center;">Best regards,<br><strong>Kitsify Team</strong></p>
        </div>
        <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
          <p>This email was sent automatically from the Kitsify system.</p>
          <p>© ${new Date().getFullYear()} Kitsify. All rights reserved.</p>
        </div>
      </div>
    `;

    try {
      // Send email notification
      await this.mailerService.sendMail({
        to: user.email,
        from: {
          name: 'Kitsify',
          address: this.configService.get('mail.from'),
        },
        subject,
        html,
        headers: {
          'Message-ID': messageId,
          'List-Unsubscribe': `<https://kitsify.com/unsubscribe?uid=${user.id}>, <mailto:<EMAIL>>`,
          'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
          'Feedback-ID': `expiration-expired:${user.id}:kitsify:${process.env.MAIL_FEEDBACK_ID}`,
          Precedence: 'bulk',
          'X-Auto-Response-Suppress': 'OOF, AutoReply',
          'X-Entity-Ref-ID': `kitsify-expiration-expired-${user.id}`,
        },
      });
      this.logger.log(
        `Successfully sent expiration expired notification to ${user.email}`,
      );

      // Discord messaging is disabled
      // Send Discord DM if user has Discord ID
      // if (user.discord_id) {
      //   // Create package list for Discord message if packages are provided
      //   let packagesDiscord = '';
      //   if (packages && packages.length > 0) {
      //     packagesDiscord = `\n**Expired service packages:**\n${packages.map((pkg) => `- ${pkg.name}`).join('\n')}`;
      //   }

      //   const discordMessage = `**[Kitsify] Service Expiration Notice**

      // Hello,

      // ⚠️ Your service expired on **${expirationDate.toLocaleDateString('en-US')}**
      // ${packagesDiscord}

      // Your access has been downgraded to basic membership. To continue using all features, please renew your service.

      // Renew now: https://kitsify.com/renew?uid=${user.id}

      // If you need support, please contact us:
      // - Email: <EMAIL>

      // Best regards,
      // Kitsify Team`;

      //   const dmSent = await this.discordService.sendDirectMessage(
      //     user.discord_id,
      //     discordMessage,
      //   );
      //   if (dmSent) {
      //     this.logger.log(
      //       `Successfully sent Discord DM to user ${user.email} (Discord ID: ${user.discord_id})`,
      //     );
      //   } else {
      //     this.logger.warn(
      //       `Failed to send Discord DM to user ${user.email} (Discord ID: ${user.discord_id})`,
      //     );
      //   }
      // }
    } catch (error) {
      this.logger.error(
        `Failed to send expired notification to ${user.email}:`,
        error,
      );
    }
  }

  private async sendExpirationNotification(
    user: User,
    daysRemaining: number,
    expirationDate: Date,
    packages?: { id: number; name: string }[],
  ) {
    const messageId = `<expiration-notice-${user.id}-${Date.now()}@kitsify.com>`;
    const subject = `[Kitsify] Service Renewal Reminder - ${daysRemaining} days remaining`;

    // Create package list HTML if packages are provided
    let packagesHtml = '';
    if (packages && packages.length > 0) {
      packagesHtml = `
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
          <p style="color: #333; font-size: 16px; margin-bottom: 10px;">
            <strong>Service packages expiring soon:</strong>
          </p>
          <ul style="color: #333; font-size: 16px; margin: 0; padding-left: 20px;">
            ${packages.map((pkg) => `<li>${pkg.name}</li>`).join('')}
          </ul>
        </div>
      `;
    }

    const html = `
      <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(to right, #ffffff, #f8f9fa); padding: 20px; border-radius: 8px; border: 1px solid #e9ecef;">
          <h2 style="color: #2b3481; margin-bottom: 20px; text-align: center;">Kitsify Service Renewal Reminder</h2>
          <p style="color: #333; font-size: 16px; line-height: 1.6;">Hello <strong>${user.email}</strong>,</p>
          <div style="background-color: #fff4e5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="color: #333; font-size: 16px; margin: 0;">
              ⚠️ Your service will expire in <strong style="color: #ff6b6b;">${daysRemaining} days</strong>
            </p>
          </div>
          ${packagesHtml}
          <ul style="color: #333; font-size: 16px; line-height: 1.6;">
            <li>Expiration date: <strong>${expirationDate.toLocaleDateString('en-US')}</strong></li>
            <li>Status: <span style="color: #40c057;">Active</span></li>
          </ul>
          <p style="color: #333; font-size: 16px; line-height: 1.6;">To continue using the service without interruption, please renew before the expiration date.</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="https://kitsify.com/#pricing"
               style="display: inline-block; background-color: #2b3481; color: white; padding: 14px 28px; text-decoration: none; border-radius: 5px; font-weight: 600; letter-spacing: 0.5px;">
              RENEW NOW
            </a>
          </div>
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
            <p style="color: #666; font-size: 14px; margin: 0;">
              💡 <strong>Note:</strong> If you need support, please contact us:
            </p>
            <ul style="color: #666; font-size: 14px; margin: 10px 0;">
              <li>Email: <a href="mailto:<EMAIL>" style="color: #2b3481;"><EMAIL></a></li>
            </ul>
          </div>
          <hr style="border: none; border-top: 1px solid #e9ecef; margin: 20px 0;">
          <p style="color: #666; font-size: 14px; text-align: center;">Best regards,<br><strong>Kitsify Team</strong></p>
        </div>
        <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
          <p>This email was sent automatically from the Kitsify system.</p>
          <p>© ${new Date().getFullYear()} Kitsify. All rights reserved.</p>
        </div>
      </div>
    `;

    try {
      // Send email notification
      await this.mailerService.sendMail({
        to: user.email,
        from: {
          name: 'Kitsify',
          address: this.configService.get('mail.from'),
        },
        subject,
        html,
        headers: {
          'Message-ID': messageId,
          'List-Unsubscribe': `<https://kitsify.com/unsubscribe?uid=${user.id}>, <mailto:<EMAIL>>`,
          'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
          'Feedback-ID': `expiration-notice:${user.id}:kitsify:${process.env.MAIL_FEEDBACK_ID}`,
          Precedence: 'bulk',
          'X-Auto-Response-Suppress': 'OOF, AutoReply',
          'X-Entity-Ref-ID': `kitsify-expiration-${user.id}`,
        },
      });
      this.logger.log(
        `Successfully sent expiration notification to ${user.email}`,
      );

      // Discord messaging is disabled
      // Send Discord DM if user has Discord ID
      // if (user.discord_id) {
      //   // Create package list for Discord message if packages are provided
      //   let packagesDiscord = '';
      //   if (packages && packages.length > 0) {
      //     packagesDiscord = `\n**Service packages expiring soon:**\n${packages.map((pkg) => `- ${pkg.name}`).join('\n')}`;
      //   }

      //   const discordMessage = `**[Kitsify] Service Renewal Reminder - ${daysRemaining} days remaining**

      // Hello,

      // ⚠️ Your service will expire in **${daysRemaining} days**
      // ${packagesDiscord}

      // - Expiration date: **${expirationDate.toLocaleDateString('en-US')}**
      // - Status: **Active**

      // To continue using the service without interruption, please renew before the expiration date.

      // Renew now: https://kitsify.com/renew?uid=${user.id}

      // If you need support, please contact us:
      // - Email: <EMAIL>

      // Best regards,
      // Kitsify Team`;

      //   const dmSent = await this.discordService.sendDirectMessage(
      //     user.discord_id,
      //     discordMessage,
      //   );
      //   if (dmSent) {
      //     this.logger.log(
      //       `Successfully sent Discord DM to user ${user.email} (Discord ID: ${user.discord_id})`,
      //     );
      //   } else {
      //     this.logger.warn(
      //       `Failed to send Discord DM to user ${user.email} (Discord ID: ${user.discord_id})`,
      //     );
      //   }
      // }
    } catch (error) {
      this.logger.error(`Failed to send notification to ${user.email}:`, error);
      throw error;
    }
  }
}
