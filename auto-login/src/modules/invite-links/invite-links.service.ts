import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InviteLink } from './entities/invite-link.entity';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';
import { User } from '../users/entities/user.entity';
import { MailerService } from '@nestjs-modules/mailer';

@Injectable()
export class InviteLinksService {
  private readonly logger = new Logger(InviteLinksService.name);
  private readonly baseUrl: string;
  private readonly vipBuyerRoleId: string;

  constructor(
    @InjectRepository(InviteLink)
    private inviteLinkRepository: Repository<InviteLink>,
    private configService: ConfigService,
    private mailerService: MailerService,
  ) {
    this.baseUrl = this.configService.get<string>('API_URL');
    this.vipBuyerRoleId = this.configService.get<string>(
      'discord.vipBuyerRoleId',
    );
  }

  async createInviteLink(user: User): Promise<InviteLink> {
    if (!user.discord_id) {
      throw new Error('User does not have a Discord ID');
    }

    // Create a unique invite code
    const inviteCode = uuidv4();

    // Set expiration date (30 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    // Always use vipBuyerRoleId
    const roleId = this.vipBuyerRoleId;

    // Create and save the invite link
    const inviteLink = this.inviteLinkRepository.create({
      invite_code: inviteCode,
      discord_user_id: user.discord_id,
      role_id: roleId,
      expires_at: expiresAt,
      used: false,
    });

    await this.inviteLinkRepository.save(inviteLink);
    this.logger.log(
      `Created invite link for user ${user.email} with code ${inviteCode} with VIP role: ${roleId}`,
    );

    return inviteLink;
  }

  async findByInviteCode(inviteCode: string): Promise<InviteLink> {
    const inviteLink = await this.inviteLinkRepository
      .createQueryBuilder('inviteLink')
      .leftJoinAndSelect('inviteLink.user', 'user')
      .select([
        'inviteLink.id',
        'inviteLink.invite_code',
        'inviteLink.user_id',
        'inviteLink.expires_at',
        'inviteLink.used',
        'inviteLink.created_at',
        'inviteLink.updated_at',
        // User fields - exclude sensitive data
        'user.id',
        'user.email',
        'user.status',
        'user.role',
        'user.created_at',
        'user.updated_at',
      ])
      .where('inviteLink.invite_code = :inviteCode', { inviteCode })
      .getOne();

    if (!inviteLink) {
      throw new NotFoundException('Invite link not found');
    }

    return inviteLink;
  }

  async markAsUsed(id: number): Promise<void> {
    await this.inviteLinkRepository.update(id, { used: true });
  }

  async sendInviteEmail(user: User, inviteLink: InviteLink): Promise<void> {
    const inviteUrl = `${this.baseUrl}/discord/invite/${inviteLink.invite_code}`;
    const messageId = `<discord-invite-${user.id}-${Date.now()}@kitsify.com>`;
    const subject = `[Kitsify] Mời tham gia Discord Server của chúng tôi`;

    const html = `
      <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(to right, #ffffff, #f8f9fa); padding: 20px; border-radius: 8px; border: 1px solid #e9ecef;">
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="https://kitsify.com/logo.png" alt="Kitsify Logo" style="max-width: 150px; height: auto;">
          </div>
          <h2 style="color: #2b3481; margin-bottom: 20px; text-align: center;">Mời tham gia Discord Server</h2>
          <p style="color: #333; font-size: 16px; line-height: 1.6;">Xin chào <strong>${user.email}</strong>,</p>
          <p style="color: #333; font-size: 16px; line-height: 1.6;">
            Cảm ơn bạn đã thanh toán dịch vụ của Kitsify. Bạn đã được mời tham gia vào Discord Server của chúng tôi.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${inviteUrl}" style="background-color: #5865F2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block;">
              Tham gia Discord Server
            </a>
          </div>
          <p style="color: #333; font-size: 16px; line-height: 1.6;">
            Link này sẽ hết hạn sau 30 ngày. Nếu bạn gặp bất kỳ vấn đề gì, vui lòng liên hệ với chúng tôi.
          </p>
          <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            <p style="color: #666; font-size: 14px; margin: 0;">
              <strong>Lưu ý:</strong> Đây là email tự động, vui lòng không trả lời email này.
            </p>
          </div>
          <hr style="border: none; border-top: 1px solid #e9ecef; margin: 20px 0;">
          <p style="color: #666; font-size: 14px; text-align: center;">Trân trọng,<br><strong>Đội ngũ Kitsify</strong></p>
        </div>
        <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
          <p>© ${new Date().getFullYear()} Kitsify. Tất cả các quyền được bảo lưu.</p>
        </div>
      </div>
    `;

    try {
      await this.mailerService.sendMail({
        to: user.email,
        from: {
          name: 'Kitsify',
          address: this.configService.get('mail.from'),
        },
        subject,
        html,
        headers: {
          'Message-ID': messageId,
          'List-Unsubscribe': `<https://kitsify.com/unsubscribe?uid=${user.id}>, <mailto:<EMAIL>>`,
          'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
          'Feedback-ID': `discord-invite:${user.id}:kitsify:${process.env.MAIL_FEEDBACK_ID}`,
          Precedence: 'bulk',
          'X-Auto-Response-Suppress': 'OOF, AutoReply',
          'X-Entity-Ref-ID': `kitsify-discord-invite-${user.id}`,
        },
      });
      this.logger.log(
        `Successfully sent Discord invite email to ${user.email}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send Discord invite email to ${user.email}:`,
        error,
      );
      throw error;
    }
  }

  async isValidInvite(
    inviteCode: string,
  ): Promise<{ valid: boolean; inviteLink?: InviteLink; error?: string }> {
    try {
      const inviteLink = await this.findByInviteCode(inviteCode);

      // Check if invite is already used
      if (inviteLink.used) {
        return { valid: false, error: 'Invite link has already been used' };
      }

      // Check if invite is expired
      if (new Date() > inviteLink.expires_at) {
        return { valid: false, error: 'Invite link has expired' };
      }

      return { valid: true, inviteLink };
    } catch (error) {
      return { valid: false, error: error.message || 'Invalid invite link' };
    }
  }
}
