import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InviteLinksController } from './invite-links.controller';
import { InviteLinksService } from './invite-links.service';
import { InviteLink } from './entities/invite-link.entity';
import { DiscordModule } from '../discord/discord.module';

@Module({
  imports: [TypeOrmModule.forFeature([InviteLink]), DiscordModule],
  controllers: [InviteLinksController],
  providers: [InviteLinksService],
  exports: [InviteLinksService],
})
export class InviteLinksModule {}
