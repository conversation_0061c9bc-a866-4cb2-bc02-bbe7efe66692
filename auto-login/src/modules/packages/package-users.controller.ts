import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  NotFoundException,
  Request,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Roles } from '../auth/roles.decorator';
import { RolesGuard } from '../auth/roles.guard';
import { PackageUsersService } from './package-users.service';
import { CreatePackageUserDto } from './dto/create-package-user.dto';
import { UpdatePackageUserDto } from './dto/update-package-user.dto';

@Controller('package-users')
@UseGuards(JwtAuthGuard)
export class PackageUsersController {
  constructor(private readonly packageUsersService: PackageUsersService) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles('admin')
  async create(@Body() createPackageUserDto: CreatePackageUserDto) {
    try {
      return await this.packageUsersService.create(createPackageUserDto);
    } catch (error) {
      throw new HttpException(
        'Failed to create package user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @UseGuards(RolesGuard)
  @Roles('admin')
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 10,
    @Query('userId', new ParseIntPipe({ optional: true })) userId?: number,
    @Query('packageId', new ParseIntPipe({ optional: true }))
    packageId?: number,
    @Query('status') status?: string,
  ) {
    try {
      return await this.packageUsersService.findAll(
        page,
        limit,
        userId,
        packageId,
        status,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve package users',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('my')
  async findMyPackages(@Request() req) {
    try {
      const userId = req.user.sub;
      return await this.packageUsersService.findUserPackages(userId, 1, 100);
    } catch (error) {
      console.error('Error in findMyPackages:', error);
      throw new HttpException(
        `Failed to retrieve your packages: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.packageUsersService.findOne(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to retrieve package user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePackageUserDto: UpdatePackageUserDto,
  ) {
    try {
      return await this.packageUsersService.update(id, updatePackageUserDto);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to update package user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async remove(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.packageUsersService.remove(id);
      return { message: 'Package user deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to delete package user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
