import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Unique,
} from 'typeorm';
import { Package } from './package.entity';
import { PaymentMethod } from '../../products/entities/payment-method.entity';

@Entity('package_discounts')
@Unique(['package_id', 'payment_method_id'])
export class PackageDiscount {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'package_id' })
  package_id: number;

  @Column({ name: 'payment_method_id' })
  payment_method_id: number;

  @Column('decimal', { precision: 5, scale: 2, default: 0, name: 'discount_percent' })
  discount_percent: number;

  @ManyToOne(() => Package, (package_) => package_.packageDiscounts)
  @JoinColumn({ name: 'package_id' })
  package: Package;

  @ManyToOne(() => PaymentMethod, (paymentMethod) => paymentMethod.packageDiscounts)
  @JoinColumn({ name: 'payment_method_id' })
  paymentMethod: PaymentMethod;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;
}
