import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { PackageUser } from '../../packages/entities/package-user.entity';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @Column({ nullable: true })
  plain_password: string;

  @Column({ default: 'inactive' })
  status: string;

  @Column({ nullable: true, unique: true })
  discord_id: string;

  @Column({ nullable: true, unique: true })
  google_id: string;

  @Column({ default: 'user' })
  role: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @OneToMany(() => PackageUser, (packageUser) => packageUser.user)
  packageUsers: PackageUser[];
}
