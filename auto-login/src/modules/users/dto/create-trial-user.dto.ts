import {
  IsEmail,
  IsNotEmpty,
  <PERSON><PERSON>nt,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
} from 'class-validator';

export class CreateTrialUserDto {
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(7)
  trial_days: number = 5;

  @IsNotEmpty()
  @IsInt()
  package_id: number;
}
