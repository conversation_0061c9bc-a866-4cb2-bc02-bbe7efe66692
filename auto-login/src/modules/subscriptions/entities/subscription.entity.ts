import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { PackageDuration } from '../../packages/entities/package-duration.entity';

@Entity('subscriptions')
export class Subscription {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  user_id: number;

  @Column({ unique: true })
  paypal_subscription_id: string;

  @Column()
  paypal_plan_id: string;

  @Column({ nullable: true })
  package_duration_id: number;

  @Column({ default: 'pending' })
  status: string; // pending, active, cancelled, suspended, expired

  @Column({ type: 'timestamp' })
  start_date: Date;

  @Column({ type: 'timestamp', nullable: true })
  next_billing_date: Date;

  @Column({ type: 'timestamp', nullable: true })
  expires_at: Date;

  @Column('decimal', { precision: 10, scale: 2 })
  amount: number;

  @Column({ default: 'USD' })
  currency: string;

  @Column({ type: 'timestamp', nullable: true })
  cancelled_at: Date;

  @Column({ nullable: true })
  cancel_reason: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => PackageDuration)
  @JoinColumn({ name: 'package_duration_id' })
  packageDuration: PackageDuration;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
