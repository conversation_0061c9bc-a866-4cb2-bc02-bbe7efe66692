import {
  <PERSON>,
  <PERSON>,
  Body,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { SubscriptionsService } from './subscriptions.service';
import { SubscriptionEmailService } from './services/subscription-email.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Subscription } from './entities/subscription.entity';

interface PayPalWebhookEvent {
  id: string;
  event_type: string;
  create_time: string;
  resource_type: string;
  resource_version: string;
  event_version: string;
  summary: string;
  resource: {
    id: string;
    status: string;
    status_update_time: string;
    plan_id: string;
    start_time: string;
    quantity: string;
    billing_info?: {
      outstanding_balance: {
        currency_code: string;
        value: string;
      };
      cycle_executions: Array<{
        tenure_type: string;
        sequence: number;
        cycles_completed: number;
        cycles_remaining: number;
      }>;
      last_payment?: {
        amount: {
          currency_code: string;
          value: string;
        };
        time: string;
      };
      next_billing_time?: string;
      failed_payments_count?: number;
    };
    subscriber?: {
      email_address: string;
      payer_id: string;
    };
  };
  links: Array<{
    href: string;
    rel: string;
    method: string;
  }>;
}

@Controller('webhooks/paypal')
export class WebhooksController {
  private readonly logger = new Logger(WebhooksController.name);

  constructor(
    private _subscriptionsService: SubscriptionsService,
    private subscriptionEmailService: SubscriptionEmailService,
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
  ) {}

  @Post('subscriptions')
  async handleSubscriptionWebhook(@Body() event: PayPalWebhookEvent) {
    try {
      // Verify webhook signature (optional but recommended)
      // const isValid = await this.verifyWebhookSignature(headers, rawBody);
      // if (!isValid) {
      //   throw new HttpException('Invalid webhook signature', HttpStatus.UNAUTHORIZED);
      // }

      this.logger.log(
        `Received PayPal webhook: ${event.event_type} for subscription: ${event.resource.id}`,
      );

      const subscriptionId = event.resource.id;
      const subscription = await this.subscriptionRepository.findOne({
        where: { paypal_subscription_id: subscriptionId },
        relations: ['user', 'packageDuration', 'packageDuration.package'],
      });

      if (!subscription) {
        this.logger.warn(
          `Subscription not found for PayPal ID: ${subscriptionId}`,
        );
        return { status: 'ignored', message: 'Subscription not found' };
      }

      switch (event.event_type) {
        case 'BILLING.SUBSCRIPTION.ACTIVATED':
          await this.handleSubscriptionActivated(subscription, event);
          break;

        case 'BILLING.SUBSCRIPTION.CANCELLED':
          await this.handleSubscriptionCancelled(subscription, event);
          break;

        case 'BILLING.SUBSCRIPTION.SUSPENDED':
          await this.handleSubscriptionSuspended(subscription);
          break;

        case 'BILLING.SUBSCRIPTION.PAYMENT.FAILED':
          await this.handlePaymentFailed(subscription, event);
          break;

        case 'BILLING.SUBSCRIPTION.PAYMENT.COMPLETED':
          await this.handlePaymentCompleted(subscription, event);
          break;

        case 'BILLING.SUBSCRIPTION.EXPIRED':
          await this.handleSubscriptionExpired(subscription);
          break;

        default:
          this.logger.log(`Unhandled webhook event type: ${event.event_type}`);
          break;
      }

      return { status: 'success', message: 'Webhook processed successfully' };
    } catch (error) {
      this.logger.error('Error processing PayPal webhook:', error);
      throw new HttpException(
        'Error processing webhook',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async handleSubscriptionActivated(
    subscription: Subscription,
    event: PayPalWebhookEvent,
  ) {
    this.logger.log(`Activating subscription: ${subscription.id}`);

    await this.subscriptionRepository.update(subscription.id, {
      status: 'active',
      start_date: new Date(event.resource.start_time),
      next_billing_date: event.resource.billing_info?.next_billing_time
        ? new Date(event.resource.billing_info.next_billing_time)
        : null,
    });

    // Update user's package access
    await this.updateUserPackageAccess(subscription);

    // Send confirmation email
    try {
      await this.subscriptionEmailService.sendSubscriptionConfirmationEmail(
        subscription,
        subscription.user,
      );
    } catch (emailError) {
      this.logger.error(
        `Failed to send subscription confirmation email for subscription ${subscription.id}:`,
        emailError,
      );
      // Don't throw the error to prevent webhook failure
    }
  }

  private async handleSubscriptionCancelled(
    subscription: Subscription,
    event: PayPalWebhookEvent,
  ) {
    this.logger.log(`Cancelling subscription: ${subscription.id}`);

    await this.subscriptionRepository.update(subscription.id, {
      status: 'cancelled',
      cancelled_at: new Date(event.resource.status_update_time),
    });

    // Remove user's package access or set expiration
    await this.removeUserPackageAccess(subscription);

    // Send cancellation email
    try {
      await this.subscriptionEmailService.sendSubscriptionCancellationEmail(
        subscription,
        subscription.user,
      );
    } catch (emailError) {
      this.logger.error(
        `Failed to send subscription cancellation email for subscription ${subscription.id}:`,
        emailError,
      );
      // Don't throw the error to prevent webhook failure
    }
  }

  private async handleSubscriptionSuspended(subscription: Subscription) {
    this.logger.log(`Suspending subscription: ${subscription.id}`);

    await this.subscriptionRepository.update(subscription.id, {
      status: 'suspended',
    });

    // Optionally suspend user's package access
    await this.suspendUserPackageAccess(subscription);
  }

  private async handlePaymentFailed(
    subscription: Subscription,
    event: PayPalWebhookEvent,
  ) {
    this.logger.log(`Payment failed for subscription: ${subscription.id}`);

    // Update next billing date if available
    if (event.resource.billing_info?.next_billing_time) {
      await this.subscriptionRepository.update(subscription.id, {
        next_billing_date: new Date(
          event.resource.billing_info.next_billing_time,
        ),
      });
    }

    // Send payment failure notification email
    try {
      await this.subscriptionEmailService.sendPaymentFailureEmail(
        subscription,
        subscription.user,
      );
    } catch (emailError) {
      this.logger.error(
        `Failed to send payment failure email for subscription ${subscription.id}:`,
        emailError,
      );
      // Don't throw the error to prevent webhook failure
    }
  }

  private async handlePaymentCompleted(
    subscription: Subscription,
    event: PayPalWebhookEvent,
  ) {
    this.logger.log(`Payment completed for subscription: ${subscription.id}`);

    // Update next billing date
    if (event.resource.billing_info?.next_billing_time) {
      await this.subscriptionRepository.update(subscription.id, {
        next_billing_date: new Date(
          event.resource.billing_info.next_billing_time,
        ),
      });
    }

    // Ensure user's package access is active
    await this.updateUserPackageAccess(subscription);

    // Send payment success notification email
    try {
      await this.subscriptionEmailService.sendPaymentSuccessEmail(
        subscription,
        subscription.user,
      );
    } catch (emailError) {
      this.logger.error(
        `Failed to send payment success email for subscription ${subscription.id}:`,
        emailError,
      );
      // Don't throw the error to prevent webhook failure
    }
  }

  private async handleSubscriptionExpired(subscription: Subscription) {
    this.logger.log(`Subscription expired: ${subscription.id}`);

    await this.subscriptionRepository.update(subscription.id, {
      status: 'expired',
    });

    // Remove user's package access
    await this.removeUserPackageAccess(subscription);
  }

  private async updateUserPackageAccess(subscription: Subscription) {
    this.logger.log(`Updating package access for user ${subscription.user_id}`);
    // Delegate to the subscriptions service which has the full implementation
    await this._subscriptionsService.updateUserPackageAccess(subscription);
  }

  private async removeUserPackageAccess(subscription: Subscription) {
    this.logger.log(`Removing package access for user ${subscription.user_id}`);
    // Delegate to the subscriptions service which has the full implementation
    await this._subscriptionsService.removeUserPackageAccess(subscription);
  }

  private async suspendUserPackageAccess(subscription: Subscription) {
    this.logger.log(
      `Suspending package access for user ${subscription.user_id}`,
    );
    // For suspension, we can remove access temporarily
    // This could be enhanced to have a separate 'suspended' status
    await this._subscriptionsService.removeUserPackageAccess(subscription);
  }
}
