import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { SubscriptionsService } from '../subscriptions.service';

@Injectable()
export class SubscriptionSchedulerService {
  private readonly logger = new Logger(SubscriptionSchedulerService.name);

  constructor(private subscriptionsService: SubscriptionsService) {}

  // Run every day at midnight to check for expired subscriptions
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleExpiredSubscriptions() {
    this.logger.log('Checking for expired subscriptions...');

    try {
      const expiredCount =
        await this.subscriptionsService.checkExpiredSubscriptions();

      if (expiredCount > 0) {
        this.logger.log(
          `Found and processed ${expiredCount} expired subscriptions`,
        );
      } else {
        this.logger.log('No expired subscriptions found');
      }
    } catch (error) {
      this.logger.error('Error checking expired subscriptions:', error);
    }
  }

  // Run daily at midnight to log subscription statistics
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async logSubscriptionStats() {
    this.logger.log('Generating daily subscription statistics...');

    try {
      // You can add more detailed statistics here
      this.logger.log('Daily subscription check completed');
    } catch (error) {
      this.logger.error('Error generating subscription statistics:', error);
    }
  }
}
