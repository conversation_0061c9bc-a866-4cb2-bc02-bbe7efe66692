import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';
import { Subscription } from '../entities/subscription.entity';
import { User } from '../../users/entities/user.entity';

@Injectable()
export class SubscriptionEmailService {
  private readonly logger = new Logger(SubscriptionEmailService.name);

  constructor(
    private mailerService: MailerService,
    private configService: ConfigService,
  ) {}

  async sendSubscriptionConfirmationEmail(
    subscription: Subscription,
    user: User,
  ): Promise<void> {
    try {
      // Validate required data
      if (!user.email) {
        throw new Error('User email is required');
      }

      if (!subscription.id) {
        throw new Error('Subscription ID is required');
      }

      const messageId = `<subscription-confirmation-${subscription.id}-${Date.now()}@kitsify.com>`;
      const subject = `[Kitsify] Thank you for your purchase - Welcome to Kitsify Service`;

      const monthlyAmount =
        parseFloat(subscription.amount?.toString() || '0') || 0;

      const packageName =
        subscription.packageDuration?.package?.name || 'Premium Package';

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Subscription Activated</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .highlight { background: #e8f4fd; padding: 15px; border-radius: 8px; margin: 20px 0; }
            .button { display: inline-block; background: #4CAF50; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎉 Subscription Activated!</h1>
              <p>Welcome to Kitsify Premium</p>
            </div>
            
            <div class="content">
              <h2>Hello ${user.email}!</h2>
              
              <p>Great news! Your subscription to <strong>${packageName}</strong> has been successfully activated.</p>
              
              <div class="highlight">
                <h3>📋 Subscription Details:</h3>
                <ul>
                  <li><strong>Plan:</strong> ${packageName}</li>
                  <li><strong>Monthly Amount:</strong> $${monthlyAmount.toFixed(2)} USD</li>
                  <li><strong>Status:</strong> Active</li>
                  <li><strong>Next Billing:</strong> ${subscription.next_billing_date ? new Date(subscription.next_billing_date).toLocaleDateString() : 'N/A'}</li>
                </ul>
              </div>

              <h3>🚀 What's Next?</h3>
              <ol>
                <li><strong>Install the Chrome Extension:</strong>
                  <br>
                  <a href="https://chromewebstore.google.com/detail/hbdinbncknklkkjeehlkngpbhhadenhn?utm_source=item-share-cb"
                    style="display: inline-block; background-color: #2b3481; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 10px; font-weight: bold;">
                    📥 Download Chrome Extension
                  </a>
                </li>
                <li><strong>Start Using the Tools:</strong>
                  <br>
                  <a href="https://kitsify.com/tools"
                    style="display: inline-block; background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 10px; font-weight: bold;">
                    🛠️ Access Your Tools
                  </a>
                </li>
                <li><strong>Manage Your Subscription:</strong> You can view and manage your subscription anytime in your account dashboard</li>
              </ol>

              <h3>💡 Need Help?</h3>
              <p>If you have any questions or need assistance, please don't hesitate to contact our support team. We're here to help!</p>
              
              <p>Thank you for choosing Kitsify!</p>
              
              <p>Best regards,<br>The Kitsify Team</p>
            </div>
            
            <div class="footer">
              <p>This email was sent to ${user.email}</p>
              <p>© ${new Date().getFullYear()} Kitsify. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `;

      const mailFrom = this.configService.get('mail.from');

      const emailOptions = {
        to: user.email,
        from: {
          name: 'Kitsify',
          address: mailFrom,
        },
        subject: subject,
        html: htmlContent,
        headers: {
          'Message-ID': messageId,
          'List-Unsubscribe': `<https://kitsify.com/unsubscribe?uid=${user.id}>, <mailto:<EMAIL>>`,
          'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
          'Feedback-ID': `subscription-confirmation:${user.id}:kitsify:${process.env.MAIL_FEEDBACK_ID}`,
          Precedence: 'bulk',
          'X-Auto-Response-Suppress': 'OOF, AutoReply',
          'X-Entity-Ref-ID': `kitsify-subscription-confirmation-${user.id}`,
        },
      };

      await this.mailerService.sendMail(emailOptions);

      this.logger.log(
        `✅ Subscription confirmation email successfully sent to ${user.email} for subscription ${subscription.id}`,
      );
    } catch (error) {
      this.logger.error(
        `❌ Failed to send subscription confirmation email to ${user.email} for subscription ${subscription.id}:`,
        {
          error: error.message,
          stack: error.stack,
          subscriptionId: subscription.id,
          userEmail: user.email,
          mailConfig: {
            host: this.configService.get('mail.host'),
            port: this.configService.get('mail.port'),
            from: this.configService.get('mail.from'),
            user: this.configService.get('mail.user'),
          },
        },
      );

      // Log specific error types for better debugging
      if (error.code === 'EAUTH') {
        this.logger.error(
          'SMTP Authentication failed. Check MAIL_USER and MAIL_PASSWORD.',
        );
      } else if (error.code === 'ECONNECTION') {
        this.logger.error(
          'SMTP Connection failed. Check MAIL_HOST and MAIL_PORT.',
        );
      } else if (error.code === 'EMESSAGE') {
        this.logger.error('Email message format error. Check email content.');
      }

      throw error;
    }
  }

  async sendSubscriptionCancellationEmail(
    subscription: Subscription,
    user: User,
  ): Promise<void> {
    try {
      const messageId = `<subscription-cancellation-${subscription.id}-${Date.now()}@kitsify.com>`;
      const subject = `[Kitsify] Subscription Cancelled - We're Sorry to See You Go`;

      const packageName =
        subscription.packageDuration?.package?.name || 'Premium Package';

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Subscription Cancelled</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .highlight { background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107; }
            .button { display: inline-block; background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>😔 Subscription Cancelled</h1>
              <p>We're sorry to see you go</p>
            </div>
            
            <div class="content">
              <h2>Hello ${user.email}!</h2>
              
              <p>We've processed your request to cancel your subscription to <strong>${packageName}</strong>.</p>
              
              <div class="highlight">
                <h3>📋 Cancellation Details:</h3>
                <ul>
                  <li><strong>Plan:</strong> ${packageName}</li>
                  <li><strong>Cancelled Date:</strong> ${subscription.cancelled_at ? new Date(subscription.cancelled_at).toLocaleDateString() : new Date().toLocaleDateString()}</li>
                  <li><strong>Status:</strong> Cancelled</li>
                </ul>
              </div>

              <h3>🔄 What Happens Next?</h3>
              <ul>
                <li>Your subscription has been cancelled and you will not be charged again</li>
                <li>You may continue to use the service until the end of your current billing period</li>
                <li>Your account and data will be preserved in case you decide to resubscribe</li>
              </ul>

              <h3>💭 We'd Love Your Feedback</h3>
              <p>Your feedback is valuable to us. If you have a moment, please let us know why you cancelled and how we can improve our service.</p>

              <div style="text-align: center;">
                <a href="https://kitsify.com/subscribe" class="button">Resubscribe Anytime</a>
              </div>

              <p>Thank you for being part of the Kitsify community. We hope to see you again soon!</p>
              
              <p>Best regards,<br>The Kitsify Team</p>
            </div>
            
            <div class="footer">
              <p>This email was sent to ${user.email}</p>
              <p>© ${new Date().getFullYear()} Kitsify. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `;

      await this.mailerService.sendMail({
        to: user.email,
        from: {
          name: 'Kitsify',
          address: this.configService.get('mail.from'),
        },
        subject: subject,
        html: htmlContent,
        headers: {
          'Message-ID': messageId,
          'List-Unsubscribe': `<https://kitsify.com/unsubscribe?uid=${user.id}>, <mailto:<EMAIL>>`,
          'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
          'Feedback-ID': `subscription-cancellation:${user.id}:kitsify:${process.env.MAIL_FEEDBACK_ID}`,
          Precedence: 'bulk',
          'X-Auto-Response-Suppress': 'OOF, AutoReply',
          'X-Entity-Ref-ID': `kitsify-subscription-cancellation-${user.id}`,
        },
      });

      this.logger.log(
        `Subscription cancellation email sent to ${user.email} for subscription ${subscription.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send subscription cancellation email to ${user.email}:`,
        error,
      );
      throw error;
    }
  }

  async sendPaymentFailureEmail(
    subscription: Subscription,
    user: User,
  ): Promise<void> {
    try {
      const messageId = `<payment-failure-${subscription.id}-${Date.now()}@kitsify.com>`;
      const subject = `[Kitsify] Payment Failed - Action Required`;

      const packageName =
        subscription.packageDuration?.package?.name || 'Premium Package';
      // Safely convert amount to number and handle edge cases
      const monthlyAmount =
        parseFloat(subscription.amount?.toString() || '0') || 0;

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Payment Failed</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .alert { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545; }
            .button { display: inline-block; background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>⚠️ Payment Failed</h1>
              <p>Action Required for Your Subscription</p>
            </div>
            
            <div class="content">
              <h2>Hello ${user.email}!</h2>
              
              <div class="alert">
                <h3>🚨 Payment Issue Detected</h3>
                <p>We were unable to process your payment for your <strong>${packageName}</strong> subscription.</p>
              </div>
              
              <h3>📋 Subscription Details:</h3>
              <ul>
                <li><strong>Plan:</strong> ${packageName}</li>
                <li><strong>Amount:</strong> $${monthlyAmount.toFixed(2)} USD</li>
                <li><strong>Next Retry:</strong> ${subscription.next_billing_date ? new Date(subscription.next_billing_date).toLocaleDateString() : 'Soon'}</li>
              </ul>

              <h3>🔧 What You Can Do:</h3>
              <ol>
                <li><strong>Update Payment Method:</strong> Log in to your account and update your payment information</li>
                <li><strong>Check Your Bank:</strong> Ensure your card has sufficient funds and hasn't expired</li>
                <li><strong>Contact Support:</strong> If you need assistance, our support team is here to help</li>
              </ol>

              <div style="text-align: center;">
                <a href="https://kitsify.com/account/billing" class="button">Update Payment Method</a>
              </div>

              <p><strong>Important:</strong> If payment continues to fail, your subscription may be suspended. We'll retry the payment automatically, but updating your payment method ensures uninterrupted service.</p>
              
              <p>Thank you for your prompt attention to this matter.</p>
              
              <p>Best regards,<br>The Kitsify Team</p>
            </div>
            
            <div class="footer">
              <p>This email was sent to ${user.email}</p>
              <p>© ${new Date().getFullYear()} Kitsify. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `;

      await this.mailerService.sendMail({
        to: user.email,
        from: {
          name: 'Kitsify',
          address: this.configService.get('mail.from'),
        },
        subject: subject,
        html: htmlContent,
        headers: {
          'Message-ID': messageId,
          'List-Unsubscribe': `<https://kitsify.com/unsubscribe?uid=${user.id}>, <mailto:<EMAIL>>`,
          'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
          'Feedback-ID': `payment-failure:${user.id}:kitsify:${process.env.MAIL_FEEDBACK_ID}`,
          Precedence: 'bulk',
          'X-Auto-Response-Suppress': 'OOF, AutoReply',
          'X-Entity-Ref-ID': `kitsify-payment-failure-${user.id}`,
        },
      });

      this.logger.log(
        `Payment failure email sent to ${user.email} for subscription ${subscription.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send payment failure email to ${user.email}:`,
        error,
      );
      throw error;
    }
  }

  async sendPaymentSuccessEmail(
    subscription: Subscription,
    user: User,
  ): Promise<void> {
    try {
      const messageId = `<payment-success-${subscription.id}-${Date.now()}@kitsify.com>`;
      const subject = `[Kitsify] Payment Successful - Thank You!`;

      const packageName =
        subscription.packageDuration?.package?.name || 'Premium Package';
      // Safely convert amount to number and handle edge cases
      const monthlyAmount =
        parseFloat(subscription.amount?.toString() || '0') || 0;

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Payment Successful</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745; }
            .button { display: inline-block; background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>✅ Payment Successful!</h1>
              <p>Thank you for your continued subscription</p>
            </div>
            
            <div class="content">
              <h2>Hello ${user.email}!</h2>
              
              <div class="success">
                <h3>🎉 Payment Processed Successfully</h3>
                <p>Your payment for <strong>${packageName}</strong> has been processed successfully.</p>
              </div>
              
              <h3>📋 Payment Details:</h3>
              <ul>
                <li><strong>Plan:</strong> ${packageName}</li>
                <li><strong>Amount Paid:</strong> $${monthlyAmount.toFixed(2)} USD</li>
                <li><strong>Payment Date:</strong> ${new Date().toLocaleDateString()}</li>
                <li><strong>Next Billing:</strong> ${subscription.next_billing_date ? new Date(subscription.next_billing_date).toLocaleDateString() : 'Next month'}</li>
              </ul>

              <h3>🚀 Your Service Continues</h3>
              <p>Your subscription is active and you can continue enjoying all the premium features of Kitsify.</p>

              <div style="text-align: center;">
                <a href="https://kitsify.com/tools" class="button">Access Your Tools</a>
              </div>

              <p>Thank you for being a valued Kitsify subscriber!</p>
              
              <p>Best regards,<br>The Kitsify Team</p>
            </div>
            
            <div class="footer">
              <p>This email was sent to ${user.email}</p>
              <p>© ${new Date().getFullYear()} Kitsify. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `;

      await this.mailerService.sendMail({
        to: user.email,
        from: {
          name: 'Kitsify',
          address: this.configService.get('mail.from'),
        },
        subject: subject,
        html: htmlContent,
        headers: {
          'Message-ID': messageId,
          'List-Unsubscribe': `<https://kitsify.com/unsubscribe?uid=${user.id}>, <mailto:<EMAIL>>`,
          'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
          'Feedback-ID': `payment-success:${user.id}:kitsify:${process.env.MAIL_FEEDBACK_ID}`,
          Precedence: 'bulk',
          'X-Auto-Response-Suppress': 'OOF, AutoReply',
          'X-Entity-Ref-ID': `kitsify-payment-success-${user.id}`,
        },
      });

      this.logger.log(
        `Payment success email sent to ${user.email} for subscription ${subscription.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send payment success email to ${user.email}:`,
        error,
      );
      throw error;
    }
  }
}
