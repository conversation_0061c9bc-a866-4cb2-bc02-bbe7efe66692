import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as paypal from '@paypal/checkout-server-sdk';
import axios from 'axios';

interface PayPalPlan {
  id: string;
  name: string;
  description: string;
  status: string;
  billing_cycles: Array<{
    frequency: {
      interval_unit: string;
      interval_count: number;
    };
    tenure_type: string;
    sequence: number;
    total_cycles: number;
    pricing_scheme: {
      fixed_price: {
        value: string;
        currency_code: string;
      };
    };
  }>;
  payment_preferences: {
    auto_bill_outstanding: boolean;
    setup_fee_failure_action: string;
    payment_failure_threshold: number;
  };
  taxes: {
    percentage: string;
    inclusive: boolean;
  };
}

@Injectable()
export class PayPalPlansService {
  private readonly logger = new Logger(PayPalPlansService.name);
  private _paypalClient: paypal.core.PayPalHttpClient;
  private baseURL: string;

  constructor(private configService: ConfigService) {
    const clientId = this.configService.get<string>('paypal.clientId');
    const clientSecret = this.configService.get<string>('paypal.clientSecret');
    const isProduction =
      this.configService.get<string>('NODE_ENV') === 'production';

    const environment = isProduction
      ? new paypal.core.LiveEnvironment(clientId, clientSecret)
      : new paypal.core.SandboxEnvironment(clientId, clientSecret);

    this._paypalClient = new paypal.core.PayPalHttpClient(environment);
    this.baseURL = isProduction
      ? 'https://api.paypal.com'
      : 'https://api.sandbox.paypal.com';
  }

  async getAccessToken(): Promise<string> {
    try {
      const clientId = this.configService.get<string>('paypal.clientId');
      const clientSecret = this.configService.get<string>(
        'paypal.clientSecret',
      );

      const auth = Buffer.from(`${clientId}:${clientSecret}`).toString(
        'base64',
      );

      const response = await axios.post(
        `${this.baseURL}/v1/oauth2/token`,
        'grant_type=client_credentials',
        {
          headers: {
            Authorization: `Basic ${auth}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      return (response.data as { access_token: string }).access_token;
    } catch (error) {
      this.logger.error('Failed to get PayPal access token:', error);
      throw new HttpException(
        'Failed to authenticate with PayPal',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createBillingPlan(
    packageName: string,
    amount: number,
    durationDays: number,
    description?: string,
  ): Promise<PayPalPlan> {
    try {
      const accessToken = await this.getAccessToken();

      // Calculate billing cycle based on duration
      const billingCycle = this.calculateBillingCycle(durationDays);

      const planData = {
        product_id: await this.createOrGetProduct(packageName, description),
        name: `${packageName} - ${billingCycle.displayName} Subscription`,
        description:
          description ||
          `${billingCycle.displayName} subscription for ${packageName}`,
        status: 'ACTIVE',
        billing_cycles: [
          {
            frequency: {
              interval_unit: billingCycle.intervalUnit,
              interval_count: billingCycle.intervalCount,
            },
            tenure_type: 'REGULAR',
            sequence: 1,
            total_cycles: 0, // 0 means infinite recurring cycles
            pricing_scheme: {
              fixed_price: {
                value: amount.toFixed(2),
                currency_code: 'USD',
              },
            },
          },
        ],
        payment_preferences: {
          auto_bill_outstanding: true,
          setup_fee_failure_action: 'CONTINUE',
          payment_failure_threshold: 3,
        },
        taxes: {
          percentage: '0.00',
          inclusive: false,
        },
      };

      const response = await axios.post(
        `${this.baseURL}/v1/billing/plans`,
        planData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            Accept: 'application/json',
            'PayPal-Request-Id': `plan-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          },
        },
      );

      const responseData = response.data as PayPalPlan;
      this.logger.log(`Created PayPal billing plan: ${responseData.id}`);
      return responseData;
    } catch (error) {
      this.logger.error('Failed to create PayPal billing plan:', error);
      throw new HttpException(
        'Failed to create billing plan',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async createOrGetProduct(
    name: string,
    description?: string,
  ): Promise<string> {
    try {
      const accessToken = await this.getAccessToken();

      const productData = {
        name: name,
        description: description || `Product for ${name}`,
        type: 'SERVICE',
        category: 'SOFTWARE',
      };

      const response = await axios.post(
        `${this.baseURL}/v1/catalogs/products`,
        productData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            Accept: 'application/json',
            'PayPal-Request-Id': `product-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          },
        },
      );

      return (response.data as { id: string }).id;
    } catch (error) {
      // If product already exists, try to find it
      this.logger.warn(
        'Product creation failed, might already exist:',
        error.response?.data,
      );
      // For now, return a default product ID or create a unique one
      return `PRODUCT-${Date.now()}`;
    }
  }

  private calculateBillingCycle(durationDays: number): {
    intervalUnit: string;
    intervalCount: number;
    displayName: string;
  } {
    // Map duration days to appropriate billing cycles
    if (durationDays <= 31) {
      // 1 month (30-31 days)
      return {
        intervalUnit: 'MONTH',
        intervalCount: 1,
        displayName: '1 Month',
      };
    } else if (durationDays <= 93) {
      // 3 months (90-93 days)
      return {
        intervalUnit: 'MONTH',
        intervalCount: 3,
        displayName: '3 Months',
      };
    } else if (durationDays <= 186) {
      // 6 months (180-186 days)
      return {
        intervalUnit: 'MONTH',
        intervalCount: 6,
        displayName: '6 Months',
      };
    } else if (durationDays <= 366) {
      // 12 months (365-366 days)
      return {
        intervalUnit: 'YEAR',
        intervalCount: 1,
        displayName: '1 Year',
      };
    } else {
      // For custom durations, use days
      return {
        intervalUnit: 'DAY',
        intervalCount: durationDays,
        displayName: `${durationDays} Days`,
      };
    }
  }

  async getPlan(planId: string): Promise<PayPalPlan> {
    try {
      const accessToken = await this.getAccessToken();

      const response = await axios.get(
        `${this.baseURL}/v1/billing/plans/${planId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data as PayPalPlan;
    } catch (error) {
      this.logger.error(`Failed to get PayPal plan ${planId}:`, error);
      throw new HttpException(
        'Failed to retrieve billing plan',
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async deactivatePlan(planId: string): Promise<void> {
    try {
      const accessToken = await this.getAccessToken();

      await axios.post(
        `${this.baseURL}/v1/billing/plans/${planId}/deactivate`,
        {},
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      );

      this.logger.log(`Deactivated PayPal billing plan: ${planId}`);
    } catch (error) {
      this.logger.error(`Failed to deactivate PayPal plan ${planId}:`, error);
      throw new HttpException(
        'Failed to deactivate billing plan',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
