import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { Subscription } from './entities/subscription.entity';
import { SubscriptionsService } from './subscriptions.service';
import { SubscriptionsController } from './subscriptions.controller';
import { AdminSubscriptionsController } from './admin-subscriptions.controller';
import { WebhooksController } from './webhooks.controller';
import { PayPalPlansService } from './services/paypal-plans.service';
import { SubscriptionEmailService } from './services/subscription-email.service';
import { SubscriptionSchedulerService } from './services/subscription-scheduler.service';
import { PackagesModule } from '../packages/packages.module';
import { UsersModule } from '../users/users.module';
import { MailerModule } from '@nestjs-modules/mailer';

@Module({
  imports: [
    TypeOrmModule.forFeature([Subscription]),
    ScheduleModule.forRoot(),
    PackagesModule,
    UsersModule,
    MailerModule,
  ],
  providers: [
    SubscriptionsService,
    PayPalPlansService,
    SubscriptionEmailService,
    SubscriptionSchedulerService,
  ],
  controllers: [
    SubscriptionsController,
    AdminSubscriptionsController,
    WebhooksController,
  ],
  exports: [SubscriptionsService, PayPalPlansService],
})
export class SubscriptionsModule {}
