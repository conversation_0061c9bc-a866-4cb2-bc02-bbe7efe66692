import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import puppeteer from 'puppeteer';
import { Account } from './entities/account.entity';
import { ServerCookie } from './entities/server-cookie.entity';
import { Session } from '../sessions/entities/session.entity';
import { ConfigService } from '@nestjs/config';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import Redis from 'ioredis';
import { CreateAccountDto } from './dto/create-account.dto';
import { UpdateAccountDto } from './dto/update-account.dto';
import { PackageUser } from '../packages/entities/package-user.entity';
import { ProductUser } from '../products/entities/product-user.entity';
import { User } from '../users/entities/user.entity';

interface JwtPayload {
  sub: number;
  email: string;
}

interface RequestWithUser extends Request {
  user: JwtPayload;
}

@Injectable()
export class AccountsService {
  constructor(
    @InjectRepository(Account)
    private accountRepository: Repository<Account>,
    @InjectRepository(ServerCookie)
    private serverCookieRepository: Repository<ServerCookie>,
    @InjectRepository(Session)
    private sessionRepository: Repository<Session>,
    @InjectRepository(PackageUser)
    private packageUserRepository: Repository<PackageUser>,
    @InjectRepository(ProductUser)
    private productUserRepository: Repository<ProductUser>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private configService: ConfigService,
    @Inject(REQUEST) private request: RequestWithUser,
    @Inject('REDIS_CLIENT') private readonly redisClient: Redis,
  ) {}

  async create(createAccountDto: CreateAccountDto): Promise<Account> {
    const newAccount = this.accountRepository.create(createAccountDto);
    return this.accountRepository.save(newAccount);
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
    filter?: string,
    userId?: number,
    sortBy?: string,
    sortOrder?: 'ASC' | 'DESC',
  ) {
    const skip = (page - 1) * limit;
    const queryBuilder = this.accountRepository
      .createQueryBuilder('account')
      .leftJoinAndSelect('account.packages', 'packages')
      .skip(skip)
      .take(limit);

    // Apply sorting logic
    if (
      sortBy &&
      ['sort', 'created_at', 'updated_at', 'name', 'website_url'].includes(
        sortBy,
      )
    ) {
      queryBuilder.orderBy(`account.${sortBy}`, sortOrder || 'ASC');
      // Add secondary sort by created_at if not already sorting by it
      if (sortBy !== 'created_at') {
        queryBuilder.addOrderBy('account.created_at', 'DESC');
      }
    } else {
      // Default sorting: sort column first (ASC), then created_at (DESC)
      queryBuilder
        .orderBy('account.sort', 'ASC')
        .addOrderBy('account.created_at', 'DESC');
    }

    // Filter by user access through package_users or product_users if userId is provided
    if (userId) {
      queryBuilder.andWhere(
        `(
          EXISTS (
            SELECT 1 FROM package_users pu 
            JOIN account_packages ap ON ap.package_id = pu.package_id 
            WHERE ap.account_id = account.id 
            AND pu.user_id = :userId 
            AND pu.status = 'active'
          ) 
          OR 
          EXISTS (
            SELECT 1 FROM product_users pu2 
            JOIN account_products ap2 ON ap2.product_id = pu2.product_id 
            WHERE ap2.account_id = account.id 
            AND pu2.user_id = :userId 
            AND pu2.status = 'active'
          )
        )`,
        { userId },
      );
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere(
        '(account.website_url LIKE :search OR account.username LIKE :search OR account.name LIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Apply filter if provided
    if (filter === 'with_package') {
      queryBuilder.andWhere('packages.id IS NOT NULL');
    } else if (filter === 'without_package') {
      queryBuilder.andWhere('packages.id IS NULL');
    }

    const [accounts, total] = await queryBuilder.getManyAndCount();

    // Transform accounts to the requested format
    const transformedAccounts = accounts.map((account) => ({
      website_url: account.website_url,
      img_intro: account.img_intro,
      name: account.name,
      description: account.description,
      login_cookie: account.login_cookie,
      sort: account.sort,
      id: account.id,
    }));

    return {
      data: transformedAccounts,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number): Promise<Account> {
    const account = await this.accountRepository.findOne({
      where: { id },
      relations: ['packages'],
    });

    if (!account) {
      throw new NotFoundException(`Account with ID ${id} not found`);
    }

    return account;
  }

  async update(
    id: number,
    updateAccountDto: UpdateAccountDto,
  ): Promise<Account> {
    const account = await this.findOne(id);

    this.accountRepository.merge(account, updateAccountDto);
    return this.accountRepository.save(account);
  }

  async remove(id: number): Promise<void> {
    const account = await this.findOne(id);
    await this.accountRepository.remove(account);
  }

  async saveCookie(
    accountId: number,
    cookieData: string,
    userId: number,
    serverId: number = 1,
  ): Promise<Account> {
    // Validate that accountId is a number
    if (isNaN(accountId)) {
      throw new Error('Invalid account ID');
    }

    // Validate that serverId is a number
    if (isNaN(serverId)) {
      throw new Error('Invalid server ID');
    }

    const account = await this.accountRepository.findOne({
      where: { id: accountId },
      relations: ['packages'],
    });

    if (!account) {
      throw new NotFoundException(`Account with ID ${accountId} not found`);
    }

    // Check if user has access to this account through package_users
    const hasAccess = await this.userHasAccessToAccount(userId, accountId);
    if (!hasAccess) {
      throw new NotFoundException(
        `User does not have access to account with ID ${accountId}`,
      );
    }

    // Try to parse the cookie data as JSON to ensure it's in the correct format
    try {
      // If it's already JSON, this will parse successfully
      JSON.parse(cookieData);
    } catch (e) {
      // If it's not JSON, try to convert it to the required format
      cookieData = this.formatCookieString(cookieData);
    }

    // Use a transaction to ensure all operations are atomic
    const queryRunner =
      this.accountRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check if server cookie exists
      const existingCookie = await queryRunner.manager.query(
        `SELECT id FROM server_cookies WHERE "accountId" = $1 AND "serverId" = $2`,
        [accountId, serverId],
      );

      const now = new Date().toISOString();

      if (existingCookie && existingCookie.length > 0) {
        // Update existing cookie
        await queryRunner.manager.query(
          `UPDATE server_cookies
           SET "cookieData" = $1, "lastUsed" = $2, "updatedAt" = $3
           WHERE "accountId" = $4 AND "serverId" = $5`,
          [cookieData, now, now, accountId, serverId],
        );
      } else {
        // Insert new cookie
        await queryRunner.manager.query(
          `INSERT INTO server_cookies ("accountId", "serverId", "cookieData", "lastUsed", "createdAt", "updatedAt")
           VALUES ($1, $2, $3, $4, $5, $6)`,
          [accountId, serverId, cookieData, now, now, now],
        );
      }

      // Update account's cookie_data for backward compatibility
      await queryRunner.manager.query(
        `UPDATE accounts SET cookie_data = $1, updated_at = $2 WHERE id = $3`,
        [cookieData, now, accountId],
      );

      // Commit the transaction
      await queryRunner.commitTransaction();

      // Return the updated account
      return this.accountRepository.findOne({
        where: { id: accountId },
      });
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();
      console.error('Error saving cookie:', error);
      throw error;
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  async getCookie(
    accountId: number,
    userId: number,
    serverId: number = 1,
  ): Promise<{ cookieData: string } | null> {
    const account = await this.accountRepository.findOne({
      where: { id: accountId },
      relations: ['serverCookies'],
    });

    if (!account) {
      throw new NotFoundException(`Account with ID ${accountId} not found`);
    }

    // Check if the user has access to this account through package_users
    const hasAccess = await this.userHasAccessToAccount(userId, accountId);
    if (!hasAccess) {
      return null;
    }

    // Find server cookie for the specified server
    const serverCookie = await this.serverCookieRepository.findOne({
      where: {
        accountId,
        serverId,
      },
    });

    if (!serverCookie || !serverCookie.cookieData) {
      // If no server-specific cookie is found, try to use the account's cookie_data for backward compatibility
      if (!account.cookie_data) {
        return null;
      }
      return { cookieData: account.cookie_data };
    }

    // Update last used timestamp
    serverCookie.lastUsed = new Date();
    await this.serverCookieRepository.save(serverCookie);

    return { cookieData: serverCookie.cookieData };
  }

  async getAllServerCookies(
    accountId: number,
    userId: number,
  ): Promise<ServerCookie[]> {
    const account = await this.accountRepository.findOne({
      where: { id: accountId },
    });

    if (!account) {
      throw new NotFoundException(`Account with ID ${accountId} not found`);
    }

    // Check if the user has access to this account through package_users
    const hasAccess = await this.userHasAccessToAccount(userId, accountId);
    if (!hasAccess) {
      throw new NotFoundException(
        `User does not have access to account with ID ${accountId}`,
      );
    }

    return this.serverCookieRepository.find({
      where: { accountId },
      order: { lastUsed: 'DESC' },
    });
  }

  async deleteServerCookie(
    accountId: number,
    serverId: number,
    userId: number,
  ): Promise<boolean> {
    const account = await this.accountRepository.findOne({
      where: { id: accountId },
    });

    if (!account) {
      throw new NotFoundException(`Account with ID ${accountId} not found`);
    }

    // Check if the user has access to this account through package_users
    const hasAccess = await this.userHasAccessToAccount(userId, accountId);
    if (!hasAccess) {
      throw new NotFoundException(
        `User does not have access to account with ID ${accountId}`,
      );
    }

    const serverCookie = await this.serverCookieRepository.findOne({
      where: {
        accountId,
        serverId,
      },
    });

    if (!serverCookie) {
      return false;
    }

    await this.serverCookieRepository.remove(serverCookie);
    return true;
  }

  async autoLogin(accountId: string): Promise<Session> {
    const account = await this.accountRepository.findOne({
      where: { id: parseInt(accountId) },
    });

    if (!account) {
      throw new Error('Account not found');
    }

    const session = this.sessionRepository.create({
      account_id: account.id,
      user_id: this.request.user.sub,
      status: 'pending',
      user_agent:
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      proxy: 'none',
    });
    await this.sessionRepository.save(session);

    let browser: any;
    try {
      browser = await puppeteer.launch({
        headless: false,
        devtools: false,
        defaultViewport: null,
        args: ['--no-sandbox', '--disable-setuid-sandbox', '--start-maximized'],
      });

      const pages = await browser.pages();
      const page = pages.length ? pages[0] : await browser.newPage();

      // Chặn menu chuột phải và phím tắt mở DevTools
      await page.evaluateOnNewDocument(() => {
        document.addEventListener(
          'contextmenu',
          (e) => {
            e.preventDefault();
            e.stopPropagation();
          },
          true,
        );

        document.addEventListener(
          'keydown',
          (e) => {
            // F12
            if (e.key === 'F12') {
              e.preventDefault();
              e.stopPropagation();
            }
            // Ctrl + Shift + I
            if (e.ctrlKey && e.shiftKey && e.key === 'I') {
              e.preventDefault();
              e.stopPropagation();
            }
            // Ctrl + Shift + J hoặc Ctrl + Shift + C
            if (e.ctrlKey && e.shiftKey && (e.key === 'J' || e.key === 'C')) {
              e.preventDefault();
              e.stopPropagation();
            }
          },
          true,
        );
      });

      // Thiết lập header để giảm thiểu fingerprint
      await page.setExtraHTTPHeaders({
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept-Language': 'en-US,en;q=0.9',
      });

      // Giả mạo dữ liệu canvas
      await page.evaluateOnNewDocument(() => {
        HTMLCanvasElement.prototype.toDataURL = function () {
          return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg==';
        };
      });

      // Giả mạo dữ liệu WebGL
      await page.evaluateOnNewDocument(() => {
        const originalGetParameter =
          WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function (parameter) {
          if (parameter === WebGLRenderingContext.RENDERER) {
            return 'Mali-G78';
          }
          return originalGetParameter.call(this, parameter);
        };
      });

      // Điều hướng đến trang đăng nhập
      await page.goto(account.website_url, { waitUntil: 'networkidle2' });

      // Nhập thông tin đăng nhập
      await page.waitForSelector('input[placeholder="Enter Your Email"]', {
        visible: true,
      });
      await page.type(
        'input[placeholder="Enter Your Email"]',
        account.username,
      );

      await page.waitForSelector('input[placeholder="Enter Your Password"]', {
        visible: true,
      });
      await page.type(
        'input[placeholder="Enter Your Password"]',
        account.password,
      );

      // Loại bỏ icon toggle password nếu có
      await page.evaluate(() => {
        const toggleEye = document.querySelector('.suffix');
        if (toggleEye) toggleEye.remove();

        const passInput = document.querySelector(
          'input[placeholder="Enter Your Password"]',
        );
        if (passInput) {
          passInput.setAttribute('type', 'password');
          passInput.replaceWith(passInput.cloneNode(true));
        }
      });

      await page.waitForSelector(
        'button[type="submit"], input[type="submit"]',
        {
          visible: true,
        },
      );

      // Nhấn nút submit kèm chờ trang load
      await Promise.all([
        page.waitForNavigation({ waitUntil: 'networkidle2' }),
        page.click('button[type="submit"], input[type="submit"]'),
      ]);

      // Kiểm tra đăng nhập thành công
      const isLogined = await page
        .waitForSelector('img[alt="User profile"]', { timeout: 5000 })
        .catch(() => null);

      if (!isLogined) {
        throw new Error('Login failed');
      }

      // Save cookies to the account
      const cookies = await page.cookies();
      const cookieData = JSON.stringify(cookies);

      // Get the server ID from the request headers or use 1 (default)
      const serverId =
        parseInt(this.request.headers['server-id'] as string) || 1;

      // Save cookie to server_cookies table
      let serverCookie = await this.serverCookieRepository.findOne({
        where: {
          accountId: account.id,
          serverId,
        },
      });

      if (!serverCookie) {
        // Create new server cookie
        serverCookie = this.serverCookieRepository.create({
          accountId: account.id,
          serverId,
          cookieData,
          lastUsed: new Date(),
        });
      } else {
        // Update existing server cookie
        serverCookie.cookieData = cookieData;
        serverCookie.lastUsed = new Date();
      }

      await this.serverCookieRepository.save(serverCookie);

      // Update account with cookie data for backward compatibility
      account.cookie_data = cookieData;
      await this.accountRepository.save(account);

      // Also save to Redis for backward compatibility
      await this.redisClient.set('shared_cookie', cookieData, 'EX', 14400); // Store for 4 hours

      session.status = 'success';
      await this.sessionRepository.save(session);
      return session;
    } catch (error) {
      session.status = 'failed';
      await this.sessionRepository.save(session);
      if (browser) await browser.close();
      throw error;
    }
  }

  // Helper method to format cookie string to JSON array format
  private formatCookieString(cookieStr: string): string {
    try {
      // Split the cookie string by tabs or spaces
      const lines = cookieStr.split(/\n/).filter((line) => line.trim());
      const cookies = [];

      for (const line of lines) {
        const parts = line.split(/\t|\s+/).filter((part) => part.trim());
        if (parts.length >= 3) {
          // At minimum, we need name, value, domain
          const cookie = {
            name: parts[0],
            value: parts[1],
            domain: parts[2],
            path: parts.length > 3 ? parts[3] : '/',
            expires:
              parts.length > 4
                ? new Date(parts[4]).getTime() / 1000
                : undefined,
            size: parts[0].length + parts[1].length,
            httpOnly: false,
            secure: true,
            session: false,
            priority: 'Medium',
            sameParty: false,
            sourceScheme: 'Secure',
          };
          cookies.push(cookie);
        }
      }

      return JSON.stringify(cookies);
    } catch (error) {
      console.error('Error formatting cookie string:', error);
      // Return the original string if formatting fails
      return cookieStr;
    }
  }

  // Find all cookies with pagination and filters
  async findAllCookies(
    page: number = 1,
    limit: number = 10,
    search?: string,
    accountId?: number,
    serverId?: number,
  ) {
    const skip = (page - 1) * limit;

    // Create query builder for server cookies
    const queryBuilder = this.serverCookieRepository
      .createQueryBuilder('serverCookie')
      .leftJoinAndSelect('serverCookie.account', 'account')
      .orderBy('serverCookie.lastUsed', 'DESC')
      .skip(skip)
      .take(limit);

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere(
        '(account.website_url LIKE :search OR account.name LIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Apply account filter if provided
    if (accountId) {
      queryBuilder.andWhere('serverCookie.accountId = :accountId', {
        accountId,
      });
    }

    // Apply server filter if provided
    if (serverId) {
      queryBuilder.andWhere('serverCookie.serverId = :serverId', { serverId });
    }

    const [cookies, total] = await queryBuilder.getManyAndCount();

    return {
      data: cookies,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  // Helper method to check if a user has access to an account through package_users, product_users or admin role
  async userHasAccessToAccount(
    userId: number,
    accountId: number,
  ): Promise<boolean> {
    // First, check if the user is an admin
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    // If user is admin, they have access to all accounts
    if (user && user.role === 'admin') {
      return true;
    }

    // Check access through packages
    const hasPackageAccess = await this.checkPackageAccess(userId, accountId);
    if (hasPackageAccess) {
      return true;
    }

    // Check access through products
    const hasProductAccess = await this.checkProductAccess(userId, accountId);
    return hasProductAccess;
  }

  // Helper method to check package access
  private async checkPackageAccess(
    userId: number,
    accountId: number,
  ): Promise<boolean> {
    // Get all packages associated with the account
    const account = await this.accountRepository.findOne({
      where: { id: accountId },
      relations: ['packages'],
    });

    if (!account || !account.packages || account.packages.length === 0) {
      return false;
    }

    // Get all active package_users for the user
    const packageUsers = await this.packageUserRepository.find({
      where: {
        user_id: userId,
        status: 'active',
      },
    });

    if (!packageUsers || packageUsers.length === 0) {
      return false;
    }

    // Check if any of the user's packages match the account's packages
    const userPackageIds = packageUsers.map((pu) => pu.package_id);
    const accountPackageIds = account.packages.map((p) => p.id);

    // Return true if there's at least one package in common
    return userPackageIds.some((id) => accountPackageIds.includes(id));
  }

  // Helper method to check product access
  private async checkProductAccess(
    userId: number,
    accountId: number,
  ): Promise<boolean> {
    // Get all active product_users for the user
    const productUsers = await this.productUserRepository.find({
      where: {
        user_id: userId,
        status: 'active',
      },
    });

    if (!productUsers || productUsers.length === 0) {
      return false;
    }

    // Get user's product IDs
    const userProductIds = productUsers.map((pu) => pu.product_id);

    // Check if any of the user's products are associated with this account
    // through account_products table
    const accountProductQuery = await this.accountRepository.manager.query(
      `SELECT COUNT(*) as count 
       FROM account_products ap 
       WHERE ap.account_id = $1 
       AND ap.product_id = ANY($2)`,
      [accountId, userProductIds],
    );

    const hasProductAccess = parseInt(accountProductQuery[0]?.count || '0') > 0;
    return hasProductAccess;
  }
}
