import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
  OneToMany,
} from 'typeorm';
import { Package } from '../../packages/entities/package.entity';
import { ServerCookie } from './server-cookie.entity';

@Entity('accounts')
export class Account {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  website_url: string;

  @Column({ nullable: true })
  username: string;

  @Column({ nullable: true })
  password: string;

  @Column({ default: 1 })
  login_cookie: number;

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true, type: 'text' })
  img_intro: string;

  @Column({ nullable: true, type: 'text' })
  description: string;

  @Column({ nullable: true, type: 'text' })
  cookie_data: string;

  @Column({ type: 'timestamp', nullable: true })
  last_login: Date;

  @Column({ type: 'integer', default: 0 })
  sort: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToMany(() => Package)
  @JoinTable({
    name: 'account_packages',
    joinColumn: { name: 'account_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'package_id', referencedColumnName: 'id' },
  })
  packages: Package[];

  @OneToMany(() => ServerCookie, (serverCookie) => serverCookie.account)
  serverCookies: ServerCookie[];
}
