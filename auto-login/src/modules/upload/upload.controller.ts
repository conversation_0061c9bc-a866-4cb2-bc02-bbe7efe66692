import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  HttpException,
  HttpStatus,
  Req,
  Get,
  Param,
  Res,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Request, Response } from 'express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import * as fs from 'fs';
import * as path from 'path';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';

interface RequestWithUser extends Request {
  user?: {
    sub: number;
    email: string;
    role: string;
    status: string;
  };
}

@Controller('upload')
export class UploadController {
  @Post('image')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @UseInterceptors(
    FileInterceptor('image', {
      storage: diskStorage({
        destination: (req, file, cb) => {
          const uploadPath = path.join(process.cwd(), 'uploads', 'images');
          if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
          }
          cb(null, uploadPath);
        },
        filename: (req, file, cb) => {
          const uniqueSuffix =
            Date.now() + '-' + Math.round(Math.random() * 1e9);
          cb(null, `image-${uniqueSuffix}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif|webp)$/)) {
          return cb(new Error('Only image files are allowed!'), false);
        }
        cb(null, true);
      },
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
      },
    }),
  )
  async uploadImage(
    @UploadedFile() file: Express.Multer.File,
    @Req() req: RequestWithUser,
  ) {
    try {
      // Verify user authentication
      const userId = req.user?.sub;
      if (!userId) {
        throw new HttpException(
          'User not authenticated',
          HttpStatus.UNAUTHORIZED,
        );
      }

      if (!file) {
        throw new HttpException('No file uploaded', HttpStatus.BAD_REQUEST);
      }

      const baseUrl = process.env.ADMIN_APP_URL || 'http://localhost:3000';
      const imageUrl = `${baseUrl}/uploads/images/${file.filename}`;

      console.log('Image uploaded successfully:', {
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        imageUrl: imageUrl,
        filePath: path.join(process.cwd(), 'uploads', 'images', file.filename),
      });

      return {
        success: true,
        imageUrl,
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
      };
    } catch (error) {
      throw new HttpException(
        'Failed to upload image: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('test-image/:filename')
  async testImage(@Param('filename') filename: string, @Res() res: Response) {
    try {
      const imagePath = path.join(process.cwd(), 'uploads', 'images', filename);
      console.log('Testing image access:', imagePath);

      if (fs.existsSync(imagePath)) {
        return res.sendFile(imagePath);
      } else {
        return res
          .status(404)
          .json({ error: 'Image not found', path: imagePath });
      }
    } catch (error) {
      return res
        .status(500)
        .json({ error: 'Failed to serve image', message: error.message });
    }
  }
}
