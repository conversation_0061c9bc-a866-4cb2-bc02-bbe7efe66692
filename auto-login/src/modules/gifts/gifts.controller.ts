import { <PERSON>, Controller, Post } from '@nestjs/common';
import { GiftsService } from './gifts.service';
import { SendGiftEmailDto } from './dto/send-gift-email.dto';

@Controller('gifts')
export class GiftsController {
  constructor(private readonly giftsService: GiftsService) {}

  @Post('send-email')
  async sendGiftEmail(@Body() sendGiftEmailDto: SendGiftEmailDto) {
    return this.giftsService.sendGiftEmail(sendGiftEmailDto);
  }
}
