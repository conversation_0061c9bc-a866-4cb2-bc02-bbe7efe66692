import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MailerService } from '@nestjs-modules/mailer';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { SendGiftEmailDto } from './dto/send-gift-email.dto';
import { User } from '../users/entities/user.entity';

@Injectable()
export class GiftsService {
  private readonly logger = new Logger(GiftsService.name);

  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
  ) {}

  /**
   * Check if a user exists by email
   */
  private async findUserByEmail(email: string): Promise<User | null> {
    return await this.usersRepository.findOne({
      where: { email },
    });
  }

  /**
   * Create a basic user with email only
   */
  private async createBasicUser(email: string): Promise<User> {
    // Generate a random password for the user
    const randomPassword = Math.random().toString(36).slice(-12);
    const hashedPassword = await bcrypt.hash(randomPassword, 10);

    const user = this.usersRepository.create({
      email,
      password: hashedPassword,
      status: 'inactive',
      role: 'user',
    });

    return await this.usersRepository.save(user);
  }

  /**
   * Ensure user exists - create if not exists
   */
  private async ensureUserExists(email: string): Promise<User> {
    let user = await this.findUserByEmail(email);

    if (!user) {
      this.logger.log(`Creating new user for email: ${email}`);
      user = await this.createBasicUser(email);
    } else {
      this.logger.log(`User already exists for email: ${email}`);
    }

    return user;
  }

  async sendGiftEmail(
    sendGiftEmailDto: SendGiftEmailDto,
  ): Promise<{ success: boolean; message: string }> {
    const { email } = sendGiftEmailDto;

    try {
      // Ensure user exists in database - create if not exists
      await this.ensureUserExists(email);
      // Generate unique message ID
      const messageId = `gift-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@kitsify.com`;

      const subject = '🎁 Your Special Gift from Kitsify';

      const htmlContent = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Your Special Gift from Kitsify</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
              background-color: #f8f9fa;
            }
            .container {
              background: white;
              border-radius: 15px;
              padding: 30px;
              box-shadow: 0 10px 30px rgba(0,0,0,0.1);
              max-width: 800px;
              margin: 0 auto;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              padding: 20px;
              background: linear-gradient(135deg, #ec4899, #8b5cf6);
              border-radius: 10px;
              color: white;
            }
            .gift-icon {
              font-size: 48px;
              margin-bottom: 10px;
            }
            .title {
              font-size: 28px;
              font-weight: bold;
              margin: 0;
            }
            .subtitle {
              font-size: 16px;
              opacity: 0.9;
              margin: 5px 0 0 0;
            }
            .benefits {
              background: #f8f9ff;
              border-radius: 10px;
              padding: 25px;
              margin: 25px 0;
            }
            .benefit-item {
              display: flex;
              align-items: center;
              margin: 15px 0;
              font-size: 16px;
            }
            .benefit-icon {
              font-size: 20px;
              margin-right: 12px;
              width: 30px;
            }
            .cta-section {
              text-align: center;
              margin: 30px 0;
              padding: 25px;
              background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
              border-radius: 10px;
            }
            .cta-button {
              display: inline-block;
              background: linear-gradient(135deg, #ec4899, #8b5cf6);
              color: white;
              text-decoration: none;
              padding: 15px 30px;
              border-radius: 25px;
              font-weight: bold;
              font-size: 18px;
              margin: 10px;
              transition: transform 0.2s;
            }
            .cta-button:hover {
              transform: translateY(-2px);
            }
            .instructions {
              background: #fff3cd;
              border: 1px solid #ffeaa7;
              border-radius: 8px;
              padding: 20px;
              margin: 20px 0;
            }
            .footer {
              text-align: center;
              margin-top: 30px;
              padding-top: 20px;
              border-top: 1px solid #e5e7eb;
              color: #6b7280;
              font-size: 14px;
            }
            .highlight {
              background: linear-gradient(120deg, #fbbf24 0%, #f59e0b 100%);
              color: white;
              padding: 2px 8px;
              border-radius: 4px;
              font-weight: bold;
            }
            .messenger-section {
              text-align: center;
              margin: 30px 0;
              padding: 32px;
              background: linear-gradient(135deg, #2563eb, #4f46e5);
              border-radius: 16px;
              color: white;
            }
            .messenger-icon {
              display: inline-flex;
              align-items: center;
              justify-content: center;
              width: 80px;
              height: 80px;
              background: rgba(255, 255, 255, 0.2);
              border-radius: 50%;
              font-size: 40px;
              margin: 0 auto 16px auto;
            }
            .messenger-button {
              display: inline-flex;
              align-items: center;
              justify-content: center;
              gap: 12px;
              background: white;
              color: #2563eb;
              text-decoration: none;
              padding: 16px 32px;
              border-radius: 50px;
              font-weight: bold;
              font-size: 18px;
              box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
              transition: all 0.3s ease;
            }
            .messenger-button:hover {
              transform: scale(1.05);
              box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
              color: #2563eb;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="gift-icon">🎁</div>
              <h1 class="title">Your Special Gift is Here!</h1>
              <p class="subtitle">Exclusive access to premium tools and resources</p>
            </div>
            
            <h2 style="font-weight: normal;">Hello there!</h2>
            
            <h2 style="font-weight: normal;">Thank you for your interest in Kitsify! We're excited to share this special gift package with you.</h2>
            
            <div class="benefits">
              <h3 style="margin-top: 0; color: #4f46e5; font-size: 20px;">🎉 1000+ High-Converting Canva Templates:</h3>
              
              <div class="benefit-item">
                <span class="benefit-icon">🛒</span>
                <strong>E-commerce:</strong>
                <a href="https://www.canva.com/design/DAEwKBGghZE/VgSuOCZNz2Y8f4R5C6_RkA/view?mode=preview" target="_blank">Templates 1</a> |
                <a href="https://www.canva.com/design/DAEwKb51MUU/PufNClbJYY4dcyUlmeJjUA/view?utm_content=DAEwKb51MUU&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 2</a>
              </div>

              <div class="benefit-item">
                <span class="benefit-icon">🎓</span>
                <strong>School Academy:</strong>
                <a href="https://www.canva.com/design/DAEwJ6XOrhU/noJOHSOG0ooQ-bj-VyyOsg/view?utm_content=DAEwJ6XOrhU&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 1</a> |
                <a href="https://www.canva.com/design/DAEwJ3S65_0/YEWh7xCtNuKEtLwMTfettA/view?utm_content=DAEwJ3S65_0&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 2</a>
              </div>

              <div class="benefit-item">
                <span class="benefit-icon">🏋️</span>
                <strong>Gym:</strong>
                <a href="https://www.canva.com/design/DAEwKGoeFzg/RTq4wA0g3G_kCAPH0tWtmg/view?utm_content=DAEwKGoeFzg&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 1</a> |
                <a href="https://www.canva.com/design/DAEwJ3S65_0/YEWh7xCtNuKEtLwMTfettA/view?utm_content=DAEwJ3S65_0&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 2</a>
              </div>

              <div class="benefit-item">
                <span class="benefit-icon">💆</span>
                <strong>Beauty - Spa:</strong>
                <a href="https://www.canva.com/design/DAEwKHPUeJ8/MmFLmazi8Fecp8I0rRkL1A/view?utm_content=DAEwKHPUeJ8&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 1</a> |
                <a href="https://www.canva.com/design/DAEwKE3NJGo/viQG9IkY_oTpXLHmNmoung/view?utm_content=DAEwKE3NJGo&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 2</a>
              </div>

              <div class="benefit-item">
                <span class="benefit-icon">💇</span>
                <strong>Beauty - Hair:</strong>
                <a href="https://www.canva.com/design/DAEwKZMmQNI/ZzIxYcPkUsOpn-FPNa4ttw/view?utm_content=DAEwKZMmQNI&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 1</a> |
                <a href="https://www.canva.com/design/DAEwKYHKjlo/JMS3tjksyrLBDqydMsfBlA/view?utm_content=DAEwKYHKjlo&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 2</a>
              </div>

              <div class="benefit-item">
                <span class="benefit-icon">🍽️</span>
                <strong>Restaurant:</strong>
                <a href="https://www.canva.com/design/DAEwKvEfBV8/X9YJwpdQMGHW6YVpNsvYJQ/view?utm_content=DAEwKvEfBV8&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 1</a> |
                <a href="https://www.canva.com/design/DAEwKjEqX-g/16i_V0hnCH1iU3pnlLLwUA/view?utm_content=DAEwKjEqX-g&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 2</a>
              </div>

              <div class="benefit-item">
                <span class="benefit-icon">💰</span>
                <strong>Accounting:</strong>
                <a href="https://www.canva.com/design/DAEwJzQg5mE/CObUs9PYT41Qn4S29xbxzg/view?utm_content=DAEwJzQg5mE&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 1</a> |
                <a href="https://www.canva.com/design/DAEwJ2VE7oQ/nqJ83LPjazDly9bQzvRPAg/view?utm_content=DAEwJ2VE7oQ&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 2</a>
              </div>

              <div class="benefit-item">
                <span class="benefit-icon">🦷</span>
                <strong>Dentist:</strong>
                <a href="https://www.canva.com/design/DAEwJ0x1NbM/ZE7PxyNmG_KJBpTa4OJFow/view?utm_content=DAEwJ0x1NbM&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 1</a> |
                <a href="https://www.canva.com/design/DAEwJ9irQPA/hdZJ9F5kfj2rk6DMOKXPMA/view?utm_content=DAEwJ9irQPA&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 2</a>
              </div>

              <div class="benefit-item">
                <span class="benefit-icon">🥗</span>
                <strong>Nutrition:</strong>
                <a href="https://www.canva.com/design/DAEwKQWdkkA/iNXuIOo6iSUUVryPddCpGA/view?utm_content=DAEwKQWdkkA&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 1</a> |
                <a href="https://www.canva.com/design/DAEwKSnyMqk/2i5DPGnTcPxTMmQbAoTJ4g/view?utm_content=DAEwKSnyMqk&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 2</a>
              </div>

              <div class="benefit-item">
                <span class="benefit-icon">👕</span>
                <strong>Clothing Store:</strong>
                <a href="https://www.canva.com/design/DAEwKdpd_9U/wO9WalGGHFwywrWhA5B2mg/view?utm_content=DAEwKdpd_9U&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 1</a> |
                <a href="https://www.canva.com/design/DAEwKRN0NNE/xM81dMpd29Id1O__Jw9fww/view?utm_content=DAEwKRN0NNE&utm_campaign=designshare&utm_medium=link&utm_source=sharebutton&mode=preview" target="_blank">Templates 2</a>
              </div>
            </div>
            
            <div class="messenger-section">
              <div class="messenger-icon">💬</div>
              <h3 style="margin: 0 0 12px 0; font-size: 24px; font-weight: bold;">Contact Fanpage</h3>
              <p style="margin: 0 0 24px 0; font-size: 18px; opacity: 0.9;">
                Message us now for usage instructions and a free trial experience!
              </p>
              
              <a href="https://www.facebook.com/messages/t/110106370397786" 
                 target="_blank" 
                 class="messenger-button">
                <span style="font-size: 20px;">Contact Fanpage</span>
              </a>
            </div>
            
            <p>Welcome to Kitsify! 🌟</p>
            
            <div class="footer">
              <p>This email was sent to ${email}</p>
              <p>© ${new Date().getFullYear()} Kitsify. All rights reserved.</p>
              <p><a href="https://kitsify.com" target="_blank">Visit our website</a></p>
            </div>
          </div>
        </body>
        </html>
      `;

      const mailFrom = this.configService.get('mail.from');

      await this.mailerService.sendMail({
        to: email,
        from: {
          name: 'Kitsify',
          address: mailFrom,
        },
        subject: subject,
        html: htmlContent,
        headers: {
          'Message-ID': messageId,
          'List-Unsubscribe': `<https://kitsify.com/unsubscribe>, <mailto:<EMAIL>>`,
          'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
          'Feedback-ID': `gift-email:kitsify:${process.env.MAIL_FEEDBACK_ID}`,
          Precedence: 'bulk',
          'X-Auto-Response-Suppress': 'OOF, AutoReply',
          'X-Entity-Ref-ID': `kitsify-gift-email-${Date.now()}`,
        },
      });

      this.logger.log(`Gift email sent successfully to ${email}`);

      return {
        success: true,
        message: 'Gift email sent successfully! Please check your inbox.',
      };
    } catch (error) {
      this.logger.error(`Failed to send gift email to ${email}:`, error);

      return {
        success: false,
        message: 'Failed to send gift email. Please try again later.',
      };
    }
  }
}
