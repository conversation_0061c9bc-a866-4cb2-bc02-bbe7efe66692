import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  NotFoundException,
  Request,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Roles } from '../auth/roles.decorator';
import { RolesGuard } from '../auth/roles.guard';
import { ProductUsersService } from './product-users.service';
import { CreateProductUserDto } from './dto/create-product-user.dto';
import { UpdateProductUserDto } from './dto/update-product-user.dto';

@Controller('product-users')
@UseGuards(JwtAuthGuard)
export class ProductUsersController {
  constructor(private readonly productUsersService: ProductUsersService) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles('admin')
  async create(@Body() createProductUserDto: CreateProductUserDto) {
    try {
      return await this.productUsersService.create(createProductUserDto);
    } catch (error) {
      throw new HttpException(
        'Failed to create product user association',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @UseGuards(RolesGuard)
  @Roles('admin')
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 10,
    @Query('userId', new ParseIntPipe({ optional: true })) userId?: number,
    @Query('productId', new ParseIntPipe({ optional: true }))
    productId?: number,
    @Query('status') status?: string,
  ) {
    try {
      return await this.productUsersService.findAll(
        page,
        limit,
        userId,
        productId,
        status,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve product users',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('my')
  async findMyProducts(@Request() req) {
    try {
      const userId = req.user.sub;
      return await this.productUsersService.findUserProducts(userId);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve your products',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.productUsersService.findOne(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to retrieve product user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateProductUserDto: UpdateProductUserDto,
  ) {
    try {
      return await this.productUsersService.update(id, updateProductUserDto);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to update product user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async remove(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.productUsersService.remove(id);
      return { message: 'Product user association deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to delete product user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
