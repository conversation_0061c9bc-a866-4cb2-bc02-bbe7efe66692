import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { QueryProductDto } from './dto/query-product.dto';
import { CreateProductDurationDto } from './dto/create-product-duration.dto';
import { UpdateProductDurationDto } from './dto/update-product-duration.dto';
import { AssignAccountProductDto } from './dto/assign-account-product.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Roles } from '../auth/roles.decorator';
import { RolesGuard } from '../auth/roles.guard';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Category } from './entities/category.entity';
import { ProductCategory } from './entities/product-category.entity';
import { ManageProductDiscountsDto } from './dto/manage-product-discounts.dto';

@Controller('products')
export class ProductsController {
  constructor(
    private readonly productsService: ProductsService,
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    @InjectRepository(ProductCategory)
    private readonly productCategoryRepository: Repository<ProductCategory>,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  create(@Body() createProductDto: CreateProductDto) {
    return this.productsService.create(createProductDto);
  }

  @Get()
  findAll(@Query() queryProductDto: QueryProductDto) {
    return this.productsService.findAll(queryProductDto);
  }

  @Get('categories')
  async getAllCategories() {
    return this.categoryRepository.find({
      order: { name: 'ASC' },
    });
  }

  // Payment Methods endpoints (must be before :id route)
  @Get('payment-methods')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  getAllPaymentMethods() {
    return this.productsService.getAllPaymentMethods();
  }

  // Test endpoint without auth
  @Get('test-routing')
  testRouting() {
    console.log('🟢 test-routing endpoint called!');
    return { message: 'Routing works!', timestamp: new Date() };
  }

  // Duration endpoints (must be before :id route)
  @Get('durations/:id')
  getDuration(@Param('id') id: string) {
    const durationId = parseInt(id, 10);
    if (isNaN(durationId) || durationId <= 0) {
      throw new BadRequestException('Invalid duration ID');
    }
    return this.productsService.findProductDuration(durationId);
  }

  @Patch('durations/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  updateDuration(
    @Param('id') id: string,
    @Body() updateProductDurationDto: UpdateProductDurationDto,
  ) {
    const durationId = parseInt(id, 10);
    if (isNaN(durationId) || durationId <= 0) {
      throw new BadRequestException('Invalid duration ID');
    }
    return this.productsService.updateProductDuration(
      durationId,
      updateProductDurationDto,
    );
  }

  @Delete('durations/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  removeDuration(@Param('id') id: string) {
    const durationId = parseInt(id, 10);
    if (isNaN(durationId) || durationId <= 0) {
      throw new BadRequestException('Invalid duration ID');
    }
    return this.productsService.deleteProductDuration(durationId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    console.log('🔴 :id endpoint called with id:', id);
    const productId = parseInt(id, 10);
    if (isNaN(productId) || productId <= 0) {
      throw new BadRequestException('Invalid product ID');
    }

    const product = await this.productsService.findOne(productId);

    const productCategories = await this.productCategoryRepository.find({
      where: { product_id: productId },
      relations: ['category'],
    });

    product['categories'] = productCategories.map((pc) => pc.category);

    return product;
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async update(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
  ) {
    const productId = parseInt(id, 10);
    if (isNaN(productId) || productId <= 0) {
      throw new BadRequestException('Invalid product ID');
    }

    await this.productsService.update(productId, updateProductDto);

    return this.findOne(id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  remove(@Param('id') id: string) {
    const productId = parseInt(id, 10);
    if (isNaN(productId) || productId <= 0) {
      throw new BadRequestException('Invalid product ID');
    }
    return this.productsService.remove(productId);
  }

  // Product Duration endpoints
  @Post(':id/durations')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  createDuration(
    @Param('id') id: string,
    @Body() createProductDurationDto: CreateProductDurationDto,
  ) {
    const productId = parseInt(id, 10);
    if (isNaN(productId) || productId <= 0) {
      throw new BadRequestException('Invalid product ID');
    }
    // Ensure product_id matches the URL parameter
    createProductDurationDto.product_id = productId;
    return this.productsService.createProductDuration(createProductDurationDto);
  }

  @Get(':id/durations')
  getDurations(@Param('id') id: string) {
    const productId = parseInt(id, 10);
    if (isNaN(productId) || productId <= 0) {
      throw new BadRequestException('Invalid product ID');
    }
    return this.productsService.findProductDurations(productId);
  }

  // Account assignment endpoints
  @Post('assign-accounts')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  assignAccountsToProduct(
    @Body() assignAccountProductDto: AssignAccountProductDto,
  ) {
    return this.productsService.assignAccountsToProduct(
      assignAccountProductDto,
    );
  }

  @Delete(':productId/accounts/:accountId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  removeAccountFromProduct(
    @Param('productId') productId: string,
    @Param('accountId') accountId: string,
  ) {
    const parsedProductId = parseInt(productId, 10);
    const parsedAccountId = parseInt(accountId, 10);
    if (isNaN(parsedProductId) || parsedProductId <= 0) {
      throw new BadRequestException('Invalid product ID');
    }
    if (isNaN(parsedAccountId) || parsedAccountId <= 0) {
      throw new BadRequestException('Invalid account ID');
    }
    return this.productsService.removeAccountFromProduct(
      parsedAccountId,
      parsedProductId,
    );
  }

  @Get(':id/accounts')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  getProductAccounts(@Param('id') id: string) {
    const productId = parseInt(id, 10);
    if (isNaN(productId) || productId <= 0) {
      throw new BadRequestException('Invalid product ID');
    }
    return this.productsService.getProductAccounts(productId);
  }

  // Product Discounts endpoints
  @Post(':id/discounts')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  manageProductDiscounts(
    @Param('id') id: string,
    @Body() manageProductDiscountsDto: ManageProductDiscountsDto,
  ) {
    const productId = parseInt(id, 10);
    if (isNaN(productId) || productId <= 0) {
      throw new BadRequestException('Invalid product ID');
    }
    return this.productsService.manageProductDiscounts(
      productId,
      manageProductDiscountsDto,
    );
  }

  @Get(':id/discounts')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  getProductDiscounts(@Param('id') id: string) {
    const productId = parseInt(id, 10);
    if (isNaN(productId) || productId <= 0) {
      throw new BadRequestException('Invalid product ID');
    }
    return this.productsService.getProductDiscounts(productId);
  }

  @Get(':id/with-discounts')
  getProductWithDiscounts(@Param('id') id: string) {
    const productId = parseInt(id, 10);
    if (isNaN(productId) || productId <= 0) {
      throw new BadRequestException('Invalid product ID');
    }
    return this.productsService.findOneWithDiscounts(productId);
  }
}
