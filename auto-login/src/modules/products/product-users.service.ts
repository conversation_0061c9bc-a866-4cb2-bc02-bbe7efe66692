import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductUser } from './entities/product-user.entity';
import { CreateProductUserDto } from './dto/create-product-user.dto';
import { UpdateProductUserDto } from './dto/update-product-user.dto';

@Injectable()
export class ProductUsersService {
  constructor(
    @InjectRepository(ProductUser)
    private productUserRepository: Repository<ProductUser>,
  ) {}

  async create(
    createProductUserDto: CreateProductUserDto,
  ): Promise<ProductUser> {
    const productUser = this.productUserRepository.create(createProductUserDto);
    return this.productUserRepository.save(productUser);
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    userId?: number,
    productId?: number,
    status?: string,
  ): Promise<{
    data: ProductUser[];
    meta: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }> {
    const skip = (page - 1) * limit;
    const queryBuilder = this.productUserRepository
      .createQueryBuilder('productUser')
      .leftJoinAndSelect('productUser.user', 'user')
      .leftJoinAndSelect('productUser.product', 'product')
      .select([
        'productUser.id',
        'productUser.user_id',
        'productUser.product_id',
        'productUser.start_date',
        'productUser.expires_at',
        'productUser.status',
        'productUser.created_at',
        'productUser.updated_at',
        // User fields - exclude sensitive data
        'user.id',
        'user.email',
        'user.status',
        'user.role',
        'user.created_at',
        'user.updated_at',
        // Product fields - include all product data
        'product',
      ])
      .orderBy('productUser.created_at', 'DESC')
      .skip(skip)
      .take(limit);

    if (userId) {
      queryBuilder.andWhere('productUser.user_id = :userId', { userId });
    }

    if (productId) {
      queryBuilder.andWhere('productUser.product_id = :productId', {
        productId,
      });
    }

    if (status) {
      queryBuilder.andWhere('productUser.status = :status', { status });
    }

    const [productUsers, total] = await queryBuilder.getManyAndCount();

    return {
      data: productUsers,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number): Promise<ProductUser> {
    const productUser = await this.productUserRepository
      .createQueryBuilder('productUser')
      .leftJoinAndSelect('productUser.user', 'user')
      .leftJoinAndSelect('productUser.product', 'product')
      .select([
        'productUser.id',
        'productUser.user_id',
        'productUser.product_id',
        'productUser.start_date',
        'productUser.expires_at',
        'productUser.status',
        'productUser.created_at',
        'productUser.updated_at',
        // User fields - exclude sensitive data
        'user.id',
        'user.email',
        'user.status',
        'user.role',
        'user.created_at',
        'user.updated_at',
        // Product fields - include all product data
        'product',
      ])
      .where('productUser.id = :id', { id })
      .getOne();

    if (!productUser) {
      throw new NotFoundException(`ProductUser with ID ${id} not found`);
    }

    return productUser;
  }

  async findByUserAndProduct(
    userId: number,
    productId: number,
  ): Promise<ProductUser | null> {
    return this.productUserRepository
      .createQueryBuilder('productUser')
      .leftJoinAndSelect('productUser.user', 'user')
      .leftJoinAndSelect('productUser.product', 'product')
      .select([
        'productUser.id',
        'productUser.user_id',
        'productUser.product_id',
        'productUser.start_date',
        'productUser.expires_at',
        'productUser.status',
        'productUser.created_at',
        'productUser.updated_at',
        // User fields - exclude sensitive data
        'user.id',
        'user.email',
        'user.status',
        'user.role',
        'user.created_at',
        'user.updated_at',
        // Product fields - include all product data
        'product',
      ])
      .where('productUser.user_id = :userId', { userId })
      .andWhere('productUser.product_id = :productId', { productId })
      .getOne();
  }

  async findActiveByUser(userId: number): Promise<ProductUser[]> {
    return this.productUserRepository
      .createQueryBuilder('productUser')
      .leftJoinAndSelect('productUser.user', 'user')
      .leftJoinAndSelect('productUser.product', 'product')
      .select([
        'productUser.id',
        'productUser.user_id',
        'productUser.product_id',
        'productUser.start_date',
        'productUser.expires_at',
        'productUser.status',
        'productUser.created_at',
        'productUser.updated_at',
        // User fields - exclude sensitive data
        'user.id',
        'user.email',
        'user.status',
        'user.role',
        'user.created_at',
        'user.updated_at',
        // Product fields - include all product data
        'product',
      ])
      .where('productUser.user_id = :userId', { userId })
      .andWhere('productUser.status = :status', { status: 'active' })
      .getMany();
  }

  async update(
    id: number,
    updateProductUserDto: UpdateProductUserDto,
  ): Promise<ProductUser> {
    const productUser = await this.findOne(id);
    this.productUserRepository.merge(productUser, updateProductUserDto);
    return this.productUserRepository.save(productUser);
  }

  async remove(id: number): Promise<void> {
    const productUser = await this.findOne(id);
    await this.productUserRepository.remove(productUser);
  }

  // Dedicated method for user API to ensure no sensitive data exposure
  async findUserProducts(
    userId: number,
    page: number = 1,
    limit: number = 100,
  ): Promise<{
    data: ProductUser[];
    meta: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }> {
    const skip = (page - 1) * limit;
    const queryBuilder = this.productUserRepository
      .createQueryBuilder('productUser')
      .leftJoinAndSelect('productUser.product', 'product')
      .select([
        'productUser.id',
        'productUser.user_id',
        'productUser.product_id',
        'productUser.start_date',
        'productUser.expires_at',
        'productUser.status',
        'productUser.created_at',
        'productUser.updated_at',
        // Product fields - include all product data
        'product',
      ])
      .where('productUser.user_id = :userId', { userId })
      .orderBy('productUser.created_at', 'DESC')
      .skip(skip)
      .take(limit);

    const [productUsers, total] = await queryBuilder.getManyAndCount();

    return {
      data: productUsers,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }
}
