import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  IsN<PERSON>ber,
  Min,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class QueryProductDto {
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  @IsOptional()
  page?: number = 1;

  @IsNumber()
  @Min(1)
  @Type(() => Number)
  @IsOptional()
  limit?: number = 10;

  @IsString()
  @IsOptional()
  search?: string;

  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    // Handle different formats of the categories parameter
    if (value === undefined || value === null) {
      return [];
    }

    if (typeof value === 'string') {
      // Handle comma-separated values
      if (value.includes(',')) {
        return value.split(',').map((item) => {
          const parsed = Number.parseInt(item.trim(), 10);
          return Number.isNaN(parsed) ? 0 : parsed;
        });
      }
      // Handle single value
      const parsed = Number.parseInt(value, 10);
      return [Number.isNaN(parsed) ? 0 : parsed];
    }

    // If it's already an array, convert to numbers
    if (Array.isArray(value)) {
      return value.map((item) => {
        const parsed = Number.parseInt(String(item), 10);
        return Number.isNaN(parsed) ? 0 : parsed;
      });
    }

    // Default case: convert to number and return as single-item array
    const parsed = Number.parseInt(String(value), 10);
    return [Number.isNaN(parsed) ? 0 : parsed];
  })
  categories?: number[];

  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  inStock?: boolean = false;
}
