import { IsNotEmpty, IsN<PERSON>ber, IsOptional } from 'class-validator';

export class CreateProductDurationDto {
  @IsNotEmpty()
  @IsNumber()
  product_id: number;

  @IsNotEmpty()
  @IsNumber()
  duration_days: number;

  @IsNotEmpty()
  @IsNumber()
  original_price: number;

  @IsOptional()
  @IsNumber()
  discount_price?: number;

  @IsNotEmpty()
  @IsNumber()
  quantity: number;

  @IsOptional()
  @IsNumber()
  discount_percent?: number;
}

export class UpdateProductDurationDto {
  @IsOptional()
  @IsNumber()
  duration_days?: number;

  @IsOptional()
  @IsNumber()
  original_price?: number;

  @IsOptional()
  @IsNumber()
  discount_price?: number;

  @IsOptional()
  @IsNumber()
  quantity?: number;

  @IsOptional()
  @IsNumber()
  discount_percent?: number;
}
