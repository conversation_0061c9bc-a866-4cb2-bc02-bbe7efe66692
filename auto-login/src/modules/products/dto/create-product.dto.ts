import {
  IsString,
  <PERSON>N<PERSON><PERSON>,
  <PERSON>Optional,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateProductDurationDto {
  @IsNumber()
  duration_days: number;

  @IsNumber()
  original_price: number;

  @IsNumber()
  @IsOptional()
  discount_price?: number;

  @IsNumber()
  @IsOptional()
  discount_percent?: number;

  @IsNumber()
  quantity: number;
}

export class CreateProductDto {
  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  category_ids?: number[];

  @IsString()
  @IsOptional()
  image_url?: string;

  @IsArray()
  @IsOptional()
  features?: string[];

  @IsOptional()
  has_prompt_library?: boolean;

  @IsOptional()
  has_prompt_video?: boolean;

  @IsOptional()
  is_best_seller?: boolean;

  @IsNumber()
  @IsOptional()
  sort?: number;

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateProductDurationDto)
  durations?: CreateProductDurationDto[];
}
