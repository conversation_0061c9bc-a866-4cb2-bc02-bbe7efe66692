import {
  IsS<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Is<PERSON><PERSON>ber,
  IsInt,
  IsBoolean,
} from 'class-validator';

export class CreateCategoryDto {
  @IsString()
  @MaxLength(100)
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  color?: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  icon?: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  img_url?: string;

  @IsNumber()
  @IsInt()
  @IsOptional()
  sort?: number;

  @IsBoolean()
  @IsOptional()
  is_hot?: boolean;
}
