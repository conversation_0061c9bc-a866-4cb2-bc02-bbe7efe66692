import {
  IsNotEmpty,
  IsNumber,
  IsDate,
  IsString,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateProductUserDto {
  @IsNotEmpty()
  @IsNumber()
  user_id: number;

  @IsNotEmpty()
  @IsNumber()
  product_id: number;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  start_date?: Date;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  expires_at: Date;

  @IsOptional()
  @IsString()
  status?: string;
}
