import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from './entities/product.entity';
import { Category } from './entities/category.entity';
import { ProductDuration } from './entities/product-duration.entity';
import { AccountProduct } from './entities/account-product.entity';
import { ProductCategory } from './entities/product-category.entity';
import { PaymentMethod } from './entities/payment-method.entity';
import { ProductDiscount } from './entities/product-discount.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { CreateProductDurationDto } from './dto/create-product-duration.dto';
import { UpdateProductDurationDto } from './dto/update-product-duration.dto';
import { AssignAccountProductDto } from './dto/assign-account-product.dto';
import { ManageProductDiscountsDto } from './dto/manage-product-discounts.dto';

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    @InjectRepository(ProductDuration)
    private readonly productDurationRepository: Repository<ProductDuration>,
    @InjectRepository(AccountProduct)
    private readonly accountProductRepository: Repository<AccountProduct>,
    @InjectRepository(ProductCategory)
    private readonly productCategoryRepository: Repository<ProductCategory>,
    @InjectRepository(PaymentMethod)
    private readonly paymentMethodRepository: Repository<PaymentMethod>,
    @InjectRepository(ProductDiscount)
    private readonly productDiscountRepository: Repository<ProductDiscount>,
  ) {}

  async create(createProductDto: CreateProductDto): Promise<Product> {
    const { durations, category_ids, ...productData } = createProductDto;

    // Create a new product instance
    const product = this.productRepository.create(productData);

    // Save the product first
    const savedProduct = await this.productRepository.save(product);
    const productId = (savedProduct as any).id;

    // Handle multiple category assignments if category_ids are provided
    if (category_ids && category_ids.length > 0) {
      const productCategories = category_ids.map((categoryId) => ({
        product_id: productId,
        category_id: categoryId,
      }));

      await this.productCategoryRepository.save(productCategories);
    }

    // Handle durations if provided
    if (durations && durations.length > 0) {
      const productDurations = durations.map((duration) =>
        this.productDurationRepository.create({
          ...duration,
          product_id: productId,
        }),
      );
      await this.productDurationRepository.save(productDurations);
    }

    // Return the product with durations
    return this.findOne(productId);
  }

  async findAll(query: any) {
    const { page = 1, limit = 10, search, categories, inStock } = query;
    const skip = (page - 1) * limit;

    const queryBuilder = this.productRepository
      .createQueryBuilder('product')
      .leftJoinAndSelect('product.durations', 'durations')
      .leftJoinAndSelect('product.productCategories', 'productCategories')
      .leftJoinAndSelect('productCategories.category', 'category')
      .orderBy('product.sort', 'DESC')
      .addOrderBy('product.is_best_seller', 'DESC')
      .addOrderBy('product.created_at', 'DESC');

    // Apply search filter
    if (search) {
      queryBuilder.andWhere(
        '(product.name ILIKE :search OR product.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Apply category filter
    if (categories) {
      let categoryIds = [];

      if (Array.isArray(categories)) {
        categoryIds = categories
          .map((id) => parseInt(id))
          .filter((id) => !isNaN(id));
      } else if (typeof categories === 'string') {
        categoryIds = categories
          .split(',')
          .map((id) => parseInt(id.trim()))
          .filter((id) => !isNaN(id));
      }

      if (categoryIds.length > 0) {
        queryBuilder
          .innerJoin('product_categories', 'pc', 'pc.product_id = product.id')
          .andWhere('pc.category_id IN (:...categoryIds)', {
            categoryIds,
          });
      }
    }

    // Apply in-stock filter
    if (inStock === 'true') {
      queryBuilder.andWhere(
        'EXISTS (SELECT 1 FROM product_durations pd WHERE pd.product_id = product.id AND pd.quantity > 0)',
      );
    }

    // Get total count for pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    // Execute query
    const products = await queryBuilder.getMany();

    // Add is_hot flag to each product based on categories
    const productsWithHotFlag = products.map((product) => {
      const isHot =
        product.productCategories?.some((pc) => pc.category?.is_hot) || false;
      return {
        ...product,
        is_hot: isHot,
      };
    });

    return {
      data: productsWithHotFlag,
      meta: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const product = await this.productRepository.findOne({
      where: { id },
      relations: ['durations'],
    });

    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    return product;
  }

  async update(id: number, updateProductDto: UpdateProductDto) {
    const { durations, category_ids, ...productData } = updateProductDto;
    const product = await this.findOne(id);

    // Update other fields
    Object.assign(product, productData);
    await this.productRepository.save(product);

    // Handle multiple category assignments if category_ids are provided
    if (category_ids && category_ids.length > 0) {
      // Remove existing product-category relationships
      await this.productCategoryRepository.delete({ product_id: id });

      // Create new product-category relationships
      const productCategories = category_ids.map((categoryId) => ({
        product_id: id,
        category_id: categoryId,
      }));

      await this.productCategoryRepository.save(productCategories);
    }

    // Handle durations if provided
    if (durations && Array.isArray(durations)) {
      // Remove existing durations
      await this.productDurationRepository.delete({ product_id: id });

      // Create new durations
      if (durations.length > 0) {
        const productDurations = durations.map((duration) => {
          const durationData: any = { ...duration };
          delete durationData.id; // Remove id from duration data to avoid conflicts
          return {
            ...durationData,
            product_id: id,
          };
        });
        await this.productDurationRepository.save(productDurations);
      }
    }

    // Return updated product with durations
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const product = await this.findOne(id);
    await this.productRepository.remove(product);
  }

  // Product Duration methods
  async createProductDuration(
    createProductDurationDto: CreateProductDurationDto,
  ): Promise<ProductDuration> {
    // Verify product exists
    await this.findOne(createProductDurationDto.product_id);

    const productDuration = this.productDurationRepository.create(
      createProductDurationDto,
    );
    return await this.productDurationRepository.save(productDuration);
  }

  async findProductDurations(productId: number): Promise<ProductDuration[]> {
    return await this.productDurationRepository.find({
      where: { product_id: productId },
      order: { duration_days: 'ASC' },
    });
  }

  async findProductDuration(id: number): Promise<ProductDuration> {
    const productDuration = await this.productDurationRepository.findOne({
      where: { id },
      relations: ['product'],
    });
    if (!productDuration) {
      throw new NotFoundException(`Product duration with ID ${id} not found`);
    }
    return productDuration;
  }

  async updateProductDuration(
    id: number,
    updateProductDurationDto: UpdateProductDurationDto,
  ): Promise<ProductDuration> {
    const productDuration = await this.findProductDuration(id);
    Object.assign(productDuration, updateProductDurationDto);
    return await this.productDurationRepository.save(productDuration);
  }

  async deleteProductDuration(id: number): Promise<void> {
    const productDuration = await this.findProductDuration(id);
    await this.productDurationRepository.remove(productDuration);
  }

  // Account Product assignment methods
  async assignAccountsToProduct(
    assignAccountProductDto: AssignAccountProductDto,
  ): Promise<AccountProduct[]> {
    const { product_id, account_ids } = assignAccountProductDto;

    // Verify product exists
    await this.findOne(product_id);

    const accountProducts = account_ids.map((account_id) =>
      this.accountProductRepository.create({ account_id, product_id }),
    );

    return await this.accountProductRepository.save(accountProducts);
  }

  async removeAccountFromProduct(
    accountId: number,
    productId: number,
  ): Promise<void> {
    const accountProduct = await this.accountProductRepository.findOne({
      where: { account_id: accountId, product_id: productId },
    });

    if (accountProduct) {
      await this.accountProductRepository.remove(accountProduct);
    }
  }

  async getProductAccounts(productId: number): Promise<AccountProduct[]> {
    return await this.accountProductRepository.find({
      where: { product_id: productId },
      relations: ['account'],
    });
  }

  async getAccountProducts(accountId: number): Promise<AccountProduct[]> {
    return await this.accountProductRepository.find({
      where: { account_id: accountId },
      relations: ['product'],
    });
  }

  // Payment Methods
  async getAllPaymentMethods(): Promise<PaymentMethod[]> {
    try {
      return this.paymentMethodRepository.find({
        order: { id: 'ASC' },
      });
    } catch (error) {
      console.error('Error getting all payment methods:', error);
      throw new InternalServerErrorException('Failed to get payment methods');
    }
  }

  // Product Discounts
  async manageProductDiscounts(
    productId: number,
    manageProductDiscountsDto: ManageProductDiscountsDto,
  ): Promise<ProductDiscount[]> {
    // Remove existing discounts for this product
    await this.productDiscountRepository.delete({ product_id: productId });

    // Create new discounts
    const discountsToSave = manageProductDiscountsDto.discounts
      .filter((discount) => discount.discount_percent > 0)
      .map((discount) => ({
        product_id: productId,
        payment_method_id: discount.payment_method_id,
        discount_percent: discount.discount_percent,
      }));

    if (discountsToSave.length > 0) {
      return this.productDiscountRepository.save(discountsToSave);
    }

    return [];
  }

  async getProductDiscounts(productId: number): Promise<ProductDiscount[]> {
    return this.productDiscountRepository.find({
      where: { product_id: productId },
      relations: ['paymentMethod'],
    });
  }

  async findOneWithDiscounts(id: number): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { id },
      relations: [
        'durations',
        'productDiscounts',
        'productDiscounts.paymentMethod',
      ],
    });

    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    return product;
  }
}
