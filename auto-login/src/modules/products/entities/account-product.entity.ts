import {
  <PERSON><PERSON>ty,
  PrimaryColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Product } from './product.entity';
import { Account } from '../../accounts/entities/account.entity';

@Entity('account_products')
export class AccountProduct {
  @PrimaryColumn()
  account_id: number;

  @PrimaryColumn()
  product_id: number;

  @CreateDateColumn({ name: 'assigned_at' })
  assigned_at: Date;

  @ManyToOne(() => Account, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'account_id' })
  account: Account;

  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'product_id' })
  product: Product;
}
