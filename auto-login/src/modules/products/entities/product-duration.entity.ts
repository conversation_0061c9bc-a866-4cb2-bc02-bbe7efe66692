import {
  Column,
  <PERSON>tity,
  <PERSON>in<PERSON>ol<PERSON>n,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Product } from './product.entity';

@Entity('product_durations')
export class ProductDuration {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  product_id: number;

  @Column()
  duration_days: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  original_price: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  discount_price: number;

  @Column({ default: 0 })
  quantity: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  discount_percent: number;

  @ManyToOne(() => Product, (product) => product.durations)
  @JoinColumn({ name: 'product_id' })
  product: Product;
}
