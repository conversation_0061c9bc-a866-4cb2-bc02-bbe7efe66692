import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Unique,
} from 'typeorm';
import { Product } from './product.entity';
import { PaymentMethod } from './payment-method.entity';

@Entity('product_discounts')
@Unique(['product_id', 'payment_method_id'])
export class ProductDiscount {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'product_id' })
  product_id: number;

  @Column({ name: 'payment_method_id' })
  payment_method_id: number;

  @Column('decimal', { precision: 5, scale: 2, default: 0, name: 'discount_percent' })
  discount_percent: number;

  @ManyToOne(() => Product, (product) => product.productDiscounts)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @ManyToOne(() => PaymentMethod, (paymentMethod) => paymentMethod.productDiscounts)
  @JoinColumn({ name: 'payment_method_id' })
  paymentMethod: PaymentMethod;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;
} 