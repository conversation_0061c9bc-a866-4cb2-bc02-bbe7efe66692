import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { ProductDuration } from './product-duration.entity';
import { ProductCategory } from './product-category.entity';
import { ProductDiscount } from './product-discount.entity';

@Entity('products')
export class Product {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @OneToMany(
    () => ProductDuration,
    (productDuration) => productDuration.product,
  )
  durations: ProductDuration[];

  @OneToMany(
    () => ProductCategory,
    (productCategory) => productCategory.product,
  )
  productCategories: ProductCategory[];

  @OneToMany(
    () => ProductDiscount,
    (productDiscount) => productDiscount.product,
  )
  productDiscounts: ProductDiscount[];

  @Column({ name: 'image_url', nullable: true })
  image_url: string;

  @Column('jsonb', { default: [] })
  features: string[];

  @Column({ name: 'has_prompt_library', default: false })
  has_prompt_library: boolean;

  @Column({ name: 'has_prompt_video', default: false })
  has_prompt_video: boolean;

  @Column({ name: 'is_best_seller', default: false })
  is_best_seller: boolean;

  @Column({ name: 'sort', default: 0 })
  sort: number;

  // Các trường giá và số lượng đã được chuyển sang bảng product_durations

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;
}
