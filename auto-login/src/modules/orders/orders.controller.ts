import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Request,
  Param,
  HttpCode,
  HttpStatus,
  Query,
  Patch,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';

@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post('checkout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  async checkout(@Body() createOrderDto: CreateOrderDto, @Request() req) {
    const userId = req.user.sub;
    return this.ordersService.createOrder(userId, createOrderDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async findUserOrders(@Request() req) {
    const userId = req.user.sub;
    return this.ordersService.findUserOrders(userId);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async findOne(@Param('id') id: string, @Request() req) {
    const userId = req.user.sub;
    return this.ordersService.findOne(+id, userId);
  }

  @Post('capture/:orderId')
  @UseGuards(JwtAuthGuard)
  async capturePayment(@Param('orderId') orderId: string, @Request() req) {
    const userId = req.user.sub;
    return this.ordersService.capturePayment(+orderId, userId);
  }

  // Admin-only endpoints
  @Get('admin/all')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async findAllOrders(@Query() query: any) {
    return this.ordersService.findAllOrders(query);
  }

  @Get('admin/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async findOrderById(@Param('id') id: string) {
    return this.ordersService.findOrderById(+id);
  }

  @Patch(':id/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async updateOrderStatus(
    @Param('id') id: string,
    @Body() updateData: { status: string },
  ) {
    return this.ordersService.updateOrderStatus(+id, updateData);
  }
}
