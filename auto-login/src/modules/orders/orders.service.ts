import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Order } from './entities/order.entity';
import { OrderItem } from './entities/order-item.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { ProductsService } from '../products/products.service';
import { ProductUsersService } from '../products/product-users.service';
import * as paypal from '@paypal/checkout-server-sdk';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class OrdersService {
  private paypalClient: paypal.core.PayPalHttpClient;

  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(OrderItem)
    private readonly orderItemRepository: Repository<OrderItem>,
    private readonly productsService: ProductsService,
    private readonly productUsersService: ProductUsersService,
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,
  ) {
    // Initialize PayPal client
    const environment =
      this.configService.get('NODE_ENV') === 'production'
        ? new paypal.core.LiveEnvironment(
            this.configService.get('PAYPAL_CLIENT_ID'),
            this.configService.get('PAYPAL_CLIENT_SECRET'),
          )
        : new paypal.core.SandboxEnvironment(
            this.configService.get('PAYPAL_CLIENT_ID'),
            this.configService.get('PAYPAL_CLIENT_SECRET'),
          );
    this.paypalClient = new paypal.core.PayPalHttpClient(environment);
  }

  async createOrder(userId: number, createOrderDto: CreateOrderDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Validate order items and calculate total
      let totalAmount = 0;
      const orderItems = [];

      for (const item of createOrderDto.items) {
        const product = await this.productsService.findOne(item.product_id);

        // Kiểm tra xem sản phẩm có duration không
        if (!product.durations || product.durations.length === 0) {
          throw new BadRequestException(
            `Product ${product.name} has no available durations`,
          );
        }

        // Lấy duration mặc định (đầu tiên) hoặc duration được chỉ định
        const productDuration = item.duration_id
          ? product.durations.find((d) => d.id === item.duration_id)
          : product.durations[0];

        if (!productDuration) {
          throw new BadRequestException(
            `Invalid duration for product: ${product.name}`,
          );
        }

        // Check if product is in stock
        if (productDuration.quantity < item.quantity) {
          throw new BadRequestException(
            `Not enough stock for product: ${product.name}`,
          );
        }

        // Calculate price (use discount_price if available, otherwise use original_price)
        const price =
          productDuration.discount_price > 0
            ? productDuration.discount_price
            : productDuration.original_price;

        // Calculate item total
        const itemTotal = price * item.quantity;
        totalAmount += itemTotal;

        // Prepare order item
        orderItems.push({
          productId: product.id,
          productDurationId: productDuration.id,
          quantity: item.quantity,
          price,
        });

        // Update product duration quantity
        productDuration.quantity -= item.quantity;
        await queryRunner.manager.save(productDuration);
      }

      // Create order
      const order = new Order();
      order.userId = userId;
      order.status = 'PENDING';
      order.totalAmount = totalAmount;

      // Save order
      const savedOrder = await queryRunner.manager.save(order);

      // Create and save order items
      for (const item of orderItems) {
        const orderItem = new OrderItem();
        orderItem.orderId = savedOrder.id;
        orderItem.productId = item.productId;
        orderItem.quantity = item.quantity;
        orderItem.price = item.price;
        await queryRunner.manager.save(orderItem);
      }

      // Create PayPal order
      const paypalOrderResponse = await this.createPayPalOrder(
        savedOrder.id,
        totalAmount,
      );

      // Update order with PayPal order ID
      savedOrder.paypalOrderId = paypalOrderResponse.id;
      await queryRunner.manager.save(savedOrder);

      // Commit transaction
      await queryRunner.commitTransaction();

      return {
        orderId: savedOrder.id,
        paypalOrderId: paypalOrderResponse.id,
        approvalLink: this.getApprovalLink(paypalOrderResponse),
        totalAmount,
      };
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }

  async findUserOrders(userId: number) {
    const orders = await this.orderRepository.find({
      where: { userId },
      relations: ['items', 'items.product'],
      order: { createdAt: 'DESC' },
    });

    return orders;
  }

  async findOne(id: number, userId: number) {
    const order = await this.orderRepository.findOne({
      where: { id, userId },
      relations: ['items', 'items.product'],
    });

    if (!order) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }

    return order;
  }

  async capturePayment(orderId: number, userId: number) {
    const order = await this.findOne(orderId, userId);

    if (order.status !== 'PENDING') {
      throw new BadRequestException('Order is not in pending state');
    }

    if (!order.paypalOrderId) {
      throw new BadRequestException('Order does not have a PayPal order ID');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create capture request
      const request = new paypal.orders.OrdersCaptureRequest(
        order.paypalOrderId,
      );
      request.requestBody({});

      // Call PayPal to capture the payment
      const response = await this.paypalClient.execute(request);

      // Update order status
      if (response.result.status === 'COMPLETED') {
        order.status = 'COMPLETED';
        await queryRunner.manager.save(order);

        // Get order items with product durations
        const orderItems = await queryRunner.manager.find(OrderItem, {
          where: { orderId: order.id },
          relations: ['product', 'product.durations'],
        });

        // Create product_users entries for each product in the order
        for (const orderItem of orderItems) {
          const product = orderItem.product;

          // Find the product duration used in this order
          const productDuration = product.durations?.[0]; // You might need to store duration ID in OrderItem

          if (productDuration) {
            // Calculate expiry date based on duration days
            const expiryDate = new Date();
            expiryDate.setDate(
              expiryDate.getDate() + productDuration.duration_days,
            );

            // Create product user entry
            await this.productUsersService.create({
              user_id: userId,
              product_id: product.id,
              expires_at: expiryDate,
              status: 'active',
            });
          }
        }

        await queryRunner.commitTransaction();
      } else {
        await queryRunner.rollbackTransaction();
        throw new BadRequestException('Payment capture failed');
      }

      return {
        status: 'success',
        orderId: order.id,
        paypalOrderId: order.paypalOrderId,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(`Payment capture failed: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  private async createPayPalOrder(orderId: number, amount: number) {
    // Create PayPal order
    const request = new paypal.orders.OrdersCreateRequest();
    request.prefer('return=representation');

    // Format amount to 2 decimal places
    const formattedAmount = amount.toFixed(2);

    request.requestBody({
      intent: 'CAPTURE',
      purchase_units: [
        {
          reference_id: orderId.toString(),
          amount: {
            currency_code: 'USD',
            value: formattedAmount,
          },
        },
      ],
      application_context: {
        return_url: `${this.configService.get('APP_URL')}/payment/success`,
        cancel_url: `${this.configService.get('APP_URL')}/payment/cancel`,
      },
    });

    try {
      const response = await this.paypalClient.execute(request);
      return response.result;
    } catch (error) {
      throw new BadRequestException(
        `Failed to create PayPal order: ${error.message}`,
      );
    }
  }

  private getApprovalLink(paypalOrder: any): string {
    // Find the approval link in the HATEOAS links
    const approvalLink = paypalOrder.links.find(
      (link) => link.rel === 'approve',
    );
    return approvalLink ? approvalLink.href : '';
  }

  // Admin methods
  async findAllOrders(query: any = {}) {
    const page = parseInt(query.page) || 1;
    const limit = parseInt(query.limit) || 10;
    const skip = (page - 1) * limit;

    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.items', 'items')
      .leftJoinAndSelect('items.product', 'product')
      .orderBy('order.createdAt', 'DESC');

    // Add search filter
    if (query.search) {
      queryBuilder.andWhere(
        '(order.id::text ILIKE :search OR order.customerEmail ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // Add status filter
    if (query.status && query.status !== 'all') {
      queryBuilder.andWhere('order.status = :status', { status: query.status });
    }

    // Add date range filter
    if (query.dateRange && query.dateRange !== 'all') {
      const now = new Date();
      let startDate: Date;

      switch (query.dateRange) {
        case 'today':
          startDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate(),
          );
          break;
        case 'yesterday':
          startDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate() - 1,
          );
          queryBuilder.andWhere(
            'order.createdAt >= :startDate AND order.createdAt < :endDate',
            {
              startDate,
              endDate: new Date(
                now.getFullYear(),
                now.getMonth(),
                now.getDate(),
              ),
            },
          );
          break;
        case 'last7days':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          queryBuilder.andWhere('order.createdAt >= :startDate', { startDate });
          break;
        case 'last30days':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          queryBuilder.andWhere('order.createdAt >= :startDate', { startDate });
          break;
        case 'thisMonth':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          queryBuilder.andWhere('order.createdAt >= :startDate', { startDate });
          break;
        case 'lastMonth':
          const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
          const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          queryBuilder.andWhere(
            'order.createdAt >= :startDate AND order.createdAt < :endDate',
            {
              startDate: lastMonth,
              endDate: thisMonth,
            },
          );
          break;
      }

      if (query.dateRange !== 'yesterday' && query.dateRange !== 'lastMonth') {
        queryBuilder.andWhere('order.createdAt >= :startDate', { startDate });
      }
    }

    const [orders, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    // Normalize status values for frontend
    const normalizedOrders = orders.map((order) => {
      let normalizedStatus = order.status.toLowerCase();

      // Map backend status to frontend expected status
      switch (normalizedStatus) {
        case 'pending':
          normalizedStatus = 'pending';
          break;
        case 'completed':
          normalizedStatus = 'delivered';
          break;
        case 'cancelled':
          normalizedStatus = 'cancelled';
          break;
        default:
          normalizedStatus = 'pending';
      }

      return {
        ...order,
        status: normalizedStatus,
        // Ensure payment status is normalized
        paymentStatus: order.paymentStatus || 'unpaid',
      };
    });

    return {
      data: normalizedOrders,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOrderById(id: number) {
    const order = await this.orderRepository.findOne({
      where: { id },
      relations: ['items', 'items.product'],
    });

    if (!order) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }

    return order;
  }

  async updateOrderStatus(id: number, updateData: { status: string }) {
    const order = await this.findOrderById(id);

    if (updateData.status) {
      order.status = updateData.status;
    }

    return this.orderRepository.save(order);
  }
}
