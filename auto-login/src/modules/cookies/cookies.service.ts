import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not, IsNull } from 'typeorm';
import { CreateCookieDto } from './dto/create-cookie.dto';
import { UpdateAllowedMembersDto } from './dto/update-allowed-members.dto';
import { User } from '../users/entities/user.entity';
import { Account } from '../accounts/entities/account.entity';
import { ServerCookie } from '../accounts/entities/server-cookie.entity';
import { AccountsService } from '../accounts/accounts.service';

@Injectable()
export class CookiesService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Account)
    private accountRepository: Repository<Account>,
    @InjectRepository(ServerCookie)
    private serverCookieRepository: Repository<ServerCookie>,
    private accountsService: AccountsService,
  ) {}

  async saveCookie(
    createCookieDto: CreateCookieDto,
    userId: number,
  ): Promise<Account> {
    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Normalize domain from website_url
    const normalizedDomain = this.normalizeDomain(createCookieDto.website_url);
    if (!normalizedDomain) {
      throw new BadRequestException('Invalid website URL format');
    }

    // Parse and validate cookie data
    let cookieData: string;

    try {
      // Handle different input types and normalize to string
      if (typeof createCookieDto.cookie_data === 'string') {
        // Parse to validate JSON format and filter
        const parsedCookies = JSON.parse(createCookieDto.cookie_data);
        const cookieArray = Array.isArray(parsedCookies)
          ? parsedCookies
          : [parsedCookies];
        const filteredCookies = this.filterRelevantCookies(
          cookieArray,
          normalizedDomain,
        );
        cookieData = JSON.stringify(filteredCookies);
      } else {
        // Handle case when input is already an object/array
        const cookieArray = Array.isArray(createCookieDto.cookie_data)
          ? createCookieDto.cookie_data
          : [createCookieDto.cookie_data];
        const filteredCookies = this.filterRelevantCookies(
          cookieArray,
          normalizedDomain,
        );
        cookieData = JSON.stringify(filteredCookies);
      }
    } catch (error) {
      throw new BadRequestException(
        `Invalid cookie data format: ${error.message}`,
      );
    }

    // Find account by normalized domain with exact or domain-ending match
    const accounts = await this.accountRepository
      .createQueryBuilder('account')
      .where(
        'LOWER(account.website_url) = :exactDomain OR ' +
          'LOWER(account.website_url) LIKE :domainEnding OR ' +
          'LOWER(account.website_url) LIKE :wwwDomainEnding OR ' +
          'LOWER(account.website_url) LIKE :httpsDomain OR ' +
          'LOWER(account.website_url) LIKE :httpswwwDomain',
      )
      .setParameters({
        exactDomain: normalizedDomain,
        domainEnding: `%.${normalizedDomain}`,
        wwwDomainEnding: `%www.${normalizedDomain}`,
        httpsDomain: `https://${normalizedDomain}%`,
        httpswwwDomain: `https://www.${normalizedDomain}%`,
      })
      .orderBy(
        'CASE ' +
          'WHEN LOWER(account.website_url) = :exactDomain THEN 1 ' +
          'WHEN LOWER(account.website_url) = :wwwDomain THEN 2 ' +
          'WHEN LOWER(account.website_url) = :httpsDomain THEN 3 ' +
          'WHEN LOWER(account.website_url) = :httpswwwDomain THEN 4 ' +
          'ELSE 5 END',
        'ASC',
      )
      .setParameters({
        exactDomain: normalizedDomain,
        wwwDomain: `www.${normalizedDomain}`,
        httpsDomain: `https://${normalizedDomain}`,
        httpswwwDomain: `https://www.${normalizedDomain}`,
      })
      .getMany();

    if (!accounts || accounts.length === 0) {
      throw new NotFoundException(
        `No account found for domain: ${normalizedDomain}`,
      );
    }

    // Get the server ID from the request or use 1 (default)
    const serverId = createCookieDto.server_id || 1;

    // Update account with cookie data (always passing a string)
    return this.accountsService.saveCookie(
      accounts[0].id,
      cookieData, // Now this is guaranteed to be a string
      userId,
      serverId,
    );
  }

  /**
   * Normalize a URL to extract the domain
   * @param url URL or domain string
   * @returns normalized domain (e.g., 'example.com')
   */
  private normalizeDomain(url: string): string {
    try {
      // Remove whitespace
      url = url.trim().toLowerCase();

      // Check if it's already just a domain
      if (!url.includes('/') && !url.includes(':')) {
        // Remove www. prefix if present
        return url.replace(/^www\./, '');
      }

      // Add protocol if missing
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
      }

      // Parse URL
      const parsedUrl = new URL(url);

      // Extract hostname and remove www. prefix
      const domain = parsedUrl.hostname.replace(/^www\./, '');

      return domain;
    } catch (error) {
      return null;
    }
  }

  /**
   * Filter cookies to only include those relevant to the target domain
   * @param cookies Array of cookie objects
   * @param targetDomain The normalized domain
   * @returns Filtered cookies array
   */
  private filterRelevantCookies(cookies: any[], targetDomain: string): any[] {
    return cookies.filter((cookie) => {
      if (!cookie || !cookie.domain) return false;

      // Normalize cookie domain
      let cookieDomain = cookie.domain.trim().toLowerCase();
      if (cookieDomain.startsWith('.')) {
        cookieDomain = cookieDomain.substring(1);
      }

      // Check if cookie domain matches target or is a parent domain
      return (
        targetDomain === cookieDomain ||
        targetDomain.endsWith('.' + cookieDomain) ||
        cookieDomain.endsWith('.' + targetDomain)
      );
    });
  }

  async getAllCookies(): Promise<Account[]> {
    // Only return accounts that have cookie data
    return this.accountRepository.find({
      where: { cookie_data: Not(IsNull()) },
      relations: ['packages'],
    });
  }

  async checkCookieValidity(
    websiteUrl: string,
  ): Promise<{ valid: boolean; lastUpdated?: Date }> {
    // Find account by website URL with flexible matching
    const domainToFind = websiteUrl.toLowerCase().trim();

    // Use query builder for more flexible search
    const account = await this.accountRepository
      .createQueryBuilder('account')
      .where(
        'LOWER(account.website_url) LIKE :url1 OR LOWER(account.website_url) LIKE :url2 OR LOWER(account.website_url) LIKE :url3 OR LOWER(account.website_url) LIKE :url4 OR LOWER(account.website_url) LIKE :url5 OR LOWER(account.website_url) = :url6',
        {
          url1: `%${domainToFind}%`, // Match anywhere in the URL
          url2: `%${domainToFind}`, // Match at the end of the URL
          url3: `https://${domainToFind}%`, // Match with https:// prefix
          url4: `https://www.${domainToFind}%`, // Match with https://www. prefix
          url5: `www.${domainToFind}%`, // Match with www. prefix
          url6: domainToFind, // Exact match but case insensitive
        },
      )
      .getOne();

    if (!account || !account.cookie_data) {
      return { valid: false };
    }

    // Check if cookie is still valid (e.g., not too old)
    const now = new Date();
    const lastUpdated = account.updated_at;
    const daysSinceUpdate =
      (now.getTime() - lastUpdated.getTime()) / (1000 * 3600 * 24);

    // Cookies older than 30 days might be invalid
    const valid = daysSinceUpdate < 30;

    return { valid, lastUpdated };
  }

  async deleteCookie(websiteUrl: string): Promise<boolean> {
    // Find account by website URL with flexible matching
    const domainToFind = websiteUrl.toLowerCase().trim();

    // Use query builder for more flexible search
    const account = await this.accountRepository
      .createQueryBuilder('account')
      .where(
        'LOWER(account.website_url) LIKE :url1 OR LOWER(account.website_url) LIKE :url2 OR LOWER(account.website_url) LIKE :url3 OR LOWER(account.website_url) LIKE :url4 OR LOWER(account.website_url) LIKE :url5 OR LOWER(account.website_url) = :url6',
        {
          url1: `%${domainToFind}%`, // Match anywhere in the URL
          url2: `%${domainToFind}`, // Match at the end of the URL
          url3: `https://${domainToFind}%`, // Match with https:// prefix
          url4: `https://www.${domainToFind}%`, // Match with https://www. prefix
          url5: `www.${domainToFind}%`, // Match with www. prefix
          url6: domainToFind, // Exact match but case insensitive
        },
      )
      .getOne();

    if (!account) {
      return false;
    }

    // Clear the cookie data but don't delete the account
    account.cookie_data = null;
    await this.accountRepository.save(account);
    return true;
  }

  async updateAllowedMembers(
    updateDto: UpdateAllowedMembersDto,
  ): Promise<Account> {
    // This method is no longer needed as we don't have allowed_members in the new schema
    // But we'll keep it for backward compatibility

    // Find account by website URL with flexible matching
    const domainToFind = updateDto.website_url.toLowerCase().trim();

    // Use query builder for more flexible search
    const account = await this.accountRepository
      .createQueryBuilder('account')
      .leftJoinAndSelect('account.packages', 'packages')
      .where(
        'LOWER(account.website_url) LIKE :url1 OR LOWER(account.website_url) LIKE :url2 OR LOWER(account.website_url) LIKE :url3 OR LOWER(account.website_url) LIKE :url4 OR LOWER(account.website_url) LIKE :url5 OR LOWER(account.website_url) = :url6',
        {
          url1: `%${domainToFind}%`, // Match anywhere in the URL
          url2: `%${domainToFind}`, // Match at the end of the URL
          url3: `https://${domainToFind}%`, // Match with https:// prefix
          url4: `https://www.${domainToFind}%`, // Match with https://www. prefix
          url5: `www.${domainToFind}%`, // Match with www. prefix
          url6: domainToFind, // Exact match but case insensitive
        },
      )
      .getOne();

    if (!account) {
      throw new NotFoundException(
        `No account found for ${updateDto.website_url}`,
      );
    }

    // We can't update allowed members directly anymore
    // This would require updating package_users which is beyond the scope of this method
    return account;
  }

  async getAccessibleSites(userId: number): Promise<string[]> {
    // Get all accounts that the user has access to through package_users
    const accounts = await this.accountRepository
      .createQueryBuilder('account')
      .innerJoin('account.packages', 'package')
      .innerJoin('package.packageUsers', 'packageUser')
      .where('packageUser.user_id = :userId', { userId })
      .andWhere('packageUser.status = :status', { status: 'active' })
      .andWhere('account.cookie_data IS NOT NULL')
      .getMany();

    return accounts.map((account) => account.website_url);
  }

  async checkCookieExists(
    websiteUrl: string,
    userId: number,
  ): Promise<boolean> {
    // Find account by website URL with flexible matching
    const domainToFind = websiteUrl.toLowerCase().trim();

    // Use query builder for more flexible search
    const account = await this.accountRepository
      .createQueryBuilder('account')
      .where(
        'LOWER(account.website_url) LIKE :url1 OR LOWER(account.website_url) LIKE :url2 OR LOWER(account.website_url) LIKE :url3 OR LOWER(account.website_url) LIKE :url4 OR LOWER(account.website_url) LIKE :url5 OR LOWER(account.website_url) = :url6',
        {
          url1: `%${domainToFind}%`, // Match anywhere in the URL
          url2: `%${domainToFind}`, // Match at the end of the URL
          url3: `https://${domainToFind}%`, // Match with https:// prefix
          url4: `https://www.${domainToFind}%`, // Match with https://www. prefix
          url5: `www.${domainToFind}%`, // Match with www. prefix
          url6: domainToFind, // Exact match but case insensitive
        },
      )
      .getOne();

    if (!account || !account.cookie_data) {
      return false;
    }
    // Check if the user has access to this account
    return this.accountsService.userHasAccessToAccount(userId, account.id);
  }

  async getCookieForWebsite(
    websiteUrl: string,
    userId: number,
    serverId: number = 1,
  ): Promise<{ cookieData: string } | null> {
    // Find account by website URL with flexible matching
    const domainToFind = websiteUrl.toLowerCase().trim();

    // Use query builder for more flexible search
    const account = await this.accountRepository
      .createQueryBuilder('account')
      .where(
        'LOWER(account.website_url) LIKE :url1 OR LOWER(account.website_url) LIKE :url2 OR LOWER(account.website_url) LIKE :url3 OR LOWER(account.website_url) LIKE :url4 OR LOWER(account.website_url) LIKE :url5 OR LOWER(account.website_url) = :url6',
        {
          url1: `%${domainToFind}%`, // Match anywhere in the URL
          url2: `%${domainToFind}`, // Match at the end of the URL
          url3: `https://${domainToFind}%`, // Match with https:// prefix
          url4: `https://www.${domainToFind}%`, // Match with https://www. prefix
          url5: `www.${domainToFind}%`, // Match with www. prefix
          url6: domainToFind, // Exact match but case insensitive
        },
      )
      .getOne();

    if (!account) {
      return null;
    }

    // Check if the user has access to this account
    const hasAccess = await this.accountsService.userHasAccessToAccount(
      userId,
      account.id,
    );
    if (!hasAccess) {
      return null;
    }

    // Try to get server-specific cookie first
    const serverCookie = await this.serverCookieRepository.findOne({
      where: {
        accountId: account.id,
        serverId,
      },
    });

    if (serverCookie && serverCookie.cookieData) {
      // Update last used timestamp
      serverCookie.lastUsed = new Date();
      await this.serverCookieRepository.save(serverCookie);
      return { cookieData: serverCookie.cookieData };
    }

    // Fall back to account cookie_data if no server-specific cookie is found
    if (account.cookie_data) {
      return { cookieData: account.cookie_data };
    }

    return null;
  }
}
