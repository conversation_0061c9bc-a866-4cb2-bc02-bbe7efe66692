import {
  <PERSON>,
  Post,
  Get,
  Delete,
  Patch,
  Body,
  Param,
  UseGuards,
  Req,
  Query,
  NotFoundException,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { CookiesService } from './cookies.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AdminGuard } from './guards/admin.guard';
import { CreateCookieDto } from './dto/create-cookie.dto';
import { CookieValidityDto } from './dto/cookie-validity.dto';
import { UpdateAllowedMembersDto } from './dto/update-allowed-members.dto';

@Controller('cookies')
export class CookiesController {
  constructor(private readonly cookiesService: CookiesService) {}

  // Route to save cookies (admin only)
  @UseGuards(JwtAuthGuard, AdminGuard)
  @Post('save')
  async saveCookie(@Body() createCookieDto: CreateCookieDto, @Req() req) {
    return this.cookiesService.saveCookie(createCookieDto, req.user.sub);
  }

  // Route to get all cookies (admin only)
  @UseGuards(JwtAuthGuard, AdminGuard)
  @Get('all')
  async getAllCookies() {
    return this.cookiesService.getAllCookies();
  }

  // Route to check cookies validity
  @UseGuards(JwtAuthGuard)
  @Post('validity')
  async checkCookieValidity(@Body() cookieValidityDto: CookieValidityDto) {
    return this.cookiesService.checkCookieValidity(
      cookieValidityDto.website_url,
    );
  }

  // Route to delete cookies for a website
  @UseGuards(JwtAuthGuard, AdminGuard)
  @Delete(':websiteUrl')
  async deleteCookie(@Param('websiteUrl') websiteUrl: string) {
    return { success: await this.cookiesService.deleteCookie(websiteUrl) };
  }

  // Route to update allowed members for a website
  @UseGuards(JwtAuthGuard, AdminGuard)
  @Patch('allowed-members')
  async updateAllowedMembers(@Body() updateDto: UpdateAllowedMembersDto) {
    return this.cookiesService.updateAllowedMembers(updateDto);
  }

  // Route to get all accessible sites for a member
  @UseGuards(JwtAuthGuard)
  @Get('accessible-sites')
  async getAccessibleSites(@Req() req) {
    return {
      sites: await this.cookiesService.getAccessibleSites(req.user.sub),
    };
  }

  // Route to check if cookies exist for a specific domain
  @UseGuards(JwtAuthGuard)
  @Get('exists/:websiteUrl')
  async checkCookieExists(@Param('websiteUrl') websiteUrl: string, @Req() req) {
    return {
      exists: await this.cookiesService.checkCookieExists(
        websiteUrl,
        req.user.sub,
      ),
    };
  }

  // Route to check if cookies exist for a specific domain (alternative endpoint)
  @UseGuards(JwtAuthGuard)
  @Get('check/:domain')
  async checkCookiesForDomain(@Param('domain') domain: string, @Req() req) {
    return {
      hasCookies: await this.cookiesService.checkCookieExists(
        domain,
        req.user.sub,
      ),
    };
  }

  // Route to get cookies for a specific website
  @UseGuards(JwtAuthGuard)
  @Get(':websiteUrl')
  async getCookieForWebsite(
    @Param('websiteUrl') websiteUrl: string,
    @Query('server_id', new DefaultValuePipe(1), ParseIntPipe) serverId: number,
    @Req() req,
  ) {
    const cookieData = await this.cookiesService.getCookieForWebsite(
      websiteUrl,
      req.user.sub,
      serverId,
    );

    if (!cookieData) {
      throw new NotFoundException(
        'No cookies found or you do not have access to this website',
      );
    }

    return cookieData;
  }
}
