<script>
  // Setup axios with credentials
  function setupAxios() {
    // Set axios to send cookies with all requests
    axios.defaults.withCredentials = true;
    return true;
  }

  // Handle token refresh
  async function refreshToken() {
    try {
      const response = await axios.post('/auth/refresh', {}, { withCredentials: true });
      return response.data.success;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  }

  // Handle API errors with token refresh
  async function handleApiError(error) {
    if (error.response && error.response.status === 401) {
      // Try to refresh the token
      const refreshSuccess = await refreshToken();
      
      if (refreshSuccess) {
        // Token refreshed successfully, you can retry the original request here
        console.log('Token refreshed successfully');
        return true;
      } else {
        // Refresh failed, redirect to login
        window.location.href = '/login';
        return false;
      }
    } else {
      console.error('API error:', error.response?.data?.message || 'An error occurred');
      return false;
    }
  }

  // Check authentication and admin role
  async function checkAuth() {
    try {
      const response = await axios.get('/auth/status', { withCredentials: true });
      if (!response.data.isAuthenticated) {
        window.location.href = '/login';
        return false;
      }

      // Check if user has admin role
      if (response.data.user && response.data.user.role !== 'admin') {
        // User is authenticated but not an admin
        window.location.href = '/login';
        return false;
      }

      return true;
    } catch (error) {
      // Try to refresh the token if it's an auth error
      if (error.response && error.response.status === 401) {
        const refreshSuccess = await refreshToken();
        
        if (refreshSuccess) {
          // Try checking auth again after refresh
          return checkAuth();
        }
      }
      
      window.location.href = '/login';
      return false;
    }
  }
</script>
