<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Discord Invite Success - Kitsify</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f9fafb;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    }
    .discord-bg {
      background-color: #5865F2;
    }
  </style>
</head>
<body>
  <div class="min-h-screen flex items-center justify-center px-4">
    <div class="max-w-md w-full bg-white rounded-lg shadow-lg overflow-hidden">
      <div class="discord-bg p-4 flex justify-center">
        <img src="https://assets-global.website-files.com/6257adef93867e50d84d30e2/636e0a6a49cf127bf92de1e2_icon_clyde_white_RGB.png" alt="Discord Logo" class="h-16">
      </div>
      <div class="p-8">
        <div class="flex justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h1 class="text-2xl font-bold text-center text-gray-800 mb-4">Thành Công!</h1>
        <p class="text-center text-gray-600 mb-6">
          Bạn đã được thêm vào Discord server của Kitsify thành công. Bạn đã được cấp quyền truy cập vào các kênh riêng tư.
        </p>
        <div class="flex justify-center">
          <a href="https://discord.com/channels/<%= process.env.DISCORD_GUILD_ID %>" class="discord-bg text-white font-bold py-2 px-6 rounded-full hover:bg-indigo-700 transition duration-300">
            Mở Discord
          </a>
        </div>
      </div>
      <div class="bg-gray-50 px-4 py-3 text-center">
        <p class="text-sm text-gray-600">
          &copy; <%= new Date().getFullYear() %> Kitsify. Tất cả các quyền được bảo lưu.
        </p>
      </div>
    </div>
  </div>
</body>
</html>
