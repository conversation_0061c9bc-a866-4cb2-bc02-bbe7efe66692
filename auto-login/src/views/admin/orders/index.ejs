<%- include('../../partials/layout', {
  title: 'Order Management',
  currentPath: '/admin/orders',
  body: `
    <div class="container mx-auto">
      <!-- Search and Filter -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="flex flex-col md:flex-row gap-4">
          <div class="flex-1">
            <label for="searchInput" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input
              type="text"
              id="searchInput"
              placeholder="Search by order ID or customer email"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          <div class="w-full md:w-48">
            <label for="statusSelect" class="block text-sm font-medium text-gray-700 mb-1">Filter by Status</label>
            <select
              id="statusSelect"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div class="w-full md:w-48">
            <label for="dateRangeSelect" class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
            <select
              id="dateRangeSelect"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="yesterday">Yesterday</option>
              <option value="last7days">Last 7 Days</option>
              <option value="last30days">Last 30 Days</option>
              <option value="thisMonth">This Month</option>
              <option value="lastMonth">Last Month</option>
            </select>
          </div>
          <div class="flex items-end">
            <button
              id="applyFilterBtn"
              class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>

      <!-- Orders Table -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody id="ordersTableBody" class="bg-white divide-y divide-gray-200">
            <!-- Order rows will be inserted here -->
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="mt-4 flex justify-between items-center">
        <div>
          <button id="prevPageBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-l">
            Previous
          </button>
          <button id="nextPageBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-r">
            Next
          </button>
        </div>
        <div class="text-gray-600">
          Page <span id="currentPage">1</span> of <span id="totalPages">1</span>
        </div>
      </div>
    </div>

    <!-- Update Status Modal -->
    <div id="updateStatusModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center z-50 hidden">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Update Order Status</p>
            <div class="modal-close cursor-pointer z-50" onclick="hideUpdateStatusModal()">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <div id="modalErrorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>

          <form id="updateStatusForm" class="mt-4">
            <input type="hidden" id="updateOrderId" value="" />
            
            <div class="mb-4">
              <label for="newStatus" class="block text-sm font-medium text-gray-700 mb-1">New Status</label>
              <select
                id="newStatus"
                name="newStatus"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div class="mb-4">
              <label for="statusNote" class="block text-sm font-medium text-gray-700 mb-1">Note (Optional)</label>
              <textarea
                id="statusNote"
                name="statusNote"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Add a note about this status change..."
              ></textarea>
            </div>

            <div class="flex justify-end pt-2">
              <button type="button" class="modal-close px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2" onclick="hideUpdateStatusModal()">Cancel</button>
              <button type="submit" class="px-4 bg-blue-500 p-3 rounded-lg text-white hover:bg-blue-600">Update Status</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  additionalScripts: `
    <script>
      // Global variables
      let currentPage = 1;
      let searchTerm = '';
      let statusFilter = 'all';
      let dateRangeFilter = 'all';
      let updateOrderId = null;

      // Load orders with pagination, search and filter
      async function loadOrders(page = 1) {
        try {
          // Show loading state
          const tableBody = document.getElementById('ordersTableBody');
          tableBody.innerHTML = '<tr><td colspan="8" class="px-6 py-4 text-center">Loading orders...</td></tr>';
          
          let url = '/orders/admin/all?page=' + page + '&limit=10';

          // Add search and filter parameters if they exist
          if (searchTerm) {
            url += '&search=' + encodeURIComponent(searchTerm);
          }

          if (statusFilter !== 'all') {
            url += '&status=' + statusFilter;
          }

          if (dateRangeFilter !== 'all') {
            url += '&dateRange=' + dateRangeFilter;
          }

          const response = await axios.get(url);
          const orders = response.data.data;
          const totalPages = response.data.meta.totalPages || 1;

          document.getElementById('currentPage').textContent = page;
          document.getElementById('totalPages').textContent = totalPages;

          // Update pagination button states
          document.getElementById('prevPageBtn').disabled = page <= 1;
          document.getElementById('nextPageBtn').disabled = page >= totalPages;
          
          if (page <= 1) {
            document.getElementById('prevPageBtn').classList.add('opacity-50', 'cursor-not-allowed');
          } else {
            document.getElementById('prevPageBtn').classList.remove('opacity-50', 'cursor-not-allowed');
          }
          
          if (page >= totalPages) {
            document.getElementById('nextPageBtn').classList.add('opacity-50', 'cursor-not-allowed');
          } else {
            document.getElementById('nextPageBtn').classList.remove('opacity-50', 'cursor-not-allowed');
          }

          renderOrdersTable(orders);
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            handleApiError(error);
          }
        }
      }

      // Render orders table
      function renderOrdersTable(orders) {
        const tableBody = document.getElementById('ordersTableBody');
        tableBody.innerHTML = '';

        if (!orders || orders.length === 0) {
          tableBody.innerHTML = 
            '<tr>' +
              '<td colspan="8" class="px-6 py-4 text-center text-gray-500">' +
                'No orders found. Try adjusting your search or filter criteria.' +
              '</td>' +
            '</tr>';
          return;
        }

        orders.forEach(order => {
          const row = document.createElement('tr');
          row.className = 'hover:bg-gray-50 border-b border-gray-200';

          // Format date
          const orderDate = new Date(order.createdAt);
          const formattedDate = orderDate.toLocaleDateString() + ' ' + orderDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          
          // Format total
          const total = Number(order.totalAmount).toFixed(2);

          // Status badge class and capitalized text
          let statusClass = '';
          let statusText = order.status ? order.status.charAt(0).toUpperCase() + order.status.slice(1) : 'Unknown';
          
          switch (order.status) {
            case 'pending':
              statusClass = 'bg-yellow-100 text-yellow-800';
              break;
            case 'processing':
              statusClass = 'bg-blue-100 text-blue-800';
              break;
            case 'shipped':
              statusClass = 'bg-purple-100 text-purple-800';
              break;
            case 'delivered':
              statusClass = 'bg-green-100 text-green-800';
              break;
            case 'cancelled':
              statusClass = 'bg-red-100 text-red-800';
              break;
            default:
              statusClass = 'bg-gray-100 text-gray-800';
          }

          // Payment status badge class
          let paymentClass = order.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
          let paymentStatusText = order.paymentStatus === 'paid' ? 'Paid' : 'Unpaid';

          // Get customer name or email
          const customerDisplay = order.customerEmail || 'Guest';

          row.innerHTML = 
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#' + order.id + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + formattedDate + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + customerDisplay + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">$' + total + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">' + (order.items ? order.items.length : 0) + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' +
              '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ' + statusClass + '">' +
                statusText +
              '</span>' +
            '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' +
              '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ' + paymentClass + '">' +
                paymentStatusText +
              '</span>' +
            '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">' +
              '<div class="flex space-x-2">' +
                '<button onclick="viewOrder(' + order.id + ')" class="text-indigo-600 hover:text-indigo-900 transition duration-150 ease-in-out">View</button>' +
                '<button onclick="showUpdateStatusModal(' + order.id + ', &quot;' + order.status + '&quot;)" class="text-blue-600 hover:text-blue-900 transition duration-150 ease-in-out">Update Status</button>' +
              '</div>' +
            '</td>';

          tableBody.appendChild(row);
        });
      }

      // View order details
      function viewOrder(id) {
        try {
          // Show loading state
          showToast('Loading order details...', 'info');
          window.location.href = '/admin/orders/' + id;
        } catch (error) {
          handleApiError(error);
        }
      }

      // Show update status modal
      function showUpdateStatusModal(id, currentStatus) {
        updateOrderId = id;
        document.getElementById('updateOrderId').value = id;
        document.getElementById('newStatus').value = currentStatus;
        document.getElementById('statusNote').value = '';
        document.getElementById('modalErrorMessage').classList.add('hidden');
        
        document.getElementById('updateStatusModal').classList.remove('hidden');
        document.getElementById('updateStatusModal').classList.remove('opacity-0');
        document.getElementById('updateStatusModal').classList.add('opacity-100');
        document.getElementById('updateStatusModal').classList.add('pointer-events-auto');
        document.body.classList.add('modal-active');
      }

      // Hide update status modal
      function hideUpdateStatusModal() {
        document.getElementById('updateStatusModal').classList.add('opacity-0');
        document.getElementById('updateStatusModal').classList.remove('opacity-100');
        document.getElementById('updateStatusModal').classList.add('pointer-events-none');
        setTimeout(() => {
          document.getElementById('updateStatusModal').classList.add('hidden');
          document.body.classList.remove('modal-active');
        }, 300);
        updateOrderId = null;
      }

      // Update order status
      async function updateOrderStatus(e) {
        e.preventDefault();
        
        const orderId = document.getElementById('updateOrderId').value;
        const newStatus = document.getElementById('newStatus').value;
        const statusNote = document.getElementById('statusNote').value;
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;

        if (!orderId || !newStatus) {
          showModalError('Missing required information');
          return;
        }

        try {
          // Show loading state
          submitBtn.disabled = true;
          submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
          
          await axios.patch('/orders/' + orderId + '/status', {
            status: newStatus,
            note: statusNote
          });
          
          hideUpdateStatusModal();
          showToast('Order #' + orderId + ' status updated to ' + newStatus.charAt(0).toUpperCase() + newStatus.slice(1));
          loadOrders(currentPage);
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to update order status. Please try again.';
            showModalError(message);
          }
        } finally {
          // Reset button state
          submitBtn.disabled = false;
          submitBtn.innerHTML = originalBtnText;
        }
      }

      // Show modal error message
      function showModalError(message) {
        const errorMessage = document.getElementById('modalErrorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
      }

      // Show toast message
      function showToast(message, type = 'success') {
        const backgroundColor = type === 'success' ? '#48bb78' : '#f56565';

        Toastify({
          text: message,
          duration: 3000,
          close: true,
          gravity: "top",
          position: "right",
          backgroundColor,
          stopOnFocus: true
        }).showToast();
      }

      // Handle API error
      function handleApiError(error) {
        let errorMessage = 'An error occurred. Please try again.';
        let type = 'error';
        
        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          if (error.response.status === 401) {
            errorMessage = 'Your session has expired. Please log in again.';
            type = 'warning';
            setTimeout(() => {
              window.location.href = '/login';
            }, 2000);
          } else if (error.response.status === 403) {
            errorMessage = 'You do not have permission to perform this action.';
            type = 'warning';
          } else if (error.response.status === 404) {
            errorMessage = 'The requested resource was not found.';
          } else if (error.response.status === 500) {
            errorMessage = 'Server error. Please try again later.';
          }
          
          // Use server provided message if available
          if (error.response.data && error.response.data.message) {
            errorMessage = error.response.data.message;
          }
        } else if (error.request) {
          // The request was made but no response was received
          errorMessage = 'No response from server. Please check your internet connection.';
        } else if (error.message) {
          // Something happened in setting up the request that triggered an Error
          errorMessage = error.message;
        }
        
        showToast(errorMessage, type);
      }

      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Document ready
      document.addEventListener('DOMContentLoaded', async () => {
        // Setup axios and check authentication
        setupAxios();
        const isAuthenticated = await checkAuth();
        if (!isAuthenticated) return;
        
        // Initial load
        loadOrders(currentPage);

        // Pagination
        document.getElementById('prevPageBtn').addEventListener('click', () => {
          if (currentPage > 1) {
            currentPage--;
            loadOrders(currentPage);
          }
        });

        document.getElementById('nextPageBtn').addEventListener('click', () => {
          const totalPages = parseInt(document.getElementById('totalPages').textContent);
          if (currentPage < totalPages) {
            currentPage++;
            loadOrders(currentPage);
          }
        });

        // Search and filter
        document.getElementById('applyFilterBtn').addEventListener('click', () => {
          searchTerm = document.getElementById('searchInput').value.trim();
          statusFilter = document.getElementById('statusSelect').value;
          dateRangeFilter = document.getElementById('dateRangeSelect').value;
          currentPage = 1;
          loadOrders(currentPage);
        });

        // Back to dashboard
        document.getElementById('backToHomeBtn').addEventListener('click', () => {
          window.location.href = '/admin/dashboard';
        });

        // Update status form submission
        document.getElementById('updateStatusForm').addEventListener('submit', updateOrderStatus);
      });
    </script>
  `
}) %>