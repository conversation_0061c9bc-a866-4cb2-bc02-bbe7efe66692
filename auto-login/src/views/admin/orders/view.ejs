<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin - View Order</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css">
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-6xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Order Details</h1>
        <div class="space-x-2">
          <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Orders
          </button>
        </div>
      </div>

      <div class="bg-white shadow rounded-lg overflow-hidden mb-6">
        <div class="p-6">
          <div id="errorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>

          <!-- Order Summary -->
          <div class="flex flex-col md:flex-row justify-between mb-8">
            <div>
              <h2 class="text-xl font-semibold mb-4">Order Information</h2>
              <div class="space-y-2">
                <div>
                  <span class="text-sm font-medium text-gray-500">Order ID:</span>
                  <span id="order-id" class="ml-2 text-sm text-gray-900"></span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">Date:</span>
                  <span id="order-date" class="ml-2 text-sm text-gray-900"></span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">Status:</span>
                  <span id="order-status" class="ml-2 text-sm px-2 py-1 rounded-full"></span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">Payment Status:</span>
                  <span id="payment-status" class="ml-2 text-sm px-2 py-1 rounded-full"></span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">Payment Method:</span>
                  <span id="payment-method" class="ml-2 text-sm text-gray-900"></span>
                </div>
              </div>
            </div>

            <div class="mt-6 md:mt-0">
              <h2 class="text-xl font-semibold mb-4">Customer Information</h2>
              <div class="space-y-2">
                <div>
                  <span class="text-sm font-medium text-gray-500">Email:</span>
                  <span id="customer-email" class="ml-2 text-sm text-gray-900"></span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">Name:</span>
                  <span id="customer-name" class="ml-2 text-sm text-gray-900"></span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">Phone:</span>
                  <span id="customer-phone" class="ml-2 text-sm text-gray-900"></span>
                </div>
              </div>
            </div>

            <div class="mt-6 md:mt-0">
              <h2 class="text-xl font-semibold mb-4">Order Summary</h2>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Subtotal:</span>
                  <span id="order-subtotal" class="ml-2 text-sm text-gray-900"></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-500">Total:</span>
                  <span id="order-total" class="ml-2 text-sm font-bold text-gray-900"></span>
                </div>
              </div>
            </div>
          </div>

          <!-- Shipping Address -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4">Shipping Address</h2>
            <address id="shipping-address" class="not-italic text-sm text-gray-700">
              No shipping address provided
            </address>
          </div>

          <!-- Customer Details -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4">Customer Details</h2>
            <div class="space-y-2">
              <div>
                <span class="text-sm font-medium text-gray-500">Name:</span>
                <p id="customer-name" class="text-sm text-gray-700"></p>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-500">Email:</span>
                <p id="customer-email" class="text-sm text-gray-700"></p>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-500">Phone:</span>
                <p id="customer-phone" class="text-sm text-gray-700"></p>
              </div>
            </div>
          </div>

          <!-- Order Notes -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4">Order Notes</h2>
            <div class="bg-gray-50 p-4 rounded-lg">
              <p id="order-notes" class="text-sm text-gray-700">No notes available</p>
            </div>
          </div>

          <!-- Order Items -->
          <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Order Items</h2>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                  </tr>
                </thead>
                <tbody id="order-items" class="bg-white divide-y divide-gray-200">
                  <!-- Order items will be inserted here -->
                </tbody>
              </table>
            </div>
          </div>

          <!-- Status History -->
          <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Status History</h2>
            <div id="status-history" class="space-y-4">
              <!-- Status history items will be inserted here -->
            </div>
          </div>

          <!-- Actions -->
          <div class="border-t pt-6">
            <h2 class="text-xl font-semibold mb-4">Actions</h2>
            <div class="flex flex-wrap gap-4">
              <button id="updateStatusBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Update Status
              </button>
              <button id="printInvoiceBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                Print Invoice
              </button>
              <button id="sendEmailBtn" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                Send Email to Customer
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Update Status Modal -->
    <div id="updateStatusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-semibold mb-4">Update Order Status</h3>
        <div id="modalErrorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>
        
        <form id="updateStatusForm">
          <div class="mb-4">
            <label for="newStatus" class="block text-sm font-medium text-gray-700 mb-1">New Status</label>
            <select
              id="newStatus"
              name="newStatus"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <div class="mb-4">
            <label for="statusNote" class="block text-sm font-medium text-gray-700 mb-1">Note (Optional)</label>
            <textarea
              id="statusNote"
              name="statusNote"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Add a note about this status change..."
            ></textarea>
          </div>

          <div class="flex justify-end space-x-2">
            <button type="button" id="cancelUpdateBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
              Cancel
            </button>
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              Update Status
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Email Modal -->
    <div id="emailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto w-full">
        <h3 class="text-xl font-semibold mb-4">Send Email to Customer</h3>
        <div id="emailErrorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>
        
        <form id="sendEmailForm">
          <div class="mb-4">
            <label for="emailSubject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
            <input
              type="text"
              id="emailSubject"
              name="emailSubject"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Email subject"
            />
          </div>

          <div class="mb-4">
            <label for="emailMessage" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
            <textarea
              id="emailMessage"
              name="emailMessage"
              rows="6"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Write your message here..."
            ></textarea>
          </div>

          <div class="flex justify-end space-x-2">
            <button type="button" id="cancelEmailBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
              Cancel
            </button>
            <button type="submit" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
              Send Email
            </button>
          </div>
        </form>
      </div>
    </div>

    <script>
      // Get order ID from server-rendered data
      const orderId = `<%- id %>`; // Use the ID passed from the server
      
      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Show toast notification
      function showToast(message, type = 'success') {
        const backgroundColor = type === 'success' ? '#48bb78' : '#f56565';

        Toastify({
          text: message,
          duration: 3000,
          close: true,
          gravity: "top",
          position: "right",
          backgroundColor,
          stopOnFocus: true
        }).showToast();
      }

      // Show error message
      function showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        showToast(message, 'error');
      }

      // Format date
      function formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      }

      // Format price
      function formatPrice(price) {
        if (price === undefined || price === null) return '$0.00';
        return '$' + Number(price).toFixed(2);
      }

      // Fetch order details
      async function fetchOrder(id) {
        if (!setupAxios()) return;

        try {
          const response = await axios.get(`/orders/admin/${id}`);
          return response.data;
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to fetch order details. Please try again.';
            showError(message);
          }
          return null;
        }
      }

      // Display order details
      function displayOrderDetails(order) {
        // Populate order details
        document.getElementById('order-id').textContent = order.id;
        document.getElementById('order-date').textContent = new Date(order.created_at).toLocaleDateString();
        document.getElementById('order-status').textContent = order.status;
        document.getElementById('order-total').textContent = formatPrice(order.total_amount);
        document.getElementById('order-subtotal').textContent = formatPrice(order.subtotal);

        // Populate items
        const itemsContainer = document.getElementById('order-items');
        displayOrderItems(order.items || []);

        // Customer and Shipping
        document.getElementById('customer-name').textContent = order.customer_name || 'N/A';
        document.getElementById('customer-email').textContent = order.customer_email || 'N/A';
        document.getElementById('customer-phone').textContent = order.customer_phone || 'N/A';

        // Payment Details
        document.getElementById('payment-method').textContent = order.payment_method || 'N/A';
        document.getElementById('payment-status').textContent = order.payment_status || 'N/A';

        // Shipping address
        const shippingAddress = document.getElementById('shipping-address');
        if (order.shipping_address) {
          shippingAddress.textContent = order.shipping_address;
        } else {
          shippingAddress.textContent = 'No shipping address provided';
        }
        
        // Order notes
        if (order.notes) {
          document.getElementById('order-notes').textContent = order.notes;
        }
        
        // Status history
        displayStatusHistory(order.status_history || []);
      }

      // Display order items
      function displayOrderItems(items) {
        const tableBody = document.getElementById('order-items');
        tableBody.innerHTML = '';
        
        if (items.length === 0) {
          const emptyRow = document.createElement('tr');
          emptyRow.innerHTML = '<td colspan="4" class="px-6 py-4 text-center text-gray-500">No items in this order</td>';
          tableBody.appendChild(emptyRow);
          return;
        }
        
        items.forEach(item => {
          const row = document.createElement('tr');
          
          const productCell = document.createElement('td');
          productCell.className = 'px-6 py-4 whitespace-nowrap';
          
          // Product with image if available
          const productDiv = document.createElement('div');
          productDiv.className = 'flex items-center';
          
          if (item.product && item.product.image_url) {
            const imgDiv = document.createElement('div');
            imgDiv.className = 'flex-shrink-0 h-10 w-10 mr-4';
            
            const img = document.createElement('img');
            img.className = 'h-10 w-10 rounded-full object-cover';
            img.src = item.product.image_url;
            img.alt = item.product.name;
            
            imgDiv.appendChild(img);
            productDiv.appendChild(imgDiv);
          }
          
          const nameDiv = document.createElement('div');
          nameDiv.className = 'text-sm font-medium text-gray-900';
          nameDiv.textContent = item.product ? item.product.name : item.product_name;
          
          productDiv.appendChild(nameDiv);
          productCell.appendChild(productDiv);
          row.appendChild(productCell);
          
          // Price
          const priceCell = document.createElement('td');
          priceCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';
          priceCell.textContent = formatPrice(item.price);
          row.appendChild(priceCell);
          
          // Quantity
          const quantityCell = document.createElement('td');
          quantityCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';
          quantityCell.textContent = item.quantity;
          row.appendChild(quantityCell);
          
          // Total
          const totalCell = document.createElement('td');
          totalCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';
          totalCell.textContent = formatPrice(item.price * item.quantity);
          row.appendChild(totalCell);
          
          tableBody.appendChild(row);
        });
      }

      // Display status history
      function displayStatusHistory(history) {
        const container = document.getElementById('status-history');
        container.innerHTML = '';
        
        if (history.length === 0) {
          const emptyMessage = document.createElement('p');
          emptyMessage.className = 'text-sm text-gray-500';
          emptyMessage.textContent = 'No status history available';
          container.appendChild(emptyMessage);
          return;
        }
        
        // Sort by date (newest first)
        history.sort((a, b) => new Date(b.date) - new Date(a.date));
        
        history.forEach(entry => {
          const item = document.createElement('div');
          item.className = 'bg-gray-50 p-4 rounded-lg';
          
          const header = document.createElement('div');
          header.className = 'flex justify-between items-center mb-2';
          
          const status = document.createElement('span');
          let statusClass = '';
          switch (entry.status) {
            case 'pending':
              statusClass = 'bg-yellow-100 text-yellow-800';
              break;
            case 'processing':
              statusClass = 'bg-blue-100 text-blue-800';
              break;
            case 'shipped':
              statusClass = 'bg-purple-100 text-purple-800';
              break;
            case 'delivered':
              statusClass = 'bg-green-100 text-green-800';
              break;
            case 'cancelled':
              statusClass = 'bg-red-100 text-red-800';
              break;
            default:
              statusClass = 'bg-gray-100 text-gray-800';
          }
          status.className = `px-2 py-1 rounded-full text-xs font-semibold ${statusClass}`;
          status.textContent = entry.status.charAt(0).toUpperCase() + entry.status.slice(1);
          
          const date = document.createElement('span');
          date.className = 'text-xs text-gray-500';
          date.textContent = formatDate(entry.date);
          
          header.appendChild(status);
          header.appendChild(date);
          item.appendChild(header);
          
          if (entry.note) {
            const note = document.createElement('p');
            note.className = 'text-sm text-gray-700';
            note.textContent = entry.note;
            item.appendChild(note);
          }
          
          container.appendChild(item);
        });
      }

      // Update order status
      async function updateOrderStatus(e) {
        e.preventDefault();
        
        const newStatus = document.getElementById('newStatus').value;
        const statusNote = document.getElementById('statusNote').value;

        if (!newStatus) {
          showModalError('Missing required information');
          return;
        }

        try {
          await axios.patch(`/orders/${orderId}/status`, {
            status: newStatus,
            note: statusNote
          });
          
          hideUpdateStatusModal();
          showToast('Order status updated successfully');
          
          // Refresh order details
          const order = await fetchOrder(orderId);
          if (order) {
            displayOrderDetails(order);
          }
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to update order status. Please try again.';
            showModalError(message);
          }
        }
      }

      // Send email to customer
      async function sendEmailToCustomer(e) {
        e.preventDefault();
        
        const subject = document.getElementById('emailSubject').value;
        const message = document.getElementById('emailMessage').value;

        if (!subject || !message) {
          showEmailError('Subject and message are required');
          return;
        }

        try {
          await axios.post(`/orders/${orderId}/email`, {
            subject,
            message
          });
          
          hideEmailModal();
          showToast('Email sent successfully');
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to send email. Please try again.';
            showEmailError(message);
          }
        }
      }

      // Print invoice
      function printInvoice() {
        window.open(`/orders/${orderId}/invoice`, '_blank');
      }

      // Show update status modal
      function showUpdateStatusModal() {
        document.getElementById('modalErrorMessage').classList.add('hidden');
        document.getElementById('updateStatusModal').classList.remove('hidden');
      }

      // Hide update status modal
      function hideUpdateStatusModal() {
        document.getElementById('updateStatusModal').classList.add('hidden');
      }

      // Show email modal
      function showEmailModal() {
        document.getElementById('emailErrorMessage').classList.add('hidden');
        document.getElementById('emailModal').classList.remove('hidden');
      }

      // Hide email modal
      function hideEmailModal() {
        document.getElementById('emailModal').classList.add('hidden');
      }

      // Show modal error message
      function showModalError(message) {
        const errorMessage = document.getElementById('modalErrorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
      }

      // Show email error message
      function showEmailError(message) {
        const errorMessage = document.getElementById('emailErrorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
      }

      // Event listeners
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial setup
        setupAxios();
        await checkAuth();

        // Fetch and display order details
        if (orderId) {
          const order = await fetchOrder(orderId);
          if (order) {
            displayOrderDetails(order);
          }
        } else {
          showError('Order ID not found in URL');
        }

        // Button event listeners
        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = '/admin/orders';
        });

        document.getElementById('updateStatusBtn').addEventListener('click', showUpdateStatusModal);
        document.getElementById('cancelUpdateBtn').addEventListener('click', hideUpdateStatusModal);
        document.getElementById('updateStatusForm').addEventListener('submit', updateOrderStatus);

        document.getElementById('printInvoiceBtn').addEventListener('click', printInvoice);

        document.getElementById('sendEmailBtn').addEventListener('click', showEmailModal);
        document.getElementById('cancelEmailBtn').addEventListener('click', hideEmailModal);
        document.getElementById('sendEmailForm').addEventListener('submit', sendEmailToCustomer);
      });
    </script>
  </body>
</html>