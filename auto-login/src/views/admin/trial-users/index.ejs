<%- include('../../partials/layout', {
  title: 'Trial User Management',
  currentPath: '/admin/trial-users',
  body: `
    <div class="container mx-auto">
      <div class="flex justify-between items-center mb-6">
        <div>
          <button id="createTrialUserBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
            Create Trial User
          </button>
          <button id="backToHomeBtn" class="ml-2 bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Home
          </button>
        </div>
      </div>

      <!-- Trial Users Table -->
      <div class="bg-white shadow-md rounded-lg overflow-hidden mb-6">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expires At</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody id="trialUsersTableBody" class="bg-white divide-y divide-gray-200">
            <!-- Trial users will be loaded here -->
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="flex justify-between items-center">
        <div>
          <button id="prevPageBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-l">
            Previous
          </button>
          <button id="nextPageBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-r">
            Next
          </button>
        </div>
        <div>
          Page <span id="currentPage">1</span> of <span id="totalPages">1</span>
        </div>
      </div>
    </div>

    <!-- Create Trial User Modal -->
    <div id="createTrialUserModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center hidden z-50">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Create Trial User</p>
            <div class="modal-close cursor-pointer z-50">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <form id="createTrialUserForm">
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="email">
                Email (Auto-generated)
              </label>
              <div class="flex">
                <input class="shadow appearance-none border rounded-l w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="email" type="email" readonly>
                <button type="button" id="copyEmailBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-r">
                  Copy
                </button>
              </div>
              <button type="button" id="generateEmailBtn" class="mt-2 bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-1 px-2 rounded text-sm">
                Generate New Email
              </button>
            </div>

            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="password">
                Password (Auto-generated)
              </label>
              <div class="flex">
                <input class="shadow appearance-none border rounded-l w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="password" type="text" readonly>
                <button type="button" id="copyPasswordBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-r">
                  Copy
                </button>
              </div>
            </div>

            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="trial_days">
                Trial Duration (Days)
              </label>
              <select class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="trial_days">
                <option value="1">1 day</option>
                <option value="2">2 days</option>
                <option value="3">3 days</option>
                <option value="4">4 days</option>
                <option value="5" selected>5 days</option>
                <option value="6">6 days</option>
                <option value="7">7 days</option>
              </select>
            </div>

            <div class="mb-6">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="package_id">
                Package
              </label>
              <select class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="package_id">
                <!-- Packages will be loaded here -->
                <option value="">Loading packages...</option>
              </select>
            </div>

            <div class="flex justify-end pt-2">
              <button type="button" id="cancelCreateBtn" class="px-4 bg-transparent p-3 rounded-lg text-indigo-500 hover:bg-gray-100 hover:text-indigo-400 mr-2">Cancel</button>
              <button type="submit" id="submitCreateBtn" class="px-4 bg-indigo-500 p-3 rounded-lg text-white hover:bg-indigo-400">Create</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- View Trial User Modal -->
    <div id="viewTrialUserModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center hidden z-50">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Trial User Details</p>
            <div class="modal-close cursor-pointer z-50">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">ID</label>
            <p id="view-user-id" class="py-2 px-3 bg-gray-100 rounded text-gray-700"></p>
          </div>

          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">Email</label>
            <div class="flex">
              <p id="view-user-email" class="py-2 px-3 bg-gray-100 rounded-l w-full text-gray-700"></p>
              <button type="button" id="viewCopyEmailBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-r">
                Copy
              </button>
            </div>
          </div>

          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">Password</label>
            <div class="flex">
              <p id="view-user-password" class="py-2 px-3 bg-gray-100 rounded-l w-full text-gray-700"></p>
              <button type="button" id="viewCopyPasswordBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-r">
                Copy
              </button>
            </div>
          </div>

          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">Status</label>
            <p id="view-user-status" class="py-2 px-3 bg-gray-100 rounded text-gray-700"></p>
          </div>

          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">Created At</label>
            <p id="view-user-created" class="py-2 px-3 bg-gray-100 rounded text-gray-700"></p>
          </div>

          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">Package</label>
            <p id="view-user-package" class="py-2 px-3 bg-gray-100 rounded text-gray-700"></p>
          </div>

          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">Expires At</label>
            <p id="view-user-expires" class="py-2 px-3 bg-gray-100 rounded text-gray-700"></p>
          </div>

          <div class="flex justify-end pt-2">
            <button id="closeViewBtn" class="px-4 bg-gray-500 p-3 rounded-lg text-white hover:bg-gray-400">Close</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center hidden z-50">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Confirm Delete</p>
            <div class="modal-close cursor-pointer z-50">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <p class="mb-4">Are you sure you want to delete this trial user? This action cannot be undone.</p>

          <div class="flex justify-end pt-2">
            <button id="cancelDeleteBtn" class="px-4 bg-transparent p-3 rounded-lg text-indigo-500 hover:bg-gray-100 hover:text-indigo-400 mr-2">Cancel</button>
            <button id="confirmDeleteBtn" class="px-4 bg-red-500 p-3 rounded-lg text-white hover:bg-red-400">Delete</button>
          </div>
        </div>
      </div>
    </div>
  `,
  additionalScripts: `
    <script>
      // Global variables
      let currentPage = 1;
      let deleteUserId = null;

      // Generate random email
      function generateRandomEmail() {
        const thaiNames = [
          'somchai', 'somsak', 'somying', 'somrak', 'somjai',
          'wichai', 'wichan', 'wichit', 'wichian', 'wichuda',
          'sombat', 'somboon', 'somkiat', 'somkid', 'somkiet',
          'thaksin', 'thawee', 'thawil', 'thaworn', 'thawat',
          'prasert', 'prasit', 'pradit', 'prapan', 'prapas'
        ];

        const domains = ['gmail.com', 'hotmail.com', 'yahoo.com', 'outlook.com', 'mail.com'];

        const randomName = thaiNames[Math.floor(Math.random() * thaiNames.length)];
        const randomNumber = Math.floor(Math.random() * 10000);
        const randomDomain = domains[Math.floor(Math.random() * domains.length)];

        return randomName + randomNumber + '@' + randomDomain;
      }

      // Generate random password
      function generateRandomPassword() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
        let password = '';

        // Ensure at least one uppercase, one lowercase, one number, and one special character
        password += chars.charAt(Math.floor(Math.random() * 26)); // Uppercase
        password += chars.charAt(26 + Math.floor(Math.random() * 26)); // Lowercase
        password += chars.charAt(52 + Math.floor(Math.random() * 10)); // Number
        password += chars.charAt(62 + Math.floor(Math.random() * 8)); // Special

        // Add more random characters to reach length 8
        for (let i = 0; i < 4; i++) {
          password += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        // Shuffle the password
        return password.split('').sort(() => 0.5 - Math.random()).join('');
      }

      // Load trial users with pagination
      async function loadTrialUsers(page = 1) {
        try {
          const response = await axios.get('/users/trial?page=' + page + '&limit=10');
          const users = response.data.data;
          const totalPages = response.data.meta.totalPages;

          document.getElementById('currentPage').textContent = page;
          document.getElementById('totalPages').textContent = totalPages;

          renderTrialUsersTable(users);
        } catch (error) {
          handleApiError(error);
        }
      }

      // Render trial users table
      function renderTrialUsersTable(users) {
        const tableBody = document.getElementById('trialUsersTableBody');
        tableBody.innerHTML = '';

        if (!users || users.length === 0) {
          const row = document.createElement('tr');
          row.innerHTML =
            '<td colspan="6" class="px-6 py-4 text-center text-gray-500">' +
              'No trial users found' +
            '</td>';
          tableBody.appendChild(row);
          return;
        }

        users.forEach(user => {
          const row = document.createElement('tr');
          const createdAt = new Date(user.created_at).toLocaleString();

          // Get expiration date from packageUsers if available
          let expiresAt = 'N/A';
          if (user.packageUsers && user.packageUsers.length > 0) {
            // Find the active package with the latest expiration date
            const activePackages = user.packageUsers.filter(pu => pu.status === 'active');
            if (activePackages.length > 0) {
              const latestPackage = activePackages.reduce((latest, current) => {
                return new Date(current.expires_at) > new Date(latest.expires_at) ? current : latest;
              }, activePackages[0]);

              expiresAt = new Date(latestPackage.expires_at).toLocaleString();
            }
          }

          row.innerHTML =
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">' + user.id + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + user.email + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + user.status + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + createdAt + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + expiresAt + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">' +
              '<div class="flex space-x-2">' +
                '<button onclick="viewTrialUser(' + user.id + ')" class="text-indigo-600 hover:text-indigo-900">View</button>' +
                '<button onclick="showDeleteModal(' + user.id + ')" class="text-red-600 hover:text-red-900">Delete</button>' +
              '</div>' +
            '</td>';

          tableBody.appendChild(row);
        });
      }

      // Load packages for dropdown
      async function loadPackages() {
        try {
          const response = await axios.get('/packages?limit=100');
          const packages = response.data.data;

          const packageDropdown = document.getElementById('package_id');
          packageDropdown.innerHTML = '';

          if (packages && packages.length > 0) {
            packages.forEach(pkg => {
              const option = document.createElement('option');
              option.value = pkg.id;
              option.textContent = pkg.name;
              packageDropdown.appendChild(option);
            });
          } else {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'No packages available';
            packageDropdown.appendChild(option);
          }
        } catch (error) {
          console.error('Error loading packages:', error);
          const packageDropdown = document.getElementById('package_id');
          packageDropdown.innerHTML = '<option value="">Error loading packages</option>';
        }
      }

      // Show create trial user modal
      function showCreateModal() {
        document.getElementById('createTrialUserModal').classList.remove('hidden', 'opacity-0', 'pointer-events-none');
        document.body.classList.add('modal-active');

        // Generate random email and password
        document.getElementById('email').value = generateRandomEmail();
        document.getElementById('password').value = generateRandomPassword();

        // Load packages for the dropdown
        loadPackages();
      }

      // Hide create trial user modal
      function hideCreateModal() {
        document.getElementById('createTrialUserModal').classList.add('hidden', 'opacity-0', 'pointer-events-none');
        document.body.classList.remove('modal-active');
        document.getElementById('createTrialUserForm').reset();
        document.getElementById('email').value = '';
        document.getElementById('password').value = '';
      }

      // Generate new email
      function generateNewEmail() {
        document.getElementById('email').value = generateRandomEmail();
      }

      // Show view trial user modal
      async function viewTrialUser(userId) {
        try {
          const response = await axios.get('/users/trial/' + userId);
          const user = response.data;

          // Populate the modal with user data
          document.getElementById('view-user-id').textContent = user.id;
          document.getElementById('view-user-email').textContent = user.email;
          document.getElementById('view-user-password').textContent = user.plain_password || 'Not available';
          document.getElementById('view-user-status').textContent = user.status;
          document.getElementById('view-user-created').textContent = new Date(user.created_at).toLocaleString();

          // Get package and expiration date from packageUsers if available
          let expiresAt = 'N/A';
          let packageName = 'N/A';

          if (user.packageUsers && user.packageUsers.length > 0) {
            // Find the active package with the latest expiration date
            const activePackages = user.packageUsers.filter(pu => pu.status === 'active');
            if (activePackages.length > 0) {
              const latestPackage = activePackages.reduce((latest, current) => {
                return new Date(current.expires_at) > new Date(latest.expires_at) ? current : latest;
              }, activePackages[0]);

              expiresAt = new Date(latestPackage.expires_at).toLocaleString();

              // Get package name if available
              if (latestPackage.packageInfo && latestPackage.packageInfo.name) {
                packageName = latestPackage.packageInfo.name;
              } else {
                packageName = 'Package ID: ' + latestPackage.package_id;
              }
            }
          }

          document.getElementById('view-user-package').textContent = packageName;
          document.getElementById('view-user-expires').textContent = expiresAt;

          // Show the modal
          document.getElementById('viewTrialUserModal').classList.remove('hidden', 'opacity-0', 'pointer-events-none');
          document.body.classList.add('modal-active');
        } catch (error) {
          handleApiError(error);
        }
      }

      // Hide view trial user modal
      function hideViewModal() {
        document.getElementById('viewTrialUserModal').classList.add('hidden', 'opacity-0', 'pointer-events-none');
        document.body.classList.remove('modal-active');
      }

      // Show delete confirmation modal
      function showDeleteModal(userId) {
        deleteUserId = userId;
        document.getElementById('deleteModal').classList.remove('hidden', 'opacity-0', 'pointer-events-none');
        document.body.classList.add('modal-active');
      }

      // Hide delete confirmation modal
      function hideDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden', 'opacity-0', 'pointer-events-none');
        document.body.classList.remove('modal-active');
        deleteUserId = null;
      }

      // Copy text from view modal
      function copyViewText(elementId, message) {
        const text = document.getElementById(elementId).textContent;
        if (!text || text === 'Not available') return;

        // Create a temporary input element
        const tempInput = document.createElement('input');
        tempInput.value = text;
        document.body.appendChild(tempInput);

        // Select and copy
        tempInput.select();
        document.execCommand('copy');

        // Remove the temporary element
        document.body.removeChild(tempInput);

        showToast(message);
      }

      // Create trial user
      async function createTrialUser(event) {
        event.preventDefault();

        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const trial_days = parseInt(document.getElementById('trial_days').value);
        const package_id = parseInt(document.getElementById('package_id').value);

        if (!package_id) {
          showToast('Please select a package', 'error');
          return;
        }

        try {
          const response = await axios.post('/users/trial', {
            email,
            trial_days,
            password, // Pass the generated password to backend
            package_id // Pass the selected package_id to backend
          });

          showToast('Trial user created successfully');
          loadTrialUsers(currentPage);
          hideCreateModal();
        } catch (error) {
          if (error.response && error.response.status === 409) {
            // Email already exists, generate a new one
            document.getElementById('email').value = generateRandomEmail();
            showToast('Email already exists. A new email has been generated.', 'error');
          } else {
            handleApiError(error);
          }
        }
      }

      // Delete trial user
      async function deleteTrialUser() {
        if (!deleteUserId) return;

        try {
          await axios.delete('/users/trial/' + deleteUserId);
          hideDeleteModal();
          showToast('Trial user deleted successfully');
          loadTrialUsers(currentPage);
        } catch (error) {
          hideDeleteModal();
          handleApiError(error);
        }
      }

      // Copy text to clipboard
      function copyToClipboard(elementId, message) {
        const field = document.getElementById(elementId);
        if (!field.value) return;

        field.select();
        document.execCommand('copy');
        showToast(message);
      }

      // Document ready
      document.addEventListener('DOMContentLoaded', () => {
        // Initial load
        loadTrialUsers();

        // Event listeners
        document.getElementById('createTrialUserBtn').addEventListener('click', showCreateModal);
        document.getElementById('cancelCreateBtn').addEventListener('click', hideCreateModal);
        document.getElementById('createTrialUserForm').addEventListener('submit', createTrialUser);

        // Generate new email
        document.getElementById('generateEmailBtn').addEventListener('click', generateNewEmail);

        // Copy buttons
        document.getElementById('copyEmailBtn').addEventListener('click', () => {
          copyToClipboard('email', 'Email copied to clipboard');
        });
        document.getElementById('copyPasswordBtn').addEventListener('click', () => {
          copyToClipboard('password', 'Password copied to clipboard');
        });

        // Delete trial user
        document.getElementById('cancelDeleteBtn').addEventListener('click', hideDeleteModal);
        document.getElementById('confirmDeleteBtn').addEventListener('click', deleteTrialUser);

        // View modal
        document.getElementById('closeViewBtn').addEventListener('click', hideViewModal);
        document.getElementById('viewCopyEmailBtn').addEventListener('click', () => {
          copyViewText('view-user-email', 'Email copied to clipboard');
        });
        document.getElementById('viewCopyPasswordBtn').addEventListener('click', () => {
          copyViewText('view-user-password', 'Password copied to clipboard');
        });

        // Modal close buttons
        document.querySelectorAll('.modal-close').forEach(button => {
          button.addEventListener('click', () => {
            hideCreateModal();
            hideDeleteModal();
            hideViewModal();
          });
        });

        // Modal overlays
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
          overlay.addEventListener('click', () => {
            hideCreateModal();
            hideDeleteModal();
            hideViewModal();
          });
        });

        // Back to home
        document.getElementById('backToHomeBtn').addEventListener('click', () => {
          window.location.href = '/';
        });
      });
    </script>
  `
}) %>
