<%- include('../../partials/layout', {
  title: 'Campaign Management',
  currentPath: '/admin/campaigns',
  body: `
    <div class="container mx-auto">
      <div class="flex justify-between items-center mb-6">
        <div>
          <button id="createCampaignBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
            Create New Campaign
          </button>
          <button id="backToHomeBtn" class="ml-2 bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Home
          </button>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="flex flex-col md:flex-row gap-4">
          <div class="flex-1">
            <label for="searchInput" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input
              type="text"
              id="searchInput"
              placeholder="Search by name or subject"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          <div class="w-full md:w-48">
            <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">Filter by Status</label>
            <select
              id="statusFilter"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">All Campaigns</option>
              <option value="draft">Draft</option>
              <option value="scheduled">Scheduled</option>
              <option value="sending">Sending</option>
              <option value="sent">Sent</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div class="flex items-end">
            <button
              id="applyFilterBtn"
              class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>

      <!-- Campaigns Table -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Target</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipients</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody id="campaignsTableBody" class="bg-white divide-y divide-gray-200">
            <!-- Campaign rows will be inserted here -->
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="mt-4 flex justify-between items-center">
        <div>
          <button id="prevPageBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-l">
            Previous
          </button>
          <button id="nextPageBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-r">
            Next
          </button>
        </div>
        <div class="text-gray-600">
          Page <span id="currentPage">1</span> of <span id="totalPages">1</span>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center z-50 hidden">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Confirm Delete</p>
            <div class="modal-close cursor-pointer z-50" onclick="hideDeleteModal()">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <p>Are you sure you want to delete this campaign? This action cannot be undone.</p>

          <div class="flex justify-end pt-2">
            <button class="modal-close px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2" onclick="hideDeleteModal()">Cancel</button>
            <button class="px-4 bg-red-500 p-3 rounded-lg text-white hover:bg-red-600" onclick="deleteCampaign()">Delete</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Send Confirmation Modal -->
    <div id="sendModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center z-50 hidden">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Confirm Send</p>
            <div class="modal-close cursor-pointer z-50" onclick="hideSendModal()">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <p>Are you sure you want to send this campaign? This action cannot be undone.</p>

          <div class="flex justify-end pt-2">
            <button class="modal-close px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2" onclick="hideSendModal()">Cancel</button>
            <button class="px-4 bg-green-500 p-3 rounded-lg text-white hover:bg-green-600" onclick="sendCampaign()">Send Campaign</button>
          </div>
        </div>
      </div>
    </div>
  `,
  additionalScripts: `
    <script>
      // Global variables
      let currentPage = 1;
      let searchTerm = '';
      let statusFilter = '';
      let deleteCampaignId = null;
      let sendCampaignId = null;

      // Load campaigns with pagination, search and filter
      async function loadCampaigns(page = 1) {
        try {
          let url = '/campaigns?page=' + page + '&limit=10';

          // Add search and filter parameters if they exist
          if (searchTerm) {
            url += '&search=' + encodeURIComponent(searchTerm);
          }

          if (statusFilter) {
            url += '&status=' + statusFilter;
          }

          const response = await axios.get(url);
          const campaigns = response.data.data;
          const totalPages = response.data.meta.totalPages;

          document.getElementById('currentPage').textContent = page;
          document.getElementById('totalPages').textContent = totalPages;

          renderCampaignsTable(campaigns);
        } catch (error) {
          handleApiError(error);
        }
      }

      // Render campaigns table
      function renderCampaignsTable(campaigns) {
        const tableBody = document.getElementById('campaignsTableBody');
        tableBody.innerHTML = '';

        if (campaigns.length === 0) {
          const emptyRow = document.createElement('tr');
          emptyRow.innerHTML =
            '<td colspan="9" class="px-6 py-4 text-center text-gray-500">' +
              'No campaigns found' +
            '</td>';
          tableBody.appendChild(emptyRow);
          return;
        }

        campaigns.forEach(campaign => {
          const row = document.createElement('tr');
          row.className = 'hover:bg-gray-50';

          const statusBadge = getStatusBadge(campaign.status);
          const createdAt = new Date(campaign.created_at).toLocaleDateString();

          row.innerHTML =
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">' + campaign.id + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + campaign.name + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (campaign.subject.length > 50 ? campaign.subject.substring(0, 50) + '...' : campaign.subject) + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + formatTargetAudience(campaign.target_audience) + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap">' + statusBadge + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (campaign.total_recipients || 0) + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (campaign.sent_count || 0) + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + createdAt + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">' +
              '<div class="flex space-x-2">' +
                '<button onclick="viewCampaign(' + campaign.id + ')" class="text-indigo-600 hover:text-indigo-900">View</button>' +
                (campaign.status === 'draft' || campaign.status === 'scheduled' ? 
                  '<button onclick="showSendModal(' + campaign.id + ')" class="text-green-600 hover:text-green-900">Send</button>' : '') +
                (campaign.status !== 'sending' ? 
                  '<button onclick="showDeleteModal(' + campaign.id + ')" class="text-red-600 hover:text-red-900">Delete</button>' : '') +
              '</div>' +
            '</td>';

          tableBody.appendChild(row);
        });
      }

      function getStatusBadge(status) {
        const badges = {
          'draft': '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Draft</span>',
          'scheduled': '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Scheduled</span>',
          'sending': '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Sending</span>',
          'sent': '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Sent</span>',
          'cancelled': '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Cancelled</span>',
        };
        return badges[status] || status;
      }

      function formatTargetAudience(audience) {
        const audiences = {
          'all': 'All Users',
          'active': 'Active Users',
          'inactive': 'Inactive Users',
          'trial': 'Trial Users',
          'exclude_trial': 'Exclude Trial',
          'exclude_active': 'Exclude Active',
          'exclude_inactive': 'Exclude Inactive',
        };
        return audiences[audience] || audience;
      }

      // View campaign details
      function viewCampaign(id) {
        window.location.href = '/admin/campaigns/' + id;
      }

      // Show delete confirmation modal
      function showDeleteModal(id) {
        deleteCampaignId = id;
        document.getElementById('deleteModal').classList.remove('hidden');
        document.getElementById('deleteModal').classList.remove('opacity-0');
        document.getElementById('deleteModal').classList.add('opacity-100');
        document.getElementById('deleteModal').classList.add('pointer-events-auto');
        document.body.classList.add('modal-active');
      }

      // Hide delete confirmation modal
      function hideDeleteModal() {
        document.getElementById('deleteModal').classList.add('opacity-0');
        document.getElementById('deleteModal').classList.remove('opacity-100');
        document.getElementById('deleteModal').classList.add('pointer-events-none');
        setTimeout(() => {
          document.getElementById('deleteModal').classList.add('hidden');
          document.body.classList.remove('modal-active');
        }, 300);
        deleteCampaignId = null;
      }

      // Delete campaign
      async function deleteCampaign() {
        if (!deleteCampaignId) return;

        try {
          await axios.delete('/campaigns/' + deleteCampaignId);
          hideDeleteModal();
          showToast('Campaign deleted successfully');
          loadCampaigns(currentPage);
        } catch (error) {
          hideDeleteModal();
          handleApiError(error);
        }
      }

      // Show send confirmation modal
      function showSendModal(id) {
        sendCampaignId = id;
        document.getElementById('sendModal').classList.remove('hidden');
        document.getElementById('sendModal').classList.remove('opacity-0');
        document.getElementById('sendModal').classList.add('opacity-100');
        document.getElementById('sendModal').classList.add('pointer-events-auto');
        document.body.classList.add('modal-active');
      }

      // Hide send confirmation modal
      function hideSendModal() {
        document.getElementById('sendModal').classList.add('opacity-0');
        document.getElementById('sendModal').classList.remove('opacity-100');
        document.getElementById('sendModal').classList.add('pointer-events-none');
        setTimeout(() => {
          document.getElementById('sendModal').classList.add('hidden');
          document.body.classList.remove('modal-active');
        }, 300);
        sendCampaignId = null;
      }

      // Send campaign
      async function sendCampaign() {
        if (!sendCampaignId) return;

        try {
          await axios.post('/campaigns/' + sendCampaignId + '/send');
          hideSendModal();
          showToast('Campaign sending started successfully');
          loadCampaigns(currentPage);
        } catch (error) {
          hideSendModal();
          handleApiError(error);
        }
      }

      // Document ready
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial load
        loadCampaigns(currentPage);

        // Pagination
        document.getElementById('prevPageBtn').addEventListener('click', () => {
          if (currentPage > 1) {
            currentPage--;
            loadCampaigns(currentPage);
          }
        });

        document.getElementById('nextPageBtn').addEventListener('click', () => {
          const totalPages = parseInt(document.getElementById('totalPages').textContent);
          if (currentPage < totalPages) {
            currentPage++;
            loadCampaigns(currentPage);
          }
        });

        // Search and filter
        document.getElementById('applyFilterBtn').addEventListener('click', () => {
          searchTerm = document.getElementById('searchInput').value.trim();
          statusFilter = document.getElementById('statusFilter').value;
          currentPage = 1;
          loadCampaigns(currentPage);
        });

        // Create new campaign
        document.getElementById('createCampaignBtn').addEventListener('click', () => {
          window.location.href = '/admin/campaigns/create';
        });

        // Back to home
        document.getElementById('backToHomeBtn').addEventListener('click', () => {
          window.location.href = '/';
        });
      });
    </script>
  `
}) %>
