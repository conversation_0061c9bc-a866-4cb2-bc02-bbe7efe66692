<%- include('../../partials/layout', {
  title: 'Create Campaign',
  currentPath: '/admin/campaigns/create',
  body: `
    <div class="container mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Create New Campaign</h1>
        <div>
          <button id="backToCampaignsBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Campaigns
          </button>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Campaign Form -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-xl font-semibold mb-4">Campaign Details</h2>
          
          <form id="campaignForm">
            <div class="mb-4">
              <label for="campaignName" class="block text-sm font-medium text-gray-700 mb-2">Campaign Name</label>
              <input
                type="text"
                id="campaignName"
                name="name"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Enter campaign name"
              />
            </div>

            <div class="mb-4">
              <label for="emailSubject" class="block text-sm font-medium text-gray-700 mb-2">Email Subject</label>
              <input
                type="text"
                id="emailSubject"
                name="subject"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Enter email subject"
              />
            </div>

            <div class="mb-4">
              <label for="targetAudience" class="block text-sm font-medium text-gray-700 mb-2">Target Audience</label>
              <select
                id="targetAudience"
                name="target_audience"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="exclude_trial">All Users</option>
                <option value="active">Active Users Only</option>
                <option value="inactive">Inactive Users Only</option>
              </select>
            </div>

            <div class="mb-4">
              <label for="scheduledAt" class="block text-sm font-medium text-gray-700 mb-2">Schedule Send (Optional)</label>
              <input
                type="datetime-local"
                id="scheduledAt"
                name="scheduled_at"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <p class="text-sm text-gray-500 mt-1">Leave empty to save as draft</p>
            </div>

            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-2">Email Content</label>

              <!-- Editor Mode Toggle -->
              <div class="mb-3">
                <div class="flex space-x-2">
                  <button type="button" id="richEditorBtn" class="px-3 py-1 text-xs bg-blue-500 text-white rounded">Rich Editor</button>
                  <button type="button" id="htmlEditorBtn" class="px-3 py-1 text-xs bg-gray-300 text-gray-700 rounded">HTML Code</button>
                </div>
              </div>

              <!-- Rich Text Editor -->
              <div id="richEditorContainer" class="border border-gray-300 rounded-md">
                <div class="bg-gray-50 px-3 py-2 border-b border-gray-300">
                  <div class="flex flex-wrap gap-2">
                    <button type="button" onclick="insertVariable('{{user_email}}')" class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">User Email</button>
                    <button type="button" onclick="insertVariable('{{unsubscribe_url}}')" class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Unsubscribe</button>
                    <button type="button" onclick="loadTemplate('welcome')" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Welcome Template</button>
                    <button type="button" onclick="loadTemplate('newsletter')" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Newsletter Template</button>
                    <button type="button" id="insertImageBtn" class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">Insert Image</button>
                  </div>
                </div>
                <div id="quillEditor" style="height: 400px;"></div>
              </div>

              <!-- HTML Code Editor -->
              <div id="htmlEditorContainer" class="border border-gray-300 rounded-md hidden">
                <div class="bg-gray-50 px-3 py-2 border-b border-gray-300">
                  <div class="flex flex-wrap gap-2">
                    <button type="button" onclick="insertVariable('{{user_email}}')" class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">User Email</button>
                    <button type="button" onclick="insertVariable('{{unsubscribe_url}}')" class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Unsubscribe</button>
                    <button type="button" onclick="loadTemplate('welcome')" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Welcome Template</button>
                    <button type="button" onclick="loadTemplate('newsletter')" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Newsletter Template</button>
                  </div>
                </div>
                <textarea
                  id="htmlTemplate"
                  name="html_template"
                  required
                  rows="15"
                  class="w-full px-3 py-2 border-0 focus:outline-none focus:ring-0 resize-none"
                  placeholder="Enter your HTML email template here..."
                ></textarea>
              </div>

              <!-- Hidden file input for image upload -->
              <input type="file" id="imageUpload" accept="image/*" style="display: none;">
            </div>

            <div class="flex space-x-4">
              <button
                type="submit"
                class="flex-1 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Save Campaign
              </button>
              <button
                type="button"
                id="previewBtn"
                class="flex-1 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
              >
                Preview Email
              </button>
            </div>
          </form>
        </div>

        <!-- Email Preview -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-xl font-semibold mb-4">Email Preview</h2>

          <!-- Preview Mode Toggle -->
          <div class="mb-3">
            <div class="flex space-x-2">
              <button type="button" id="desktopPreviewBtn" class="px-3 py-1 text-xs bg-blue-500 text-white rounded">Desktop</button>
              <button type="button" id="mobilePreviewBtn" class="px-3 py-1 text-xs bg-gray-300 text-gray-700 rounded">Mobile</button>
            </div>
          </div>

          <div class="border border-gray-300 rounded-md">
            <div class="bg-gray-50 px-4 py-2 border-b border-gray-300">
              <div class="text-sm">
                <strong>From:</strong> Kitsify &lt;<EMAIL>&gt;<br>
                <strong>To:</strong> <EMAIL><br>
                <strong>Subject:</strong> <span id="previewSubject">Your email subject will appear here</span>
              </div>
            </div>
            <div class="p-4 bg-gray-100">
              <div id="emailPreview" class="bg-white min-h-96 mx-auto transition-all duration-300" style="max-width: 600px; width: 100%;">
                <div class="p-4">
                  <p class="text-gray-500 italic">Email preview will appear here when you add content</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  additionalScripts: `
    <!-- Quill.js CDN -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

    <!-- Custom CSS for email editor -->
    <style>
      /* Ensure images in Quill editor are properly sized */
      .ql-editor img {
        max-width: 100% !important;
        height: auto !important;
        display: block;
        margin: 10px 0;
      }

      /* Email preview styling */
      #emailPreview img {
        max-width: 100% !important;
        height: auto !important;
        display: block;
        margin: 10px 0;
      }

      /* Loading indicator */
      .image-loading {
        opacity: 0.5;
        pointer-events: none;
      }
    </style>

    <script>
      // Global variables
      let quill;
      let currentEditorMode = 'rich'; // 'rich' or 'html'

      // Email templates
      const templates = {
        welcome: '<!DOCTYPE html><html><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Welcome to Kitsify</title><style>body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }.main-container { max-width: 600px; margin: 0 auto; padding: 20px; }.header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }.content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }.button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }.footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px; }</style></head><body><div class="main-container"><div class="header"><h1>Welcome to Kitsify!</h1></div><div class="content"><h2>Hello there!</h2><p>Welcome to Kitsify! We are excited to have you on board.</p><p>Your email: {{user_email}}</p><a href="https://kitsify.com/tools" class="button">Get Started</a><p>If you have any questions, feel free to reach out to our support team.</p></div><div class="footer"><p>This email was sent to {{user_email}}</p><p><a href="{{unsubscribe_url}}">Unsubscribe</a> | © 2024 Kitsify. All rights reserved.</p></div></div></body></html>',
        newsletter: '<!DOCTYPE html><html><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Kitsify Newsletter</title><style>body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }.main-container { max-width: 600px; margin: 0 auto; padding: 20px; }.header { background: #2c3e50; color: white; padding: 20px; text-align: center; }.content { background: white; padding: 30px; }.section { margin-bottom: 30px; }.button { display: inline-block; background: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; }.footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px; }</style></head><body><div class="main-container"><div class="header"><h1>Kitsify Newsletter</h1></div><div class="content"><div class="section"><h2>What is New This Week</h2><p>Here are the latest updates and features we have added to Kitsify...</p></div><div class="section"><h2>Featured Tools</h2><p>Check out these amazing tools that can boost your productivity...</p><a href="https://kitsify.com/tools" class="button">Explore Tools</a></div></div><div class="footer"><p>This email was sent to {{user_email}}</p><p><a href="{{unsubscribe_url}}">Unsubscribe</a> | © 2024 Kitsify. All rights reserved.</p></div></div></body></html>'
      };

      // Initialize Quill editor
      function initializeQuillEditor() {
        const toolbarOptions = [
          ['bold', 'italic', 'underline', 'strike'],
          ['blockquote', 'code-block'],
          [{ 'header': 1 }, { 'header': 2 }],
          [{ 'list': 'ordered'}, { 'list': 'bullet' }],
          [{ 'script': 'sub'}, { 'script': 'super' }],
          [{ 'indent': '-1'}, { 'indent': '+1' }],
          [{ 'direction': 'rtl' }],
          [{ 'size': ['small', false, 'large', 'huge'] }],
          [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
          [{ 'color': [] }, { 'background': [] }],
          [{ 'font': [] }],
          [{ 'align': [] }],
          ['link'],
          ['clean']
        ];

        quill = new Quill('#quillEditor', {
          theme: 'snow',
          modules: {
            toolbar: toolbarOptions
          }
        });

        // Listen for text changes
        quill.on('text-change', function() {
          updatePreview();
        });

        // Custom image handler to ensure proper image insertion
        const toolbar = quill.getModule('toolbar');
        if (toolbar) {
          // Remove default image handler since we'll use custom button
          const toolbarContainer = quill.container.previousSibling;
          const imageButton = toolbarContainer.querySelector('.ql-image');
          if (imageButton) {
            imageButton.style.display = 'none';
          }
        }
      }

      function insertVariable(variable) {
        if (currentEditorMode === 'rich' && quill) {
          const range = quill.getSelection();
          if (range) {
            quill.insertText(range.index, variable);
          } else {
            quill.insertText(quill.getLength(), variable);
          }
        } else {
          const textarea = document.getElementById('htmlTemplate');
          const start = textarea.selectionStart;
          const end = textarea.selectionEnd;
          const text = textarea.value;

          textarea.value = text.substring(0, start) + variable + text.substring(end);
          textarea.focus();
          textarea.setSelectionRange(start + variable.length, start + variable.length);
        }
        updatePreview();
      }

      function loadTemplate(templateName) {
        if (templates[templateName]) {
          if (currentEditorMode === 'rich' && quill) {
            // Convert HTML to Delta format for Quill
            quill.clipboard.dangerouslyPasteHTML(templates[templateName]);
          } else {
            document.getElementById('htmlTemplate').value = templates[templateName];
          }
          updatePreview();
        }
      }

      function updatePreview() {
        const subject = document.getElementById('emailSubject').value || 'Your email subject will appear here';
        let htmlContent = '';

        if (currentEditorMode === 'rich' && quill) {
          htmlContent = quill.root.innerHTML;
          // Update hidden textarea for form submission
          document.getElementById('htmlTemplate').value = htmlContent;
        } else {
          htmlContent = document.getElementById('htmlTemplate').value;
        }

        document.getElementById('previewSubject').textContent = subject;

        if (htmlContent.trim()) {
          // Replace variables with sample data for preview
          let previewContent = htmlContent
            .replace(/{{user_email}}/g, '<EMAIL>')
            .replace(/{{unsubscribe_url}}/g, 'https://kitsify.com/unsubscribe')
            .replace(/{{tracking_url}}/g, 'https://kitsify.com/track');

          // Ensure all images have proper styling for email compatibility
          previewContent = previewContent.replace(/<img([^>]*?)>/g, (match, attributes) => {
            // Check if style attribute already exists
            if (attributes.includes('style=')) {
              // Update existing style to ensure email compatibility
              return match.replace(/style="([^"]*?)"/g, (styleMatch, existingStyle) => {
                let newStyle = existingStyle;
                if (!newStyle.includes('max-width')) {
                  newStyle += '; max-width: 100%';
                }
                if (!newStyle.includes('height') || newStyle.includes('height: auto')) {
                  newStyle = newStyle.replace(/height:\s*[^;]+;?/g, '');
                  newStyle += '; height: auto';
                }
                if (!newStyle.includes('display')) {
                  newStyle += '; display: block';
                }
                return 'style="' + newStyle.replace(/^;\s*/, '') + '"';
              });
            } else {
              // Add style attribute if it doesn't exist
              return '<img' + attributes + ' style="max-width: 100%; height: auto; display: block;">';
            }
          });

          // Wrap content in email container with max-width
          previewContent = '<div style="max-width: 800px; margin: 0 auto; font-family: Arial, sans-serif; padding: 20px;">' + previewContent + '</div>';

          document.getElementById('emailPreview').innerHTML = previewContent;

          // Ensure images load properly in preview
          const previewImages = document.getElementById('emailPreview').querySelectorAll('img');
          previewImages.forEach(img => {
            img.onerror = function() {
              console.error('Failed to load image:', this.src);
              this.style.border = '2px dashed #ccc';
              this.style.padding = '10px';
              this.alt = 'Image failed to load: ' + this.src;
            };
          });
        } else {
          document.getElementById('emailPreview').innerHTML = '<div class="p-4"><p class="text-gray-500 italic">Email preview will appear here when you add content</p></div>';
        }
      }

      // Editor mode switching
      function switchToRichEditor() {
        currentEditorMode = 'rich';
        document.getElementById('richEditorContainer').classList.remove('hidden');
        document.getElementById('htmlEditorContainer').classList.add('hidden');
        document.getElementById('richEditorBtn').classList.add('bg-blue-500', 'text-white');
        document.getElementById('richEditorBtn').classList.remove('bg-gray-300', 'text-gray-700');
        document.getElementById('htmlEditorBtn').classList.add('bg-gray-300', 'text-gray-700');
        document.getElementById('htmlEditorBtn').classList.remove('bg-blue-500', 'text-white');

        // Sync content from HTML to Quill
        const htmlContent = document.getElementById('htmlTemplate').value;
        if (htmlContent && quill) {
          quill.clipboard.dangerouslyPasteHTML(htmlContent);
        }
      }

      function switchToHtmlEditor() {
        currentEditorMode = 'html';
        document.getElementById('richEditorContainer').classList.add('hidden');
        document.getElementById('htmlEditorContainer').classList.remove('hidden');
        document.getElementById('htmlEditorBtn').classList.add('bg-blue-500', 'text-white');
        document.getElementById('htmlEditorBtn').classList.remove('bg-gray-300', 'text-gray-700');
        document.getElementById('richEditorBtn').classList.add('bg-gray-300', 'text-gray-700');
        document.getElementById('richEditorBtn').classList.remove('bg-blue-500', 'text-white');

        // Sync content from Quill to HTML
        if (quill) {
          document.getElementById('htmlTemplate').value = quill.root.innerHTML;
        }
      }

      // Preview mode switching
      function switchToDesktopPreview() {
        document.getElementById('emailPreview').style.maxWidth = '600px';
        document.getElementById('emailPreview').style.width = '100%';
        document.getElementById('desktopPreviewBtn').classList.add('bg-blue-500', 'text-white');
        document.getElementById('desktopPreviewBtn').classList.remove('bg-gray-300', 'text-gray-700');
        document.getElementById('mobilePreviewBtn').classList.add('bg-gray-300', 'text-gray-700');
        document.getElementById('mobilePreviewBtn').classList.remove('bg-blue-500', 'text-white');
      }

      function switchToMobilePreview() {
        document.getElementById('emailPreview').style.maxWidth = '375px';
        document.getElementById('emailPreview').style.width = '375px';
        document.getElementById('mobilePreviewBtn').classList.add('bg-blue-500', 'text-white');
        document.getElementById('mobilePreviewBtn').classList.remove('bg-gray-300', 'text-gray-700');
        document.getElementById('desktopPreviewBtn').classList.add('bg-gray-300', 'text-gray-700');
        document.getElementById('desktopPreviewBtn').classList.remove('bg-blue-500', 'text-white');
      }

      // Image upload functionality
      async function uploadImage(file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
          showToast('Please select a valid image file (JPG, PNG, GIF, WEBP)', 'error');
          return null;
        }

        // Validate file size (5MB limit)
        if (file.size > 5 * 1024 * 1024) {
          showToast('Image size must be less than 5MB', 'error');
          return null;
        }

        const formData = new FormData();
        formData.append('image', file);

        try {
          const response = await axios.post('/upload/image', formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            timeout: 30000, // 30 second timeout
            withCredentials: true // Ensure cookies are sent
          });

          if (response.data && response.data.success && response.data.imageUrl) {
            console.log('Image uploaded successfully:', response.data.imageUrl);
            return response.data.imageUrl;
          } else {
            throw new Error('Invalid response from server');
          }
        } catch (error) {
          console.error('Image upload failed:', error);
          let errorMessage = 'Failed to upload image';

          if (error.response) {
            errorMessage = error.response.data?.message || errorMessage;
          } else if (error.request) {
            errorMessage = 'Network error - please check your connection';
          }

          showToast(errorMessage, 'error');
          return null;
        }
      }

      // Form submission
      document.getElementById('campaignForm').addEventListener('submit', async (e) => {
        e.preventDefault();

        // Ensure HTML template is updated from Quill editor
        if (currentEditorMode === 'rich' && quill) {
          document.getElementById('htmlTemplate').value = quill.root.innerHTML;
        }

        const formData = new FormData(e.target);
        const campaignData = {
          name: formData.get('name'),
          subject: formData.get('subject'),
          html_template: formData.get('html_template'),
          target_audience: formData.get('target_audience'),
        };

        if (formData.get('scheduled_at')) {
          campaignData.scheduled_at = formData.get('scheduled_at');
        }

        try {
          const response = await axios.post('/campaigns', campaignData);
          showToast('Campaign created successfully');
          window.location.href = '/admin/campaigns';
        } catch (error) {
          handleApiError(error);
        }
      });

      // Event handlers
      document.addEventListener('DOMContentLoaded', () => {
        // Initialize Quill editor
        initializeQuillEditor();

        // Editor mode toggle buttons
        document.getElementById('richEditorBtn').addEventListener('click', switchToRichEditor);
        document.getElementById('htmlEditorBtn').addEventListener('click', switchToHtmlEditor);

        // Preview mode toggle buttons
        document.getElementById('desktopPreviewBtn').addEventListener('click', switchToDesktopPreview);
        document.getElementById('mobilePreviewBtn').addEventListener('click', switchToMobilePreview);

        // Image upload button
        document.getElementById('insertImageBtn').addEventListener('click', () => {
          document.getElementById('imageUpload').click();
        });

        // Image file input change
        document.getElementById('imageUpload').addEventListener('change', async (e) => {
          const file = e.target.files[0];
          if (file) {
            // Show loading indicator
            showToast('Uploading image...', 'info');

            const imageUrl = await uploadImage(file);
            if (imageUrl) {
              if (currentEditorMode === 'rich' && quill) {
                const range = quill.getSelection() || { index: quill.getLength() };
                // Insert image with proper styling for email compatibility
                quill.insertEmbed(range.index, 'image', imageUrl);

                // Add styling to the inserted image
                setTimeout(() => {
                  const images = quill.container.querySelectorAll('img');
                  const lastImage = images[images.length - 1];
                  if (lastImage && lastImage.src === imageUrl) {
                    lastImage.style.maxWidth = '100%';
                    lastImage.style.height = 'auto';
                    lastImage.style.display = 'block';
                    lastImage.alt = 'Email image';
                  }
                }, 100);

              } else if (currentEditorMode === 'html') {
                const textarea = document.getElementById('htmlTemplate');
                const imgTag = '<img src="' + imageUrl + '" alt="Email image" style="max-width: 100%; height: auto; display: block;">';
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                const text = textarea.value;
                textarea.value = text.substring(0, start) + imgTag + text.substring(end);
                textarea.focus();
              }

              showToast('Image uploaded successfully!', 'success');
              updatePreview();
            }

            // Clear the file input
            e.target.value = '';
          }
        });

        // Preview button
        document.getElementById('previewBtn').addEventListener('click', updatePreview);

        // Auto-update preview when subject changes
        document.getElementById('emailSubject').addEventListener('input', updatePreview);
        document.getElementById('htmlTemplate').addEventListener('input', updatePreview);

        // Back button
        document.getElementById('backToCampaignsBtn').addEventListener('click', () => {
          window.location.href = '/admin/campaigns';
        });

        // Initialize preview
        updatePreview();
      });
    </script>
  `
}) %>
