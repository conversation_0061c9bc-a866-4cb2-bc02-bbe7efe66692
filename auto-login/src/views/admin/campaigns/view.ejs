<%- include('../../partials/layout', {
  title: 'Campaign Details',
  currentPath: '/admin/campaigns/' + id,
  body: `
    <div class="container mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Campaign Details</h1>
        <div class="space-x-2">
          <button id="sendCampaignBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded hidden">
            Send Campaign
          </button>
          <button id="backToCampaignsBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Campaigns
          </button>
        </div>
      </div>

      <!-- Campaign Info -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div class="lg:col-span-2">
          <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Campaign Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Campaign Name</label>
                <p id="campaignName" class="mt-1 text-sm text-gray-900">Loading...</p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Status</label>
                <p id="campaignStatus" class="mt-1">Loading...</p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Target Audience</label>
                <p id="targetAudience" class="mt-1 text-sm text-gray-900">Loading...</p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Created By</label>
                <p id="createdBy" class="mt-1 text-sm text-gray-900">Loading...</p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Created At</label>
                <p id="createdAt" class="mt-1 text-sm text-gray-900">Loading...</p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Scheduled At</label>
                <p id="scheduledAt" class="mt-1 text-sm text-gray-900">Loading...</p>
              </div>
            </div>

            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700">Email Subject</label>
              <p id="emailSubject" class="mt-1 text-sm text-gray-900">Loading...</p>
            </div>
          </div>
        </div>

        <!-- Campaign Stats -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-xl font-semibold mb-4">Campaign Statistics</h2>
          
          <div class="space-y-4">
            <div class="flex justify-between">
              <span class="text-sm font-medium text-gray-700">Total Recipients</span>
              <span id="totalRecipients" class="text-sm text-gray-900">0</span>
            </div>
            
            <div class="flex justify-between">
              <span class="text-sm font-medium text-gray-700">Sent</span>
              <span id="sentCount" class="text-sm text-green-600">0</span>
            </div>
            
            <div class="flex justify-between">
              <span class="text-sm font-medium text-gray-700">Failed</span>
              <span id="failedCount" class="text-sm text-red-600">0</span>
            </div>
            
            <div class="flex justify-between">
              <span class="text-sm font-medium text-gray-700">Delivery Rate</span>
              <span id="deliveryRate" class="text-sm text-gray-900">0%</span>
            </div>
            
            <div class="flex justify-between">
              <span class="text-sm font-medium text-gray-700">Open Rate</span>
              <span id="openRate" class="text-sm text-gray-900">0%</span>
            </div>
          </div>

          <!-- Progress Bar -->
          <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-600 mb-1">
              <span>Progress</span>
              <span id="progressText">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div id="progressBar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Email Template Preview -->
      <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-semibold mb-4">Email Template</h2>
        
        <div class="border border-gray-300 rounded-md">
          <div class="bg-gray-50 px-4 py-2 border-b border-gray-300">
            <div class="text-sm">
              <strong>From:</strong> Kitsify &lt;<EMAIL>&gt;<br>
              <strong>Subject:</strong> <span id="previewSubject">Loading...</span>
            </div>
          </div>
          <div id="emailPreview" class="p-4 min-h-96 bg-white">
            <p class="text-gray-500 italic">Loading email template...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Send Confirmation Modal -->
    <div id="sendModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center z-50 hidden">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Confirm Send</p>
            <div class="modal-close cursor-pointer z-50" onclick="hideSendModal()">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <p>Are you sure you want to send this campaign? This action cannot be undone.</p>
          <p class="text-sm text-gray-600 mt-2">Recipients: <span id="modalRecipientCount">0</span></p>

          <div class="flex justify-end pt-2">
            <button class="modal-close px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2" onclick="hideSendModal()">Cancel</button>
            <button class="px-4 bg-green-500 p-3 rounded-lg text-white hover:bg-green-600" onclick="sendCampaign()">Send Campaign</button>
          </div>
        </div>
      </div>
    </div>
  `,
  additionalScripts: `
    <script>
      const campaignId = '` + id + `';
      let campaign = null;

      async function loadCampaign() {
        try {
          const response = await axios.get('/campaigns/' + campaignId);
          campaign = response.data;
          
          renderCampaignDetails(campaign);
          loadCampaignStats();
        } catch (error) {
          handleApiError(error);
        }
      }

      async function loadCampaignStats() {
        try {
          const response = await axios.get('/campaigns/' + campaignId + '/stats');
          const stats = response.data;
          
          renderCampaignStats(stats);
        } catch (error) {
          console.error('Failed to load campaign stats:', error);
        }
      }

      function renderCampaignDetails(campaign) {
        document.getElementById('campaignName').textContent = campaign.name;
        document.getElementById('campaignStatus').innerHTML = getStatusBadge(campaign.status);
        document.getElementById('targetAudience').textContent = formatTargetAudience(campaign.target_audience);
        document.getElementById('createdBy').textContent = campaign.creator ? campaign.creator.email : 'Unknown';
        document.getElementById('createdAt').textContent = new Date(campaign.created_at).toLocaleString();
        document.getElementById('scheduledAt').textContent = campaign.scheduled_at ? 
          new Date(campaign.scheduled_at).toLocaleString() : 'Not scheduled';
        document.getElementById('emailSubject').textContent = campaign.subject;
        document.getElementById('previewSubject').textContent = campaign.subject;

        // Show send button if campaign can be sent
        if (campaign.status === 'draft' || campaign.status === 'scheduled') {
          document.getElementById('sendCampaignBtn').classList.remove('hidden');
        }

        // Render email template
        renderEmailTemplate(campaign.html_template);
      }

      function renderCampaignStats(stats) {
        document.getElementById('totalRecipients').textContent = stats.total_recipients;
        document.getElementById('sentCount').textContent = stats.sent_count;
        document.getElementById('failedCount').textContent = stats.failed_count;
        document.getElementById('deliveryRate').textContent = stats.delivery_rate.toFixed(1) + '%';
        document.getElementById('openRate').textContent = stats.open_rate.toFixed(1) + '%';

        // Update progress bar
        const progress = stats.total_recipients > 0 ? 
          ((stats.sent_count + stats.failed_count) / stats.total_recipients) * 100 : 0;
        document.getElementById('progressText').textContent = progress.toFixed(1) + '%';
        document.getElementById('progressBar').style.width = progress + '%';
      }

      function renderEmailTemplate(htmlTemplate) {
        // Replace variables with sample data for preview
        let previewContent = htmlTemplate
          .replace(/{{user_email}}/g, '<EMAIL>')
          .replace(/{{unsubscribe_url}}/g, 'https://kitsify.com/unsubscribe')
          .replace(/{{tracking_url}}/g, 'https://kitsify.com/track');
        
        document.getElementById('emailPreview').innerHTML = previewContent;
      }

      function getStatusBadge(status) {
        const badges = {
          'draft': '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Draft</span>',
          'scheduled': '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Scheduled</span>',
          'sending': '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Sending</span>',
          'sent': '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Sent</span>',
          'cancelled': '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Cancelled</span>',
        };
        return badges[status] || status;
      }

      function formatTargetAudience(audience) {
        const audiences = {
          'all': 'All Users',
          'active': 'Active Users',
          'inactive': 'Inactive Users',
          'trial': 'Trial Users',
          'exclude_trial': 'Exclude Trial',
          'exclude_active': 'Exclude Active',
          'exclude_inactive': 'Exclude Inactive',
        };
        return audiences[audience] || audience;
      }

      function showSendModal() {
        document.getElementById('modalRecipientCount').textContent = campaign.total_recipients || 0;
        document.getElementById('sendModal').classList.remove('hidden');
        document.getElementById('sendModal').classList.remove('opacity-0');
        document.getElementById('sendModal').classList.add('opacity-100');
        document.getElementById('sendModal').classList.add('pointer-events-auto');
        document.body.classList.add('modal-active');
      }

      function hideSendModal() {
        document.getElementById('sendModal').classList.add('opacity-0');
        document.getElementById('sendModal').classList.remove('opacity-100');
        document.getElementById('sendModal').classList.add('pointer-events-none');
        setTimeout(() => {
          document.getElementById('sendModal').classList.add('hidden');
          document.body.classList.remove('modal-active');
        }, 300);
      }

      async function sendCampaign() {
        try {
          await axios.post('/campaigns/' + campaignId + '/send');
          hideSendModal();
          showToast('Campaign sending started successfully');
          
          // Reload campaign data
          setTimeout(() => {
            loadCampaign();
          }, 1000);
        } catch (error) {
          hideSendModal();
          handleApiError(error);
        }
      }

      // Event listeners
      document.getElementById('sendCampaignBtn').addEventListener('click', showSendModal);
      document.getElementById('backToCampaignsBtn').addEventListener('click', () => {
        window.location.href = '/admin/campaigns';
      });

      // Auto-refresh stats for active campaigns
      let refreshInterval;
      function startAutoRefresh() {
        if (campaign && (campaign.status === 'sending' || campaign.status === 'scheduled')) {
          refreshInterval = setInterval(() => {
            loadCampaignStats();
            loadCampaign(); // Reload to check status changes
          }, 5000); // Refresh every 5 seconds
        }
      }

      function stopAutoRefresh() {
        if (refreshInterval) {
          clearInterval(refreshInterval);
        }
      }

      // Initialize
      document.addEventListener('DOMContentLoaded', async () => {
        await loadCampaign();
        startAutoRefresh();
      });

      // Cleanup on page unload
      window.addEventListener('beforeunload', stopAutoRefresh);
    </script>
  `
}) %>
