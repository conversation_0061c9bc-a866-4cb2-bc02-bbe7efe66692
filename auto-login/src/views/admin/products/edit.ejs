<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Edit Product</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Edit Product</h1>
        <div class="space-x-2">
          <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Product
          </button>
        </div>
      </div>

      <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="p-6">
          <div id="errorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>

          <form id="editProductForm" class="space-y-6">
            <!-- Basic Information -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Basic Information</h2>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                  <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label for="category_ids" class="block text-sm font-medium text-gray-700">Categories</label>
                  <div id="categories-container" class="mt-1 border border-gray-300 rounded-md p-2 max-h-40 overflow-y-auto">
                    <!-- Categories checkboxes will be loaded dynamically -->
                  </div>
                </div>

                <div>
                  <label for="image_url" class="block text-sm font-medium text-gray-700">Image URL</label>
                  <input 
                    type="url" 
                    id="image_url" 
                    name="image_url"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label for="image_file" class="block text-sm font-medium text-gray-700">Or Upload Image</label>
                  <input 
                    type="file" 
                    id="image_file" 
                    accept="image/*"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div id="image_preview_container" class="hidden">
                  <label class="block text-sm font-medium text-gray-700">Image Preview</label>
                  <div class="mt-1 flex items-center">
                    <img id="image_preview" src="" alt="Preview" class="h-32 w-auto object-contain" />
                    <button 
                      type="button" 
                      id="clear_image_btn"
                      class="ml-2 bg-red-500 hover:bg-red-700 text-white text-xs py-1 px-2 rounded"
                    >
                      Clear
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Description -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Description</h2>
              <div>
                <label for="description" class="block text-sm font-medium text-gray-700">Product Description (Markdown supported)</label>
                <textarea 
                  id="description" 
                  name="description" 
                  rows="10"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                ></textarea>
              </div>
              <!-- EasyMDE Markdown Editor -->
              <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/easymde/dist/easymde.min.css">
              <script src="https://cdn.jsdelivr.net/npm/easymde/dist/easymde.min.js"></script>
              <script>
                document.addEventListener('DOMContentLoaded', function() {
                  const easyMDE = new EasyMDE({
                    element: document.getElementById('description'),
                    spellChecker: false,
                    autofocus: false,
                    toolbar: ['bold', 'italic', 'heading', '|', 'quote', 'unordered-list', 'ordered-list', '|', 'link', 'image', '|', 'preview', 'side-by-side', 'fullscreen', '|', 'guide'],
                    placeholder: 'Type your markdown description here...',
                    status: ['autosave', 'lines', 'words', 'cursor']
                  });
                });
              </script>
            </div>

            <!-- Prompt Features -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Prompt Features</h2>
              <div class="space-y-4">
                <div class="flex items-center">
                  <input 
                    type="checkbox" 
                    id="has_prompt_library" 
                    name="has_prompt_library"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label for="has_prompt_library" class="ml-2 block text-sm font-medium text-gray-700">Has Prompt Library</label>
                </div>
                <div class="flex items-center">
                  <input 
                    type="checkbox" 
                    id="has_prompt_video" 
                    name="has_prompt_video"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label for="has_prompt_video" class="ml-2 block text-sm font-medium text-gray-700">Has Prompt Video</label>
                </div>
                <div class="flex items-center">
                  <input 
                    type="checkbox" 
                    id="is_best_seller" 
                    name="is_best_seller"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label for="is_best_seller" class="ml-2 block text-sm font-medium text-gray-700">Best Seller</label>
                </div>
                <div>
                  <label for="sort" class="block text-sm font-medium text-gray-700">Sort Order</label>
                  <input 
                    type="number" 
                    id="sort" 
                    name="sort"
                    value="0"
                    min="0"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <p class="mt-1 text-sm text-gray-500">Higher values will be displayed first</p>
                </div>
              </div>
            </div>

            <!-- Features -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Features</h2>
              <div class="space-y-2">
                <div id="features-container">
                  <!-- Features will be added here dynamically -->
                </div>
                <button 
                  type="button" 
                  id="addFeatureBtn"
                  class="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Add Feature
                </button>
              </div>
            </div>

            <!-- Product Durations -->
            <div>
              <div class="flex justify-between items-center mb-2">
                <h2 class="text-xl font-semibold">Additional Durations</h2>
                <button
                  type="button"
                  id="addDurationBtn"
                  class="bg-blue-500 hover:bg-blue-700 text-white text-sm py-1 px-2 rounded"
                >
                  Add Duration
                </button>
              </div>
              <div id="durationsContainer" class="space-y-3">
                <!-- Duration items will be added here dynamically -->
              </div>
            </div>

            <!-- Assigned Accounts -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold">Assigned Accounts</h2>
                <button
                  type="button"
                  id="assignAccountBtn"
                  class="bg-green-500 hover:bg-green-700 text-white text-sm py-2 px-4 rounded flex items-center space-x-2"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span>Assign Account</span>
                </button>
              </div>
              <div id="accounts-list" class="bg-white rounded-lg shadow-sm">
                <!-- Accounts will be populated here -->
              </div>
            </div>

            <!-- Payment Methods & Discounts -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h2 class="text-xl font-semibold mb-4">Payment Methods & Discounts</h2>
              <div id="payment-methods-container" class="space-y-3">
                <!-- Payment methods with discount inputs will be loaded here -->
              </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
              <button 
                type="submit"
                class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Save Changes
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Assign Account Modal -->
    <div id="assignAccountModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto w-full">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold">Assign Account</h3>
          <button id="closeAssignModal" class="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <form id="assignAccountForm">
          <div class="mb-4">
            <label for="accountSelect" class="block text-sm font-medium text-gray-700 mb-2">Select Account</label>
            <select id="accountSelect" name="accountId" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
              <option value="">Select an account</option>
              <!-- Accounts will be populated here -->
            </select>
          </div>
          <div class="flex justify-end space-x-2">
            <button type="button" id="cancelAssignBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
              Cancel
            </button>
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              Assign
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Remove Account Modal -->
    <div id="removeAccountModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-semibold mb-4">Confirm Remove</h3>
        <p class="mb-6">Are you sure you want to remove this account from the product?</p>
        <input type="hidden" id="removeAccountId">
        <div class="flex justify-end space-x-2">
          <button id="cancelRemoveAccountBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
          </button>
          <button id="confirmRemoveAccountBtn" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Remove
          </button>
        </div>
      </div>
    </div>

    <script>
      // Get product ID from server-rendered data
      const productId = `<%- id %>`; // Use the ID passed from the server
      let features = [];
      let durations = [];
      
      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }
      
      // Load categories
      async function loadCategories(selectedCategoryIds = []) {
        try {
          const response = await axios.get('/products/categories');
          const categories = response.data;
          
          // Populate checkboxes for categories
          const categoriesContainer = document.getElementById('categories-container');
          categoriesContainer.innerHTML = '';
          
          categories.forEach(category => {
            // Add checkbox for multiple selection
            const checkboxDiv = document.createElement('div');
            checkboxDiv.className = 'flex items-center mb-2';
            
            const isChecked = selectedCategoryIds.includes(category.id);
            
            checkboxDiv.innerHTML = `
              <input 
                type="checkbox" 
                id="category_${category.id}" 
                name="category_ids[]" 
                value="${category.id}"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                ${isChecked ? 'checked' : ''}
              />
              <label for="category_${category.id}" class="ml-2 block text-sm text-gray-900">
                ${category.name}
              </label>
            `;
            categoriesContainer.appendChild(checkboxDiv);
          });
        } catch (error) {
          console.error('Error loading categories:', error);
        }
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Show toast notification
      function showToast(message, type = 'success') {
        let backgroundColor;
        switch (type) {
          case 'success':
            backgroundColor = '#48bb78';
            break;
          case 'error':
            backgroundColor = '#f56565';
            break;
          case 'info':
            backgroundColor = '#4299e1';
            break;
          default:
            backgroundColor = '#48bb78';
        }

        Toastify({
          text: message,
          duration: 3000,
          close: true,
          gravity: "top",
          position: "right",
          backgroundColor,
          stopOnFocus: true
        }).showToast();
      }

      // Function to handle image selection and convert to base64
      function handleImageSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Check file size (limit to 1MB)
        if (file.size > 1024 * 1024) {
          showToast('Image size should be less than 1MB', 'error');
          event.target.value = '';
          return;
        }

        // Check file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
          showToast('Please select a valid image file (JPG, PNG, GIF, WEBP)', 'error');
          event.target.value = '';
          return;
        }

        // Create FormData for file upload
        const formData = new FormData();
        formData.append('image', file);

        // Upload to server
        uploadImageToServer(formData, file.name);
      }

      // Function to upload image to server
      async function uploadImageToServer(formData, originalName) {
        try {
          const response = await axios.post('/upload/image', formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            timeout: 30000, // 30 second timeout
            withCredentials: true // Ensure cookies are sent
          });

          if (response.data && response.data.success && response.data.imageUrl) {
            console.log('Image uploaded successfully:', response.data.imageUrl);
            
            // Set the image URL in the input field
            document.getElementById('image_url').value = response.data.imageUrl;
            document.getElementById('image_preview').src = response.data.imageUrl;
            document.getElementById('image_preview_container').classList.remove('hidden');
            
            showToast('Image uploaded successfully!', 'success');
          } else {
            throw new Error('Invalid response from server');
          }
        } catch (error) {
          console.error('Image upload failed:', error);
          let errorMessage = 'Failed to upload image';

          if (error.response) {
            errorMessage = error.response.data?.message || errorMessage;
          } else if (error.request) {
            errorMessage = 'Network error - please check your connection';
          }

          showToast(errorMessage, 'error');
          
          // Clear the file input on error
          document.getElementById('image_file').value = '';
        }
      }

      // Function to handle URL input change
      function handleImageUrlChange(event) {
        const url = event.target.value.trim();
        const preview = document.getElementById('image_preview');
        const container = document.getElementById('image_preview_container');
        
        if (url && !url.startsWith('data:')) { // Don't try to load base64 as external URL
          // Test if URL is valid and image loads
          const img = new Image();
          img.onload = function() {
            preview.src = url;
            container.classList.remove('hidden');
          };
          img.onerror = function() {
            container.classList.add('hidden');
            showToast('Invalid image URL or image failed to load', 'error');
          };
          img.src = url;
        } else if (url.startsWith('data:')) {
          // If it's base64 data, just show it
          preview.src = url;
          container.classList.remove('hidden');
        } else {
          container.classList.add('hidden');
        }
      }

      // Function to clear image
      function clearImage() {
        document.getElementById('image_url').value = '';
        document.getElementById('image_file').value = '';
        document.getElementById('image_preview').src = '';
        document.getElementById('image_preview_container').classList.add('hidden');
        showToast('Image removed', 'success');
      }

      // Show error message
      function showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        showToast(message, 'error');
      }

      // Fetch product details
      async function fetchProduct(id) {
        if (!setupAxios()) return;

        try {
          const response = await axios.get(`/products/${id}`);
          return response.data;
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to fetch product details. Please try again.';
            showError(message);
          }
          return null;
        }
      }

      // Populate form with product data
      async function populateForm(product) {
        document.getElementById('name').value = product.name || '';
        document.getElementById('image_url').value = product.image_url || '';
        document.getElementById('description').value = product.description || '';
        
        // Set prompt feature checkboxes
        document.getElementById('has_prompt_library').checked = product.has_prompt_library || false;
        document.getElementById('has_prompt_video').checked = product.has_prompt_video || false;
        document.getElementById('is_best_seller').checked = product.is_best_seller || false;
        document.getElementById('sort').value = product.sort || 0;

        // Extract category IDs from product.categories if available
        const categoryIds = [];
        if (product.categories && Array.isArray(product.categories)) {
          product.categories.forEach(category => {
            if (category && category.id) {
              categoryIds.push(category.id);
            }
          });
        }
        
        await loadCategories(categoryIds);

        // Set features
        features = product.features || [];
        renderFeatures();

        // Populate durations
        if (product.durations && product.durations.length > 0) {
          document.getElementById('durationsContainer').innerHTML = '';
          const sortedDurations = [...product.durations].sort((a, b) => a.duration_days - b.duration_days);
          sortedDurations.forEach(duration => {
            addDurationItem(
              duration.id,
              duration.duration_days,
              duration.original_price,
              duration.discount_percent || 0,
              duration.quantity || 0
            );
          });
        }

        // Show image preview if image URL exists
        if (product.image_url) {
          document.getElementById('image_preview').src = product.image_url;
          document.getElementById('image_preview_container').classList.remove('hidden');
        }

        // Load assigned accounts
        loadProductAccounts(productId);
        
        // Load payment methods and product discounts
        loadPaymentMethods(productId);
      }

      // Render features inputs
      function renderFeatures() {
        const container = document.getElementById('features-container');
        container.innerHTML = '';

        features.forEach((feature, index) => {
          const featureRow = document.createElement('div');
          featureRow.className = 'flex items-center space-x-2';
          
          featureRow.innerHTML = `
            <input 
              type="text" 
              name="features[]" 
              value="${feature}" 
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
            <button 
              type="button" 
              class="remove-feature text-red-600 hover:text-red-800" 
              data-index="${index}"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </button>
          `;
          
          container.appendChild(featureRow);
        });

        // Add event listeners for remove buttons
        document.querySelectorAll('.remove-feature').forEach(button => {
          button.addEventListener('click', function() {
            const index = parseInt(this.getAttribute('data-index'));
            features.splice(index, 1);
            renderFeatures();
          });
        });
        
        // Add event listeners for input changes to update the features array
        document.querySelectorAll('input[name="features[]"]').forEach((input, index) => {
          input.addEventListener('input', function() {
            features[index] = this.value;
          });
        });
      }

      // Add a new feature input
      function addFeature() {
        // Save current feature values before adding a new one
        const featureInputs = document.querySelectorAll('input[name="features[]"]');
        features = Array.from(featureInputs).map(input => input.value);
        
        // Add new empty feature
        features.push('');
        renderFeatures();
        
        // Focus the last input
        const inputs = document.querySelectorAll('input[name="features[]"]');
        if (inputs.length > 0) {
          inputs[inputs.length - 1].focus();
        }
      }

      // Function to update discount percent when discount price changes
      function updateDiscountPercent(input) {
        const durationItem = input.closest('.duration-item');
        const priceInput = durationItem.querySelector('.duration-price');
        const discountPriceInput = durationItem.querySelector('.discount-price');
        const discountPercentSpan = durationItem.querySelector('.discount-percent');

        const originalPrice = parseFloat(priceInput.value);
        const discountPrice = parseFloat(discountPriceInput.value);

        if (!isNaN(originalPrice) && !isNaN(discountPrice) && discountPrice < originalPrice && discountPrice > 0) {
          const discountPercent = ((originalPrice - discountPrice) / originalPrice) * 100;
          discountPercentSpan.textContent = ` (${discountPercent.toFixed(2)}%)`;
          // Store discount percent as a data attribute for form submission
          discountPriceInput.dataset.discountPercent = discountPercent.toFixed(2);
        } else {
          discountPercentSpan.textContent = '';
          discountPriceInput.dataset.discountPercent = '0';
        }
      }
      
      // Function to update discount price when original price changes
      function updateDiscountPriceOnOriginalPriceChange(input) {
        const durationItem = input.closest('.duration-item');
        const discountPriceInput = durationItem.querySelector('.discount-price');
        
        // If discount price has a value, recalculate the discount percent
        if (discountPriceInput.value) {
          updateDiscountPercent(discountPriceInput);
        }
      }

      // Function to add a new duration item
      function addDurationItem(id = '', durationDays = '', price = '', discountPercent = '', quantity = '') {
        const durationsContainer = document.getElementById('durationsContainer');
        const durationIndex = document.querySelectorAll('.duration-item').length;

        const durationItem = document.createElement('div');
        durationItem.className = 'duration-item flex flex-wrap items-center space-x-2 p-3 border border-gray-300 rounded-md';

        const idInput = id ? `<input type="hidden" class="duration-id" value="${id}">` : '';
        
        // Calculate discount price from original price and discount percent
        let discountPrice = '';
        if (price && discountPercent && !isNaN(parseFloat(price)) && !isNaN(parseFloat(discountPercent))) {
          discountPrice = (parseFloat(price) * (1 - parseFloat(discountPercent) / 100)).toFixed(2);
        }

        durationItem.innerHTML = `
          ${idInput}
          <div class="flex-1 min-w-[150px] mb-2">
            <label class="block text-xs text-gray-500">Duration (Days)</label>
            <input
              type="number"
              class="duration-days w-full px-2 py-1 border border-gray-300 rounded"
              min="1"
              value="${durationDays}"
              placeholder="30"
              required
            />
          </div>
          <div class="flex-1 min-w-[150px] mb-2">
            <label class="block text-xs text-gray-500">Original Price ($)</label>
            <input
              type="number"
              class="duration-price w-full px-2 py-1 border border-gray-300 rounded"
              min="0"
              step="0.01"
              value="${price}"
              placeholder="9.99"
              required
              oninput="updateDiscountPriceOnOriginalPriceChange(this)"
            />
          </div>
          <div class="flex-1 min-w-[150px] mb-2">
            <label class="block text-xs text-gray-500">Discount Price ($) <span class="discount-percent text-green-600"></span></label>
            <input
              type="number"
              class="discount-price w-full px-2 py-1 border border-gray-300 rounded"
              min="0"
              step="0.01"
              value="${discountPrice}"
              placeholder="8.99"
              data-discount-percent="${discountPercent || 0}"
              oninput="updateDiscountPercent(this)"
            />
          </div>
          <div class="flex-1 min-w-[150px] mb-2">
            <label class="block text-xs text-gray-500">Quantity</label>
            <input
              type="number"
              class="duration-quantity w-full px-2 py-1 border border-gray-300 rounded"
              min="0"
              value="${quantity}"
              placeholder="100"
              required
            />
          </div>
          <button type="button" class="remove-duration-btn bg-red-500 hover:bg-red-700 text-white text-xs py-1 px-2 rounded h-fit self-end mb-2">Remove</button>
        `;

        durationsContainer.appendChild(durationItem);

        // Add event listener to the new remove button
        durationItem.querySelector('.remove-duration-btn').addEventListener('click', () => {
          durationItem.remove();
        });

        // Initial discount percent calculation
        if (discountPrice) {
          updateDiscountPercent(durationItem.querySelector('.discount-price'));
        }
      }

      // Fetch product accounts
      async function fetchProductAccounts(id) {
        if (!setupAxios()) return [];

        try {
          const response = await axios.get(`/products/${id}/accounts`);
          return response.data;
        } catch (error) {
          console.error('Error fetching product accounts:', error);
          return [];
        }
      }

      // Fetch all accounts
      async function fetchAllAccounts() {
        if (!setupAxios()) return [];

        try {
          const response = await axios.get('/accounts?limit=100');
          return response.data.data || [];
        } catch (error) {
          console.error('Error fetching accounts:', error);
          return [];
        }
      }
      
      // Get accounts that can be assigned to the product
      async function getAssignableAccounts(productId) {
        const [allAccounts, assignedAccounts] = await Promise.all([
          fetchAllAccounts(),
          fetchProductAccounts(productId)
        ]);
        
        // Get IDs of accounts already assigned to this product
        const assignedAccountIds = assignedAccounts.map(ap => ap.account.id);
        
        // Filter out accounts that are already assigned to this product
        return allAccounts.filter(account => !assignedAccountIds.includes(account.id));
      }

      // Display product accounts
      function displayProductAccounts(accounts) {
        const accountsList = document.getElementById('accounts-list');
        accountsList.innerHTML = '';
        if (accounts && accounts.length > 0) {
          // Create a table for better display
          const table = document.createElement('table');
          table.className = 'min-w-full divide-y divide-gray-200';
          table.innerHTML = `
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
            </tbody>
          `;
          
          const tableBody = table.querySelector('tbody');
          accounts.forEach(account => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50';
            row.innerHTML = `
              <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">${account.account.id}</td>
              <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${account.account.name || account.account.info || 'N/A'}</td>
              <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                <span class="px-2 py-1 text-xs font-semibold rounded-full ${
                  account.account.status === 'active' ? 'bg-green-100 text-green-800' : 
                  account.account.status === 'inactive' ? 'bg-red-100 text-red-800' : 
                  'bg-gray-100 text-gray-800'
                }">
                  ${account.account.status || 'Unknown'}
                </span>
              </td>
              <td class="px-4 py-2 whitespace-nowrap text-sm font-medium">
                <button class="bg-red-500 hover:bg-red-700 text-white text-sm py-1 px-3 rounded" 
                  onclick="showRemoveAccountModal('${account.account.id}')">Remove</button>
              </td>
            `;
            tableBody.appendChild(row);
          });
          
          accountsList.appendChild(table);
        } else {
          accountsList.innerHTML = `
            <div class="p-8 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <p class="text-sm text-gray-500">No accounts assigned to this product yet.</p>
              <p class="text-xs text-gray-400 mt-1">Click "Assign Account" to add accounts to this product.</p>
            </div>
          `;
        }
      }

      // Populate account select dropdown
      function populateAccountSelect(accounts) {
        const accountSelect = document.getElementById('accountSelect');
        accountSelect.innerHTML = '<option value="">Select an account</option>';
        
        if (accounts && accounts.length > 0) {
          accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.id;
            // Use account name as primary display, fallback to info then ID
            let optionText = account.name || account.info || `Account ${account.id}`;
            if (account.status) {
              optionText += ` (${account.status})`;
            }
            option.textContent = optionText;
            accountSelect.appendChild(option);
          });
        } else {
          const option = document.createElement('option');
          option.disabled = true;
          option.textContent = 'No available accounts to assign';
          accountSelect.appendChild(option);
        }
      }

      // Show assign account modal
      async function showAssignAccountModal() {
        // Show loading state
        const assignBtn = document.getElementById('assignAccountBtn');
        const originalText = assignBtn.textContent;
        assignBtn.textContent = 'Loading...';
        assignBtn.disabled = true;
        
        try {
          // Refresh assignable accounts when modal opens
          const assignableAccounts = await getAssignableAccounts(productId);
          populateAccountSelect(assignableAccounts);
          
          document.getElementById('assignAccountModal').classList.remove('hidden');
        } catch (error) {
          showToast('Failed to load accounts', 'error');
        } finally {
          // Restore button state
          assignBtn.textContent = originalText;
          assignBtn.disabled = false;
        }
      }

      // Hide assign account modal
      function hideAssignAccountModal() {
        document.getElementById('assignAccountModal').classList.add('hidden');
      }

      // Show remove account modal
      function showRemoveAccountModal(accountId) {
        document.getElementById('removeAccountId').value = accountId;
        document.getElementById('removeAccountModal').classList.remove('hidden');
      }

      // Hide remove account modal
      function hideRemoveAccountModal() {
        document.getElementById('removeAccountModal').classList.add('hidden');
      }

      // Assign account to product
      async function assignAccountToProduct(event) {
        event.preventDefault();
        
        const accountId = document.getElementById('accountSelect').value;
        const submitBtn = event.target.querySelector('button[type="submit"]');
        
        if (!accountId) {
          showToast('Please select an account to assign', 'error');
          return;
        }
        
        if (!setupAxios()) return;
        
        // Show loading state
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Assigning...';
        submitBtn.disabled = true;
        
        try {
          await axios.post('/products/assign-accounts', {
            product_id: parseInt(productId),
            account_ids: [parseInt(accountId)]
          });
          
          showToast('Account assigned successfully');
          hideAssignAccountModal();
          
          // Reload accounts
          await loadProductAccounts(productId);
        } catch (error) {
          const message = error.response?.data?.message || 'Failed to assign account. Please try again.';
          showToast(message, 'error');
        } finally {
          // Restore button state
          submitBtn.textContent = originalText;
          submitBtn.disabled = false;
        }
      }
      
      // Remove account from product
      async function removeAccountFromProduct() {
        const accountId = document.getElementById('removeAccountId').value;
        const removeBtn = document.getElementById('confirmRemoveAccountBtn');
        
        if (!setupAxios()) return;
        
        // Show loading state
        const originalText = removeBtn.textContent;
        removeBtn.textContent = 'Removing...';
        removeBtn.disabled = true;
        
        try {
          await axios.delete(`/products/${productId}/accounts/${accountId}`);
          
          showToast('Account removed successfully');
          hideRemoveAccountModal();
          
          // Reload accounts
          await loadProductAccounts(productId);
        } catch (error) {
          const message = error.response?.data?.message || 'Failed to remove account. Please try again.';
          showToast(message, 'error');
        } finally {
          // Restore button state
          removeBtn.textContent = originalText;
          removeBtn.disabled = false;
        }
      }
      
      // Load payment methods and product discounts
      async function loadPaymentMethods(productId) {
        if (!setupAxios()) return;

        try {
          // Load all payment methods
          const paymentMethodsResponse = await axios.get('/products/payment-methods');
          const paymentMethods = paymentMethodsResponse.data;

          // Load existing product discounts
          const productDiscountsResponse = await axios.get(`/products/${productId}/discounts`);
          const productDiscounts = productDiscountsResponse.data;

          // Create a map of existing discounts by payment method ID
          const existingDiscounts = {};
          productDiscounts.forEach(discount => {
            existingDiscounts[discount.payment_method_id] = discount.discount_percent;
          });

          // Render payment methods with discount inputs
          renderPaymentMethods(paymentMethods, existingDiscounts);
        } catch (error) {
          console.error('Error loading payment methods:', error);
        }
      }

      // Render payment methods with discount inputs
      function renderPaymentMethods(paymentMethods, existingDiscounts = {}) {
        const container = document.getElementById('payment-methods-container');
        container.innerHTML = '';

        paymentMethods.forEach(method => {
          const methodDiv = document.createElement('div');
          methodDiv.className = 'flex items-center justify-between p-3 border border-gray-300 rounded-md bg-white';
          
          const currentDiscount = existingDiscounts[method.id] || 0;
          
          methodDiv.innerHTML = `
            <div class="flex items-center space-x-3">
              <input 
                type="checkbox" 
                id="method_${method.id}" 
                name="payment_methods[]" 
                value="${method.id}"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                ${currentDiscount > 0 ? 'checked' : ''}
              />
              <label for="method_${method.id}" class="text-sm font-medium text-gray-700">
                ${method.name.charAt(0).toUpperCase() + method.name.slice(1)}
              </label>
              <span class="text-xs text-gray-500">${method.description || ''}</span>
            </div>
            <div class="flex items-center space-x-2">
              <label for="discount_${method.id}" class="text-sm text-gray-600">Discount %:</label>
              <input 
                type="number" 
                id="discount_${method.id}" 
                name="discount_${method.id}" 
                value="${currentDiscount}"
                min="0" 
                max="100" 
                step="0.01"
                class="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                placeholder="0"
              />
            </div>
          `;
          
          container.appendChild(methodDiv);

          // Add event listener to checkbox to enable/disable discount input
          const checkbox = methodDiv.querySelector(`#method_${method.id}`);
          const discountInput = methodDiv.querySelector(`#discount_${method.id}`);
          
          checkbox.addEventListener('change', function() {
            if (this.checked) {
              discountInput.disabled = false;
              if (discountInput.value === '0' || discountInput.value === '') {
                discountInput.value = '5'; // Default 5% discount
              }
            } else {
              discountInput.disabled = true;
              discountInput.value = '0';
            }
          });

          // Initial state
          if (!checkbox.checked) {
            discountInput.disabled = true;
          }
        });
      }

      // Load product accounts
      async function loadProductAccounts(productId) {
        const accounts = await fetchProductAccounts(productId);
        displayProductAccounts(accounts);
        
        // Load additional accounts for assignment
        const assignableAccounts = await getAssignableAccounts(productId);
        populateAccountSelect(assignableAccounts);
      }

      // Update product
      async function updateProduct(formData) {
        if (!setupAxios()) return;

        try {
          // Get updated features from inputs
          const featureInputs = document.querySelectorAll('input[name="features[]"]');
          const updatedFeatures = Array.from(featureInputs).map(input => input.value.trim()).filter(Boolean);

          // Get all duration items
          const durationItems = document.querySelectorAll('.duration-item');
          const updatedDurations = [];
          durationItems.forEach(item => {
            const id = item.querySelector('.duration-id')?.value || undefined;
            const duration_days = parseInt(item.querySelector('.duration-days').value, 10);
            const original_price = parseFloat(item.querySelector('.duration-price').value);
            const discount_price = parseFloat(item.querySelector('.discount-price').value) || 0;
            const discount_percent = parseFloat(item.querySelector('.discount-price').dataset.discountPercent) || 0;
            const quantity = parseInt(item.querySelector('.duration-quantity').value) || 0;

            if (!isNaN(duration_days) && !isNaN(original_price)) {
              const durationData = {
                duration_days: duration_days,
                original_price: original_price,
                discount_percent: discount_percent,
                discount_price: discount_price,
                quantity: quantity
              };
              if (id) {
                durationData.id = id;
              }
              updatedDurations.push(durationData);
            }
          });
          
          // Get selected category IDs
          const categoryIds = [];
          document.querySelectorAll('input[name="category_ids[]"]:checked').forEach(checkbox => {
            categoryIds.push(parseInt(checkbox.value));
          });

          // Get payment methods discounts
          const paymentDiscounts = [];
          document.querySelectorAll('input[name="payment_methods[]"]:checked').forEach(checkbox => {
            const methodId = parseInt(checkbox.value);
            const discountInput = document.querySelector(`input[name="discount_${methodId}"]`);
            const discountPercent = parseFloat(discountInput.value) || 0;
            
            if (discountPercent > 0) {
              paymentDiscounts.push({
                payment_method_id: methodId,
                discount_percent: discountPercent
              });
            }
          });
          
          // Create payload
          const payload = {
            name: formData.get('name'),
            description: formData.get('description'),
            features: updatedFeatures,
            durations: updatedDurations,
            has_prompt_library: formData.get('has_prompt_library') === 'on',
            has_prompt_video: formData.get('has_prompt_video') === 'on',
            is_best_seller: formData.get('is_best_seller') === 'on',
            sort: parseInt(formData.get('sort') || '0', 10),
            category_ids: categoryIds.length > 0 ? categoryIds : null
          };
          

          
          const imageUrl = formData.get('image_url');
          if (imageUrl) {
            payload.image_url = imageUrl;
          }

          await axios.patch(`/products/${productId}`, payload);
          
          // Save payment discounts
          if (paymentDiscounts.length > 0) {
            await axios.post(`/products/${productId}/discounts`, {
              discounts: paymentDiscounts
            });
          } else {
            // Clear all discounts if none selected
            await axios.post(`/products/${productId}/discounts`, {
              discounts: []
            });
          }
          
          showToast('Product updated successfully');
          
          // Redirect back to product details page after a short delay
          setTimeout(() => {
            window.location.href = `/admin/products/${productId}`;
          }, 1500);
        } catch (error) {
          if (error.response) {
            if (error.response.status === 401) {
              window.location.href = '/login';
            } else if (error.response.status === 403) {
              showError('You do not have permission to update this product. Please contact an administrator.');
            } else {
              const message = error.response?.data?.message || 'Failed to update product. Please try again.';
              showError(message);
            }
          } else {
            showError('Network error. Please check your connection and try again.');
          }
        }
      }

      // Event listeners
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial setup
        setupAxios();
        await checkAuth();

        // Fetch and populate product details
        if (productId) {
          const product = await fetchProduct(productId);
          if (product) {
            await populateForm(product);
          }
        } else {
          showError('Product ID not found in URL');
        }

        // Add feature button
        document.getElementById('addFeatureBtn').addEventListener('click', addFeature);

        // Add duration button
        document.getElementById('addDurationBtn').addEventListener('click', () => addDurationItem());

        // Image upload and URL input handlers
        document.getElementById('image_file').addEventListener('change', handleImageSelect);
        document.getElementById('image_url').addEventListener('input', handleImageUrlChange);
        document.getElementById('clear_image_btn').addEventListener('click', clearImage);

        // Account management buttons
        document.getElementById('assignAccountBtn').addEventListener('click', showAssignAccountModal);
        document.getElementById('cancelAssignBtn').addEventListener('click', hideAssignAccountModal);
        document.getElementById('closeAssignModal').addEventListener('click', hideAssignAccountModal);
        document.getElementById('assignAccountForm').addEventListener('submit', assignAccountToProduct);
        
        // Remove account modal event listeners
        document.getElementById('cancelRemoveAccountBtn').addEventListener('click', hideRemoveAccountModal);
        document.getElementById('confirmRemoveAccountBtn').addEventListener('click', removeAccountFromProduct);

        // Back button
        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = `/admin/products/${productId}`;
        });

        // Form submission
      document.getElementById('editProductForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        // Make sure all discount percentages are calculated before submission
        document.querySelectorAll('.discount-price').forEach(input => {
          if (input.value) {
            updateDiscountPercent(input);
          }
        });
        const formData = new FormData(e.target);
        await updateProduct(formData);
      });
      });
    </script>
  </body>
</html>