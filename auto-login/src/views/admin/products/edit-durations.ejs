<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin - Edit Product Durations</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css">
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900"><PERSON><PERSON><PERSON><PERSON> lý thời hạn sản phẩm</h1>
        <div class="space-x-2">
          <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Quay lại sản phẩm
          </button>
        </div>
      </div>

      <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="p-6">
          <div id="errorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>
          <div id="successMessage" class="hidden mb-4 p-4 text-green-700 bg-green-100 rounded-md"></div>

          <div class="mb-6">
            <h2 class="text-xl font-semibold mb-2" id="productName">Đang tải...</h2>
            <p class="text-gray-600" id="productDescription">Đang tải...</p>
          </div>

          <!-- Durations Section -->
          <div>
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold">Các gói thời hạn</h2>
              <button 
                id="addDurationBtn"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Thêm thời hạn mới
              </button>
            </div>

            <!-- Durations Table -->
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời hạn (ngày)</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giá gốc</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giá khuyến mãi</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">% Giảm giá</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số lượng</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                  </tr>
                </thead>
                <tbody id="durationsTableBody" class="bg-white divide-y divide-gray-200">
                  <!-- Durations will be loaded here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add/Edit Duration Modal -->
    <div id="durationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold" id="modalTitle">Thêm thời hạn mới</h3>
          <button id="closeModalBtn" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <form id="durationForm" class="space-y-4">
          <input type="hidden" id="durationId" name="durationId">
          <input type="hidden" id="productId" name="productId">

          <div>
            <label for="duration_days" class="block text-sm font-medium text-gray-700">Thời hạn (ngày) *</label>
            <input 
              type="number" 
              id="duration_days" 
              name="duration_days" 
              min="1" 
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
          </div>

          <div>
            <label for="original_price" class="block text-sm font-medium text-gray-700">Giá gốc *</label>
            <div class="mt-1 relative rounded-md shadow-sm">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span class="text-gray-500 sm:text-sm">$</span>
              </div>
              <input 
                type="number" 
                step="0.01" 
                min="0" 
                id="original_price" 
                name="original_price" 
                required
                class="pl-7 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
            </div>
          </div>

          <div>
            <label for="discount_price" class="block text-sm font-medium text-gray-700">Giá khuyến mãi</label>
            <div class="mt-1 relative rounded-md shadow-sm">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span class="text-gray-500 sm:text-sm">$</span>
              </div>
              <input 
                type="number" 
                step="0.01" 
                min="0" 
                id="discount_price" 
                name="discount_price"
                class="pl-7 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
            </div>
          </div>

          <div>
            <label for="discount_percent" class="block text-sm font-medium text-gray-700">% Giảm giá</label>
            <div class="mt-1 relative rounded-md shadow-sm">
              <input 
                type="number" 
                step="0.01" 
                min="0" 
                max="100" 
                id="discount_percent" 
                name="discount_percent"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <span class="text-gray-500 sm:text-sm">%</span>
              </div>
            </div>
          </div>

          <div>
            <label for="quantity" class="block text-sm font-medium text-gray-700">Số lượng *</label>
            <input 
              type="number" 
              min="0" 
              id="quantity" 
              name="quantity" 
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
          </div>

          <div class="flex justify-end pt-4">
            <button 
              type="button"
              id="cancelBtn"
              class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
            >
              Hủy
            </button>
            <button 
              type="submit"
              class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Lưu
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">Xác nhận xóa</h3>
          <button id="closeDeleteModalBtn" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <p class="mb-4">Bạn có chắc chắn muốn xóa thời hạn này? Hành động này không thể hoàn tác.</p>

        <div class="flex justify-end">
          <button 
            id="cancelDeleteBtn"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
          >
            Hủy
          </button>
          <button 
            id="confirmDeleteBtn"
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
          >
            Xóa
          </button>
        </div>
      </div>
    </div>

    <script>
      // Global variables
      const productId = `<%- id %>`;
      let deletingDurationId = null;

      // DOM elements
      const durationModal = document.getElementById('durationModal');
      const deleteModal = document.getElementById('deleteModal');
      const durationForm = document.getElementById('durationForm');
      const errorMessage = document.getElementById('errorMessage');
      const successMessage = document.getElementById('successMessage');

      // Setup axios with credentials
      axios.defaults.withCredentials = true;

      // Load product details and durations
      async function loadProductDetails() {
        try {
          const response = await axios.get(`/products/${productId}`);
          const product = response.data;

          // Update product details
          document.getElementById('productName').textContent = product.name;
          document.getElementById('productDescription').textContent = product.description || 'Không có mô tả';
          document.getElementById('productId').value = productId;

          // Load durations
          loadProductDurations();
        } catch (error) {
          showError('Không thể tải thông tin sản phẩm: ' + (error.response?.data?.message || error.message));
        }
      }

      // Load product durations
      async function loadProductDurations() {
        try {
          const response = await axios.get(`/products/${productId}/durations`);
          const durations = response.data;

          renderDurationsTable(durations);
        } catch (error) {
          showError('Không thể tải thông tin thời hạn: ' + (error.response?.data?.message || error.message));
        }
      }

      // Render durations table
      function renderDurationsTable(durations) {
        const tableBody = document.getElementById('durationsTableBody');
        tableBody.innerHTML = '';

        if (durations.length === 0) {
          const emptyRow = document.createElement('tr');
          emptyRow.innerHTML = `
            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
              Chưa có thời hạn nào được thiết lập
            </td>
          `;
          tableBody.appendChild(emptyRow);
          return;
        }

        // Sort durations by duration_days
        durations.sort((a, b) => a.duration_days - b.duration_days);

        durations.forEach(duration => {
          const row = document.createElement('tr');
          row.className = 'hover:bg-gray-50';

          const discountPrice = duration.discount_price > 0 ? `$${duration.discount_price.toFixed(2)}` : '-';
          const discountPercent = duration.discount_percent > 0 ? `${duration.discount_percent}%` : '-';

          row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${duration.id}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${duration.duration_days}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$${duration.original_price.toFixed(2)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${discountPrice}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${discountPercent}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${duration.quantity}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button onclick="editDuration(${duration.id})" class="text-blue-600 hover:text-blue-900">Sửa</button>
                <button onclick="showDeleteModal(${duration.id})" class="text-red-600 hover:text-red-900">Xóa</button>
              </div>
            </td>
          `;

          tableBody.appendChild(row);
        });
      }

      // Show duration modal for adding
      function showAddDurationModal() {
        document.getElementById('modalTitle').textContent = 'Thêm thời hạn mới';
        document.getElementById('durationId').value = '';
        durationForm.reset();
        durationModal.classList.remove('hidden');
      }

      // Show duration modal for editing
      async function editDuration(durationId) {
        try {
          const response = await axios.get(`/products/durations/${durationId}`);
          const duration = response.data;

          document.getElementById('modalTitle').textContent = 'Chỉnh sửa thời hạn';
          document.getElementById('durationId').value = duration.id;
          document.getElementById('duration_days').value = duration.duration_days;
          document.getElementById('original_price').value = duration.original_price;
          document.getElementById('discount_price').value = duration.discount_price || '';
          document.getElementById('discount_percent').value = duration.discount_percent || '';
          document.getElementById('quantity').value = duration.quantity;

          durationModal.classList.remove('hidden');
        } catch (error) {
          showError('Không thể tải thông tin thời hạn: ' + (error.response?.data?.message || error.message));
        }
      }

      // Hide duration modal
      function hideDurationModal() {
        durationModal.classList.add('hidden');
      }

      // Show delete confirmation modal
      function showDeleteModal(durationId) {
        deletingDurationId = durationId;
        deleteModal.classList.remove('hidden');
      }

      // Hide delete confirmation modal
      function hideDeleteModal() {
        deleteModal.classList.add('hidden');
        deletingDurationId = null;
      }

      // Delete duration
      async function deleteDuration() {
        if (!deletingDurationId) return;

        try {
          await axios.delete(`/products/durations/${deletingDurationId}`);
          hideDeleteModal();
          showSuccess('Đã xóa thời hạn thành công');
          loadProductDurations();
        } catch (error) {
          showError('Không thể xóa thời hạn: ' + (error.response?.data?.message || error.message));
        }
      }

      // Save duration (create or update)
      async function saveDuration(event) {
        event.preventDefault();

        const durationId = document.getElementById('durationId').value;
        const formData = {
          product_id: parseInt(productId),
          duration_days: parseInt(document.getElementById('duration_days').value),
          original_price: parseFloat(document.getElementById('original_price').value),
          quantity: parseInt(document.getElementById('quantity').value),
        };

        // Optional fields
        const discountPrice = document.getElementById('discount_price').value;
        if (discountPrice) {
          formData.discount_price = parseFloat(discountPrice);
        }

        const discountPercent = document.getElementById('discount_percent').value;
        if (discountPercent) {
          formData.discount_percent = parseFloat(discountPercent);
        }

        try {
          if (durationId) {
            // Update existing duration
            await axios.patch(`/products/durations/${durationId}`, formData);
            showSuccess('Đã cập nhật thời hạn thành công');
          } else {
            // Create new duration
            await axios.post(`/products/${productId}/durations`, formData);
            showSuccess('Đã thêm thời hạn mới thành công');
          }

          hideDurationModal();
          loadProductDurations();
        } catch (error) {
          showError('Không thể lưu thời hạn: ' + (error.response?.data?.message || error.message));
        }
      }

      // Show error message
      function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        successMessage.classList.add('hidden');

        // Auto hide after 5 seconds
        setTimeout(() => {
          errorMessage.classList.add('hidden');
        }, 5000);
      }

      // Show success message
      function showSuccess(message) {
        successMessage.textContent = message;
        successMessage.classList.remove('hidden');
        errorMessage.classList.add('hidden');

        // Auto hide after 5 seconds
        setTimeout(() => {
          successMessage.classList.add('hidden');
        }, 5000);
      }

      // Event listeners
      document.addEventListener('DOMContentLoaded', () => {
        // Load product details
        loadProductDetails();

        // Back button
        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = `/admin/products/${productId}`;
        });

        // Add duration button
        document.getElementById('addDurationBtn').addEventListener('click', showAddDurationModal);

        // Close modal buttons
        document.getElementById('closeModalBtn').addEventListener('click', hideDurationModal);
        document.getElementById('cancelBtn').addEventListener('click', hideDurationModal);

        // Close delete modal buttons
        document.getElementById('closeDeleteModalBtn').addEventListener('click', hideDeleteModal);
        document.getElementById('cancelDeleteBtn').addEventListener('click', hideDeleteModal);

        // Confirm delete button
        document.getElementById('confirmDeleteBtn').addEventListener('click', deleteDuration);

        // Duration form submit
        durationForm.addEventListener('submit', saveDuration);
      });
    </script>
  </body>
</html>