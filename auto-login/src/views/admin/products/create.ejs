<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin - Create Product</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css">
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Create New Product</h1>
        <div class="space-x-2">
          <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Products
          </button>
        </div>
      </div>

      <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="p-6">
          <div id="errorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>

          <form id="createProductForm" class="space-y-6">
            <!-- Basic Information -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Basic Information</h2>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                  <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label for="category_ids" class="block text-sm font-medium text-gray-700">Categories</label>
                  <div id="categories-container" class="mt-1 border border-gray-300 rounded-md p-2 max-h-40 overflow-y-auto">
                    <!-- Categories checkboxes will be loaded dynamically -->
                  </div>
                </div>

                <div>
                  <label for="image_url" class="block text-sm font-medium text-gray-700">Image URL</label>
                  <input 
                    type="url" 
                    id="image_url" 
                    name="image_url"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label for="image_file" class="block text-sm font-medium text-gray-700">Or Upload Image</label>
                  <input 
                    type="file" 
                    id="image_file" 
                    accept="image/*"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div id="image_preview_container" class="hidden">
                  <label class="block text-sm font-medium text-gray-700">Image Preview</label>
                  <div class="mt-1 flex items-center">
                    <img id="image_preview" src="" alt="Preview" class="h-32 w-auto object-contain" />
                    <button 
                      type="button" 
                      id="clear_image_btn"
                      class="ml-2 bg-red-500 hover:bg-red-700 text-white text-xs py-1 px-2 rounded"
                    >
                      Clear
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Description -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Description</h2>
              <div>
                <label for="description" class="block text-sm font-medium text-gray-700">Product Description (Markdown supported)</label>
                <textarea 
                  id="description" 
                  name="description" 
                  rows="10"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                ></textarea>
              </div>
              <!-- EasyMDE Markdown Editor -->
              <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/easymde/dist/easymde.min.css">
              <script src="https://cdn.jsdelivr.net/npm/easymde/dist/easymde.min.js"></script>
              <script>
                document.addEventListener('DOMContentLoaded', function() {
                  const easyMDE = new EasyMDE({
                    element: document.getElementById('description'),
                    spellChecker: false,
                    autofocus: false,
                    toolbar: ['bold', 'italic', 'heading', '|', 'quote', 'unordered-list', 'ordered-list', '|', 'link', 'image', '|', 'preview', 'side-by-side', 'fullscreen', '|', 'guide'],
                    placeholder: 'Type your markdown description here...',
                    status: ['autosave', 'lines', 'words', 'cursor']
                  });
                });
              </script>
            </div>

            <!-- Prompt Features -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Prompt Features</h2>
              <div class="space-y-4">
                <div class="flex items-center">
                  <input 
                    type="checkbox" 
                    id="has_prompt_library" 
                    name="has_prompt_library"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label for="has_prompt_library" class="ml-2 block text-sm font-medium text-gray-700">Has Prompt Library</label>
                </div>
                <div class="flex items-center">
                  <input 
                    type="checkbox" 
                    id="has_prompt_video" 
                    name="has_prompt_video"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label for="has_prompt_video" class="ml-2 block text-sm font-medium text-gray-700">Has Prompt Video</label>
                </div>
                <div class="flex items-center">
                  <input 
                    type="checkbox" 
                    id="is_best_seller" 
                    name="is_best_seller"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label for="is_best_seller" class="ml-2 block text-sm font-medium text-gray-700">Best Seller</label>
                </div>
                <div>
                  <label for="sort" class="block text-sm font-medium text-gray-700">Sort Order</label>
                  <input 
                    type="number" 
                    id="sort" 
                    name="sort"
                    value="0"
                    min="0"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <p class="mt-1 text-sm text-gray-500">Higher values will be displayed first</p>
                </div>
              </div>
            </div>

            <!-- Features -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Features</h2>
              <div class="space-y-2">
                <div id="features-container">
                  <!-- Features will be added here dynamically -->
                </div>
                <button 
                  type="button" 
                  id="addFeatureBtn"
                  class="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Add Feature
                </button>
              </div>
            </div>

            <!-- Product Durations -->
            <div>
              <div class="flex justify-between items-center mb-2">
                <h2 class="text-xl font-semibold">Additional Durations</h2>
                <button
                  type="button"
                  id="addDurationBtn"
                  class="bg-blue-500 hover:bg-blue-700 text-white text-sm py-1 px-2 rounded"
                >
                  Add Duration
                </button>
              </div>
              <div id="durationsContainer" class="space-y-3">
                <!-- Duration items will be added here dynamically -->
              </div>
            </div>

            <!-- Payment Methods & Discounts -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h2 class="text-xl font-semibold mb-4">Payment Methods & Discounts</h2>
              <div id="payment-methods-container" class="space-y-3">
                <!-- Payment methods with discount inputs will be loaded here -->
              </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
              <button 
                type="submit"
                class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Create Product
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <script>
      let features = [];
      
      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }
      
      // Load categories
      async function loadCategories() {
        try {
          const response = await axios.get('/products/categories');
          const categories = response.data;
          
          // Populate checkboxes for categories
          const categoriesContainer = document.getElementById('categories-container');
          categoriesContainer.innerHTML = '';
          
          categories.forEach(category => {
            // Add checkbox for multiple selection
            const checkboxDiv = document.createElement('div');
            checkboxDiv.className = 'flex items-center mb-2';
            checkboxDiv.innerHTML = `
              <input 
                type="checkbox" 
                id="category_${category.id}" 
                name="category_ids[]" 
                value="${category.id}"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label for="category_${category.id}" class="ml-2 block text-sm text-gray-900">
                ${category.name}
              </label>
            `;
            categoriesContainer.appendChild(checkboxDiv);
          });
        } catch (error) {
          console.error('Error loading categories:', error);
        }
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Show toast notification
      function showToast(message, type = 'success') {
        let backgroundColor;
        switch (type) {
          case 'success':
            backgroundColor = '#48bb78';
            break;
          case 'error':
            backgroundColor = '#f56565';
            break;
          case 'info':
            backgroundColor = '#4299e1';
            break;
          default:
            backgroundColor = '#48bb78';
        }

        Toastify({
          text: message,
          duration: 3000,
          close: true,
          gravity: "top",
          position: "right",
          backgroundColor,
          stopOnFocus: true
        }).showToast();
      }

      // Function to handle image selection and convert to base64
      function handleImageSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Check file size (limit to 1MB)
        if (file.size > 1024 * 1024) {
          showToast('Image size should be less than 1MB', 'error');
          event.target.value = '';
          return;
        }

        // Check file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
          showToast('Please select a valid image file (JPG, PNG, GIF, WEBP)', 'error');
          event.target.value = '';
          return;
        }

        // Create FormData for file upload
        const formData = new FormData();
        formData.append('image', file);

        // Upload to server
        uploadImageToServer(formData, file.name);
      }

      // Function to upload image to server
      async function uploadImageToServer(formData, originalName) {
        try {
          const response = await axios.post('/upload/image', formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            timeout: 30000, // 30 second timeout
            withCredentials: true // Ensure cookies are sent
          });

          if (response.data && response.data.success && response.data.imageUrl) {
            console.log('Image uploaded successfully:', response.data.imageUrl);
            
            // Set the image URL in the input field
            document.getElementById('image_url').value = response.data.imageUrl;
            document.getElementById('image_preview').src = response.data.imageUrl;
            document.getElementById('image_preview_container').classList.remove('hidden');
            
            showToast('Image uploaded successfully!', 'success');
          } else {
            throw new Error('Invalid response from server');
          }
        } catch (error) {
          console.error('Image upload failed:', error);
          let errorMessage = 'Failed to upload image';

          if (error.response) {
            errorMessage = error.response.data?.message || errorMessage;
          } else if (error.request) {
            errorMessage = 'Network error - please check your connection';
          }

          showToast(errorMessage, 'error');
          
          // Clear the file input on error
          document.getElementById('image_file').value = '';
        }
      }

      // Function to handle URL input change
      function handleImageUrlChange(event) {
        const url = event.target.value.trim();
        const preview = document.getElementById('image_preview');
        const container = document.getElementById('image_preview_container');
        
        if (url && !url.startsWith('data:')) { // Don't try to load base64 as external URL
          // Test if URL is valid and image loads
          const img = new Image();
          img.onload = function() {
            preview.src = url;
            container.classList.remove('hidden');
          };
          img.onerror = function() {
            container.classList.add('hidden');
            showToast('Invalid image URL or image failed to load', 'error');
          };
          img.src = url;
        } else if (url.startsWith('data:')) {
          // If it's base64 data, just show it
          preview.src = url;
          container.classList.remove('hidden');
        } else {
          container.classList.add('hidden');
        }
      }

      // Function to clear image
      function clearImage() {
        document.getElementById('image_url').value = '';
        document.getElementById('image_file').value = '';
        document.getElementById('image_preview').src = '';
        document.getElementById('image_preview_container').classList.add('hidden');
        showToast('Image removed', 'success');
      }

      // Show error message
      function showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        showToast(message, 'error');
      }

      // Render features inputs
      function renderFeatures() {
        const container = document.getElementById('features-container');
        container.innerHTML = '';

        features.forEach((feature, index) => {
          const featureRow = document.createElement('div');
          featureRow.className = 'flex items-center space-x-2';
          
          featureRow.innerHTML = `
            <input 
              type="text" 
              name="features[]" 
              value="${feature}" 
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
            <button 
              type="button" 
              class="remove-feature text-red-600 hover:text-red-800" 
              data-index="${index}"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </button>
          `;
          
          container.appendChild(featureRow);
        });

        // Add event listeners for remove buttons
        document.querySelectorAll('.remove-feature').forEach(button => {
          button.addEventListener('click', function() {
            const index = parseInt(this.getAttribute('data-index'));
            features.splice(index, 1);
            renderFeatures();
          });
        });
        
        // Add event listeners for input changes to update the features array
        document.querySelectorAll('input[name="features[]"]').forEach((input, index) => {
          input.addEventListener('input', function() {
            features[index] = this.value;
          });
        });
      }

      // Add a new feature input
      function addFeature() {
        // Save current feature values before adding a new one
        const featureInputs = document.querySelectorAll('input[name="features[]"]');
        features = Array.from(featureInputs).map(input => input.value);
        
        // Add new empty feature
        features.push('');
        renderFeatures();
        
        // Focus the last input
        const inputs = document.querySelectorAll('input[name="features[]"]');
        if (inputs.length > 0) {
          inputs[inputs.length - 1].focus();
        }
      }

      // Function to update discount percent when discount price changes
      function updateDiscountPercent(input) {
        const durationItem = input.closest('.duration-item');
        const priceInput = durationItem.querySelector('.duration-price');
        const discountPriceInput = durationItem.querySelector('.discount-price');
        const discountPercentSpan = durationItem.querySelector('.discount-percent');

        const originalPrice = parseFloat(priceInput.value);
        const discountPrice = parseFloat(discountPriceInput.value);

        if (!isNaN(originalPrice) && !isNaN(discountPrice) && discountPrice < originalPrice && discountPrice > 0) {
          const discountPercent = ((originalPrice - discountPrice) / originalPrice) * 100;
          discountPercentSpan.textContent = ` (${discountPercent.toFixed(2)}%)`;
          // Store discount percent as a data attribute for form submission
          discountPriceInput.dataset.discountPercent = discountPercent.toFixed(2);
        } else {
          discountPercentSpan.textContent = '';
          discountPriceInput.dataset.discountPercent = '0';
        }
      }
      
      // Function to update discount price when original price changes
      function updateDiscountPriceOnOriginalPriceChange(input) {
        const durationItem = input.closest('.duration-item');
        const discountPriceInput = durationItem.querySelector('.discount-price');
        
        // If discount price has a value, recalculate the discount percent
        if (discountPriceInput.value) {
          updateDiscountPercent(discountPriceInput);
        }
      }

      // Function to add a new duration item
      function addDurationItem() {
        const durationsContainer = document.getElementById('durationsContainer');
        const durationIndex = document.querySelectorAll('.duration-item').length;

        const durationItem = document.createElement('div');
        durationItem.className = 'duration-item flex flex-wrap items-center space-x-2 p-3 border border-gray-300 rounded-md';

        durationItem.innerHTML = `
          <div class="flex-1 min-w-[150px] mb-2">
            <label class="block text-xs text-gray-500">Duration (Days)</label>
            <input
              type="number"
              class="duration-days w-full px-2 py-1 border border-gray-300 rounded"
              min="1"
              placeholder="30"
              required
            />
          </div>
          <div class="flex-1 min-w-[150px] mb-2">
            <label class="block text-xs text-gray-500">Original Price ($)</label>
            <input
              type="number"
              class="duration-price w-full px-2 py-1 border border-gray-300 rounded"
              min="0"
              step="0.01"
              placeholder="9.99"
              required
              oninput="updateDiscountPriceOnOriginalPriceChange(this)"
            />
          </div>
          <div class="flex-1 min-w-[150px] mb-2">
            <label class="block text-xs text-gray-500">Discount Price ($) <span class="discount-percent text-green-600"></span></label>
            <input
              type="number"
              class="discount-price w-full px-2 py-1 border border-gray-300 rounded"
              min="0"
              step="0.01"
              placeholder="8.99"
              data-discount-percent="0"
              oninput="updateDiscountPercent(this)"
            />
          </div>
          <div class="flex-1 min-w-[150px] mb-2">
            <label class="block text-xs text-gray-500">Quantity</label>
            <input
              type="number"
              class="duration-quantity w-full px-2 py-1 border border-gray-300 rounded"
              min="0"
              placeholder="100"
              required
            />
          </div>
          <button type="button" class="remove-duration-btn bg-red-500 hover:bg-red-700 text-white text-xs py-1 px-2 rounded h-fit self-end mb-2">Remove</button>
        `;

        durationsContainer.appendChild(durationItem);

        // Add event listener to the new remove button
        durationItem.querySelector('.remove-duration-btn').addEventListener('click', () => {
          durationItem.remove();
        });
      }

      // Create product
      async function createProduct(formData) {
        if (!setupAxios()) return;

        try {
          // Get all duration items
          const durationItems = document.querySelectorAll('.duration-item');
          const durations = [];
          durationItems.forEach(item => {
            const duration_days = parseInt(item.querySelector('.duration-days').value, 10);
            const price = parseFloat(item.querySelector('.duration-price').value);
            const discount_price = parseFloat(item.querySelector('.discount-price').value) || 0;
            const discount_percent = parseFloat(item.querySelector('.discount-price').dataset.discountPercent) || 0;
            const quantity = parseInt(item.querySelector('.duration-quantity').value, 10);

            if (!isNaN(duration_days) && !isNaN(price) && !isNaN(quantity)) {
              durations.push({
                duration_days: duration_days,
                original_price: price,
                discount_price: discount_price,
                discount_percent: discount_percent,
                quantity: quantity
              });
            }
          });
          
          // Get features from inputs
          const featureInputs = document.querySelectorAll('input[name="features[]"]');
          const updatedFeatures = Array.from(featureInputs).map(input => input.value.trim()).filter(Boolean);
          
          // Get selected category IDs
          const categoryIds = [];
          document.querySelectorAll('input[name="category_ids[]"]:checked').forEach(checkbox => {
            categoryIds.push(parseInt(checkbox.value));
          });
          
          // Create payload
          const payload = {
            name: formData.get('name'),
            description: formData.get('description'),
            features: updatedFeatures,
            durations: durations,
            has_prompt_library: formData.get('has_prompt_library') === 'on',
            has_prompt_video: formData.get('has_prompt_video') === 'on',
            is_best_seller: formData.get('is_best_seller') === 'on',
            sort: parseInt(formData.get('sort') || '0', 10),
            category_ids: categoryIds.length > 0 ? categoryIds : null
          };
          

          
          const imageUrl = formData.get('image_url');
          if (imageUrl) {
            payload.image_url = imageUrl;
          }

          const response = await axios.post('/products', payload);
          const newProduct = response.data;

          // Get payment methods discounts
          const paymentDiscounts = [];
          document.querySelectorAll('input[name="payment_methods[]"]:checked').forEach(checkbox => {
            const methodId = parseInt(checkbox.value);
            const discountInput = document.querySelector(`input[name="discount_${methodId}"]`);
            const discountPercent = parseFloat(discountInput.value) || 0;

            if (discountPercent > 0) {
              paymentDiscounts.push({
                payment_method_id: methodId,
                discount_percent: discountPercent
              });
            }
          });

          // Save payment discounts if any
          if (paymentDiscounts.length > 0) {
            await axios.post(`/products/${newProduct.id}/discounts`, {
              discounts: paymentDiscounts
            });
          }

          showToast('Product created successfully');
          
          // Redirect to the new product's detail page after a short delay
          setTimeout(() => {
            window.location.href = `/admin/products/${newProduct.id}`;
          }, 1500);
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to create product. Please try again.';
            showError(message);
          }
        }
      }

      // Load payment methods
      async function loadPaymentMethods() {
        try {
          const response = await axios.get('/products/payment-methods');
          const paymentMethods = response.data;
          renderPaymentMethods(paymentMethods);
        } catch (error) {
          console.error('Error loading payment methods:', error);
        }
      }

      // Render payment methods with discount inputs
      function renderPaymentMethods(paymentMethods, existingDiscounts = {}) {
        const container = document.getElementById('payment-methods-container');
        container.innerHTML = '';

        paymentMethods.forEach(method => {
          const methodDiv = document.createElement('div');
          methodDiv.className = 'flex items-center justify-between p-3 border border-gray-300 rounded-md bg-white';

          const currentDiscount = existingDiscounts[method.id] || 0;

          methodDiv.innerHTML = `
            <div class="flex items-center space-x-3">
              <input
                type="checkbox"
                id="method_${method.id}"
                name="payment_methods[]"
                value="${method.id}"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                ${currentDiscount > 0 ? 'checked' : ''}
              />
              <label for="method_${method.id}" class="text-sm font-medium text-gray-700">
                ${method.name.charAt(0).toUpperCase() + method.name.slice(1)}
              </label>
              <span class="text-xs text-gray-500">${method.description || ''}</span>
            </div>
            <div class="flex items-center space-x-2">
              <label for="discount_${method.id}" class="text-sm text-gray-600">Discount %:</label>
              <input
                type="number"
                id="discount_${method.id}"
                name="discount_${method.id}"
                value="${currentDiscount}"
                min="0"
                max="100"
                step="0.01"
                class="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                placeholder="0"
              />
            </div>
          `;

          container.appendChild(methodDiv);

          // Add event listener to checkbox to enable/disable discount input
          const checkbox = methodDiv.querySelector(`#method_${method.id}`);
          const discountInput = methodDiv.querySelector(`#discount_${method.id}`);

          checkbox.addEventListener('change', function() {
            if (this.checked) {
              discountInput.disabled = false;
              if (discountInput.value === '0' || discountInput.value === '') {
                discountInput.value = '5'; // Default 5% discount
              }
            } else {
              discountInput.disabled = true;
              discountInput.value = '0';
            }
          });

          // Initial state
          if (!checkbox.checked) {
            discountInput.disabled = true;
          }
        });
      }

      // Event listeners
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial setup
        setupAxios();
        await checkAuth();
        
        // Load categories
        await loadCategories();

        // Load payment methods
        await loadPaymentMethods();

        // Add feature button
        document.getElementById('addFeatureBtn').addEventListener('click', addFeature);

        // Add duration button
        document.getElementById('addDurationBtn').addEventListener('click', addDurationItem);

        // Image upload and URL input handlers
        document.getElementById('image_file').addEventListener('change', handleImageSelect);
        document.getElementById('image_url').addEventListener('input', handleImageUrlChange);
        document.getElementById('clear_image_btn').addEventListener('click', clearImage);

        // Back button
        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = '/admin/products';
        });

        // Form submission
        document.getElementById('createProductForm').addEventListener('submit', async (e) => {
          e.preventDefault();
          // Make sure all discount percentages are calculated before submission
          document.querySelectorAll('.discount-price').forEach(input => {
            if (input.value) {
              updateDiscountPercent(input);
            }
          });
          const formData = new FormData(e.target);
          await createProduct(formData);
        });

        // Add initial feature field
        addFeature();
      });
    </script>
  </body>
</html>