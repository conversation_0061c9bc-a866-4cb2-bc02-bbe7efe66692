<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin - View Account</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css">
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Account Details</h1>
        <div class="space-x-2">
          <button id="editBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Edit Account
          </button>
          <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Accounts
          </button>
        </div>
      </div>

      <div class="bg-white shadow rounded-lg overflow-hidden mb-6">
        <div class="p-6">
          <div id="errorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>

          <!-- Account Details -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 class="text-xl font-semibold mb-4">Basic Information</h2>

              <div class="space-y-3">
                <div>
                  <p class="text-sm font-medium text-gray-500">ID</p>
                  <p id="account-id" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Website URL</p>
                  <p id="account-website" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Username</p>
                  <p id="account-username" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Name</p>
                  <p id="account-name" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Description</p>
                  <p id="account-description" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Sort Order</p>
                  <p id="account-sort" class="mt-1 text-sm text-gray-900"></p>
                </div>
              </div>
            </div>

            <div>
              <h2 class="text-xl font-semibold mb-4">Additional Information</h2>

              <div class="space-y-3">
                <div>
                  <p class="text-sm font-medium text-gray-500">Login Cookie</p>
                  <p id="account-login-cookie" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Last Login</p>
                  <p id="account-last-login" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Created At</p>
                  <p id="account-created" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Updated At</p>
                  <p id="account-updated" class="mt-1 text-sm text-gray-900"></p>
                </div>
              </div>
            </div>
          </div>

          <!-- Image Preview -->
          <div id="image-preview-container" class="mt-6 hidden">
            <h2 class="text-xl font-semibold mb-4">Image Preview</h2>
            <div class="border rounded-lg overflow-hidden">
              <img id="account-image" src="" alt="Account Image" class="w-full h-auto max-h-64 object-contain" />
            </div>
          </div>

          <!-- Actions -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4">Actions</h2>
            <div class="flex space-x-2">
              <button id="autoLoginBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                Auto Login
              </button>
              <button id="deleteBtn" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete Account
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Packages Section -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">Assigned Packages</h2>
            <button id="assignPackageBtn" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
              Assign Package
            </button>
          </div>

          <div id="packages-container" class="mt-4">
            <div id="no-packages" class="text-gray-500 text-center py-4 hidden">No packages assigned to this account</div>
            <div id="packages-list" class="space-y-4"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-semibold mb-4">Confirm Delete</h3>
        <p class="mb-6">Are you sure you want to delete this account? This action cannot be undone.</p>
        <div class="flex justify-end space-x-2">
          <button id="cancelDeleteBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
          </button>
          <button id="confirmDeleteBtn" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
          </button>
        </div>
      </div>
    </div>

    <!-- Assign Package Modal -->
    <div id="assignPackageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg p-6 w-[500px] mx-auto">
        <h3 class="text-xl font-semibold mb-4">Assign Package</h3>
        <div id="packageModalError" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>

        <form id="assignPackageForm" class="space-y-4">
          <div>
            <label for="packageSelect" class="block text-sm font-medium text-gray-700">Select Package</label>
            <select
              id="packageSelect"
              name="packageSelect"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">Select a package</option>
              <!-- Package options will be populated here -->
            </select>
          </div>

          <div class="flex justify-end space-x-2 pt-4">
            <button type="button" id="cancelAssignBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
              Cancel
            </button>
            <button type="submit" id="confirmAssignBtn" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
              Assign
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Remove Package Modal -->
    <div id="removePackageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-semibold mb-4">Confirm Remove Package</h3>
        <p class="mb-6">Are you sure you want to remove this package from the account? This action cannot be undone.</p>
        <input type="hidden" id="removePackageId" value="" />
        <div class="flex justify-end space-x-2">
          <button onclick="hideRemovePackageModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
          </button>
          <button onclick="confirmRemovePackage()" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Remove
          </button>
        </div>
      </div>
    </div>

    <script>
      // Get account ID from server-rendered data
      const accountId = `<%- id %>`; // Use the ID passed from the server
      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Show toast notification
      function showToast(message, type = 'success') {
        const backgroundColor = type === 'success' ? '#48bb78' : '#f56565';

        Toastify({
          text: message,
          duration: 3000,
          close: true,
          gravity: "top",
          position: "right",
          backgroundColor,
          stopOnFocus: true
        }).showToast();
      }

      // Show error message
      function showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        showToast(message, 'error');
      }

      // Format date
      function formatDate(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleString();
      }

      // Fetch account details
      async function fetchAccount(id) {
        if (!setupAxios()) return;

        try {
          const response = await axios.get(`/accounts/${id}`);
          return response.data;
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to fetch account details. Please try again.';
            showError(message);
          }
          return null;
        }
      }

      // Fetch all packages
      async function fetchPackages() {
        if (!setupAxios()) return;

        try {
          const response = await axios.get('/packages');
          return response.data.data;
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            console.error('Failed to fetch packages:', error);
          }
          return [];
        }
      }

      // Display account details
      function displayAccountDetails(account) {
        document.getElementById('account-id').textContent = account.id;
        document.getElementById('account-website').textContent = account.website_url || 'N/A';
        document.getElementById('account-username').textContent = account.username || 'N/A';
        document.getElementById('account-name').textContent = account.name || 'N/A';
        document.getElementById('account-description').textContent = account.description || 'N/A';
        document.getElementById('account-sort').textContent = account.sort || 0;
        document.getElementById('account-login-cookie').textContent = account.login_cookie ? 'Yes (1)' : 'No (0)';
        document.getElementById('account-last-login').textContent = formatDate(account.last_login);
        document.getElementById('account-created').textContent = formatDate(account.created_at);
        document.getElementById('account-updated').textContent = formatDate(account.updated_at);

        // Display image if available
        if (account.img_intro) {
          document.getElementById('account-image').src = account.img_intro;
          document.getElementById('image-preview-container').classList.remove('hidden');
        }

        // Display packages
        displayPackages(account.packages || []);
      }

      // Display packages
      function displayPackages(packages) {
        const packagesContainer = document.getElementById('packages-container');
        const noPackagesElement = document.getElementById('no-packages');
        const packagesListElement = document.getElementById('packages-list');

        if (packages.length === 0) {
          noPackagesElement.classList.remove('hidden');
          packagesListElement.classList.add('hidden');
          return;
        }

        noPackagesElement.classList.add('hidden');
        packagesListElement.classList.remove('hidden');
        packagesListElement.innerHTML = '';

        packages.forEach(pkg => {
          const packageElement = document.createElement('div');
          packageElement.className = 'bg-gray-50 p-4 rounded-lg border';

          packageElement.innerHTML = `
            <div class="flex justify-between items-center">
              <div>
                <h3 class="font-semibold">${pkg.name}</h3>
                <p class="text-sm text-gray-600">${pkg.description || 'No description'}</p>
              </div>
              <button
                onclick="removePackage(${pkg.id})"
                class="text-red-600 hover:text-red-800 font-medium"
              >
                Remove
              </button>
            </div>
          `;

          packagesListElement.appendChild(packageElement);
        });
      }

      // Auto login
      async function autoLogin(id) {
        if (!setupAxios()) return;

        try {
          const response = await axios.post(`/accounts/${id}/login`);
          showToast('Auto login initiated successfully');
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to initiate auto login. Please try again.';
            showToast(message, 'error');
          }
        }
      }

      // Delete account
      async function deleteAccount(id) {
        if (!setupAxios()) return;

        try {
          await axios.delete(`/accounts/${id}`);
          showToast('Account deleted successfully');
          setTimeout(() => {
            window.location.href = '/admin/accounts';
          }, 1500);
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to delete account. Please try again.';
            showToast(message, 'error');
          }
        }
      }

      // Assign package to account
      async function assignPackage(accountId, packageId) {
        if (!setupAxios()) return;

        try {
          await axios.post(`/packages/${packageId}/accounts/${accountId}`);
          showToast('Package assigned successfully');
          hideAssignPackageModal();

          // Refresh account data instead of reloading the page
          const account = await fetchAccount(accountId);
          if (account) {
            displayAccountDetails(account);
          }
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to assign package. Please try again.';
            document.getElementById('packageModalError').textContent = message;
            document.getElementById('packageModalError').classList.remove('hidden');
          }
        }
      }

      // Remove package from account
      async function removePackage(packageId) {
        if (!setupAxios() || !accountId) return;

        // Show the remove package confirmation modal
        document.getElementById('removePackageId').value = packageId;
        document.getElementById('removePackageModal').classList.remove('hidden');
      }

      // Confirm remove package
      async function confirmRemovePackage() {
        const packageId = document.getElementById('removePackageId').value;
        if (!packageId) return;

        try {
          await axios.delete(`/packages/${packageId}/accounts/${accountId}`);
          showToast('Package removed successfully');
          hideRemovePackageModal();

          // Refresh account data instead of reloading the page
          const account = await fetchAccount(accountId);
          if (account) {
            displayAccountDetails(account);
          }
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to remove package. Please try again.';
            showToast(message, 'error');
          }
        }
      }

      // Hide remove package modal
      function hideRemovePackageModal() {
        document.getElementById('removePackageModal').classList.add('hidden');
      }

      // Populate package select dropdown
      async function populatePackageSelect() {
        const packages = await fetchPackages();
        const selectElement = document.getElementById('packageSelect');

        packages.forEach(pkg => {
          const option = document.createElement('option');
          option.value = pkg.id;
          option.textContent = pkg.name;
          selectElement.appendChild(option);
        });
      }

      // Show delete modal
      function showDeleteModal() {
        document.getElementById('deleteModal').classList.remove('hidden');
      }

      // Hide delete modal
      function hideDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
      }

      // Show assign package modal
      function showAssignPackageModal() {
        document.getElementById('assignPackageModal').classList.remove('hidden');
        document.getElementById('packageModalError').classList.add('hidden');
      }

      // Hide assign package modal
      function hideAssignPackageModal() {
        document.getElementById('assignPackageModal').classList.add('hidden');
      }

      // Event listeners
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial setup
        setupAxios();

        // Fetch and display account details
        if (accountId) {
          const account = await fetchAccount(accountId);
          if (account) {
            displayAccountDetails(account);
          }
        } else {
          showError('Account ID not found in URL');
        }

        // Populate package select dropdown
        await populatePackageSelect();

        // Button event listeners
        document.getElementById('editBtn').addEventListener('click', () => {
          window.location.href = `/admin/accounts/${accountId}/edit`;
        });

        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = '/admin/accounts';
        });

        document.getElementById('autoLoginBtn').addEventListener('click', () => {
          autoLogin(accountId);
        });

        document.getElementById('deleteBtn').addEventListener('click', showDeleteModal);
        document.getElementById('cancelDeleteBtn').addEventListener('click', hideDeleteModal);
        document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
          deleteAccount(accountId);
        });

        document.getElementById('assignPackageBtn').addEventListener('click', showAssignPackageModal);
        document.getElementById('cancelAssignBtn').addEventListener('click', hideAssignPackageModal);

        document.getElementById('assignPackageForm').addEventListener('submit', async (e) => {
          e.preventDefault();
          const packageId = document.getElementById('packageSelect').value;

          if (!packageId) {
            document.getElementById('packageModalError').textContent = 'Please select a package';
            document.getElementById('packageModalError').classList.remove('hidden');
            return;
          }

          await assignPackage(accountId, packageId);
        });
      });
    </script>
  </body>
</html>
