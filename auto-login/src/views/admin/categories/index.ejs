<%- include('../../partials/layout', {
  title: 'Category Management',
  currentPath: '/admin/categories',
  body: `
    <div class="container mx-auto">
      <div class="flex justify-start items-center mb-6">
        <div>
          <button id="createCategoryBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
            Create New Category
          </button>
          <button id="backToProductsBtn" class="ml-2 bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Products
          </button>
        </div>
      </div>

      <!-- Categories Table -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sort</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hot</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody id="categoriesTableBody" class="bg-white divide-y divide-gray-200">
            <!-- Category rows will be inserted here -->
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="mt-4 flex justify-between items-center">
        <div>
          <button id="prevPageBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-l">
            Previous
          </button>
          <button id="nextPageBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-r">
            Next
          </button>
        </div>
        <div class="text-gray-600">
          Page <span id="currentPage">1</span> of <span id="totalPages">1</span>
        </div>
      </div>
    </div>

    <!-- Create/Edit Category Modal -->
    <div id="categoryModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center z-50 hidden">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p id="modalTitle" class="text-2xl font-bold">Create Category</p>
            <div class="modal-close cursor-pointer z-50" onclick="hideCategoryModal()">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <form id="categoryForm">
            <input type="hidden" id="categoryId" value="">
            <input type="hidden" id="categoryImgUrl" name="img_url" value="">
            <div class="mb-4">
              <label for="categoryName" class="block text-gray-700 text-sm font-bold mb-2">Category Name</label>
              <input type="text" id="categoryName" name="name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
            </div>
            <div class="mb-4">
              <label for="categoryDescription" class="block text-gray-700 text-sm font-bold mb-2">Description</label>
              <textarea id="categoryDescription" name="description" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" rows="3"></textarea>
            </div>
            <div class="mb-4">
              <label for="categorySort" class="block text-gray-700 text-sm font-bold mb-2">Sort Order</label>
              <input type="number" id="categorySort" name="sort" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" min="0" value="0">
              <p class="text-xs text-gray-500 mt-1">Lower numbers appear first. Leave empty for automatic ordering.</p>
            </div>
            <div class="mb-4">
              <label for="categoryIsHot" class="block text-gray-700 text-sm font-bold mb-2">Hot Category</label>
              <div class="flex items-center">
                <input type="checkbox" id="categoryIsHot" name="is_hot" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <span class="ml-2 text-gray-700">Mark as hot category</span>
              </div>
              <p class="text-xs text-gray-500 mt-1">Hot categories will be highlighted with a special icon.</p>
            </div>
            <div class="mb-4">
              <label for="categoryImageUpload" class="block text-gray-700 text-sm font-bold mb-2">Category Image</label>
              <div class="flex items-center">
                <input type="file" id="categoryImageUpload" class="hidden" accept="image/*">
                <button type="button" id="uploadImageBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                  Upload Image
                </button>
                <span id="uploadFileName" class="ml-2 text-gray-600"></span>
              </div>
              <div id="imagePreviewContainer" class="mt-2 hidden">
                <img id="imagePreview" class="h-32 w-32 object-cover border rounded" src="" alt="Category image preview">
                <button type="button" id="removeImageBtn" class="mt-2 text-red-500 text-sm">Remove Image</button>
              </div>
              <div id="uploadProgress" class="mt-2 hidden">
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div id="uploadProgressBar" class="bg-blue-600 h-2.5 rounded-full" style="width: 0%"></div>
                </div>
                <p id="uploadStatus" class="text-sm text-gray-600 mt-1">Uploading: 0%</p>
              </div>
            </div>
            <div class="flex justify-end pt-2">
              <button type="button" class="modal-close px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2" onclick="hideCategoryModal()">Cancel</button>
              <button type="submit" class="px-4 bg-blue-500 p-3 rounded-lg text-white hover:bg-blue-600">Save</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center z-50 hidden">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Confirm Delete</p>
            <div class="modal-close cursor-pointer z-50" onclick="hideDeleteModal()">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <p>Are you sure you want to delete this category? This action cannot be undone.</p>
          <p id="deleteWarning" class="text-red-500 mt-2 hidden">Warning: This category contains products. Deleting it may affect those products.</p>

          <div class="flex justify-end pt-2">
            <button class="modal-close px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2" onclick="hideDeleteModal()">Cancel</button>
            <button class="px-4 bg-red-500 p-3 rounded-lg text-white hover:bg-red-600" onclick="deleteCategory()">Delete</button>
          </div>
        </div>
      </div>
    </div>
  `,
  additionalScripts: `
    <script>
      // Global variables
      let currentPage = 1;
      let deleteCategoryId = null;

      // Load categories with pagination
      async function loadCategories(page = 1) {
        try {
          const response = await axios.get('/products/categories?page=' + page + '&limit=10');
          const categories = response.data.data || response.data;
          const totalPages = response.data.meta?.totalPages || 1;

          document.getElementById('currentPage').textContent = page;
          document.getElementById('totalPages').textContent = totalPages;

          renderCategoriesTable(categories);
        } catch (error) {
          handleApiError(error);
        }
      }

      // Render categories table
      function renderCategoriesTable(categories) {
        const tableBody = document.getElementById('categoriesTableBody');
        tableBody.innerHTML = '';

        if (categories.length === 0) {
          const emptyRow = document.createElement('tr');
          emptyRow.innerHTML =
            '<td colspan="7" class="px-6 py-4 text-center text-gray-500">' +
              'No categories found' +
            '</td>';
          tableBody.appendChild(emptyRow);
          return;
        }

        categories.forEach(category => {
          const row = document.createElement('tr');
          row.className = 'hover:bg-gray-50';

          const imageHtml = category.img_url ? 
            '<img src="' + category.img_url + '" alt="' + category.name + '" class="h-10 w-10 object-cover rounded">' : 
            '<span class="text-gray-400">No image</span>';

          const hotStatusHtml = category.is_hot ? 
            '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Hot</span>' : 
            '<span class="text-gray-400">-</span>';

          row.innerHTML =
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">' + category.id + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (category.sort || 0) + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + category.name + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + imageHtml + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (category.description || '-') + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + hotStatusHtml + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">' +
              '<div class="flex space-x-2">' +
                '<button onclick="editCategory(' + category.id + ')" class="text-blue-600 hover:text-blue-900">Edit</button>' +
                '<button onclick="showDeleteModal(' + category.id + ')" class="text-red-600 hover:text-red-900">Delete</button>' +
              '</div>' +
            '</td>';

          tableBody.appendChild(row);
        });
      }

      // Upload image
      async function uploadImage(file) {
        const formData = new FormData();
        formData.append('image', file);

        // Show progress container
        const progressContainer = document.getElementById('uploadProgress');
        const progressBar = document.getElementById('uploadProgressBar');
        const uploadStatus = document.getElementById('uploadStatus');
        progressContainer.classList.remove('hidden');
        
        try {
          const response = await axios.post('/upload/image', formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              progressBar.style.width = percentCompleted + '%';
              uploadStatus.textContent = 'Uploading: ' + percentCompleted + '%';
            }
          });
          
          // Hide progress container after upload
          progressContainer.classList.add('hidden');
          
          // Get the image URL from the response
          const imageUrl = response.data.imageUrl;
          
          // Set the image URL to the hidden input
          document.getElementById('categoryImgUrl').value = imageUrl;
          
          // Show image preview
          const imagePreview = document.getElementById('imagePreview');
          imagePreview.src = imageUrl;
          document.getElementById('imagePreviewContainer').classList.remove('hidden');
          
          // Show file name
          document.getElementById('uploadFileName').textContent = file.name;
          
          return imageUrl;
        } catch (error) {
          // Hide progress container on error
          progressContainer.classList.add('hidden');
          handleApiError(error);
          return null;
        }
      }

      // Show category modal for create
      function showCreateCategoryModal() {
        document.getElementById('modalTitle').textContent = 'Create Category';
        document.getElementById('categoryId').value = '';
        document.getElementById('categoryName').value = '';
        document.getElementById('categoryDescription').value = '';
        document.getElementById('categorySort').value = '0';
        document.getElementById('categoryImgUrl').value = '';
        document.getElementById('categoryIsHot').checked = false;
        document.getElementById('uploadFileName').textContent = '';
        document.getElementById('imagePreviewContainer').classList.add('hidden');
        showCategoryModal();
      }

      // Show category modal for edit
      async function editCategory(id) {
        try {
          const response = await axios.get('/products/categories/' + id);
          const category = response.data;

          document.getElementById('modalTitle').textContent = 'Edit Category';
          document.getElementById('categoryId').value = category.id;
          document.getElementById('categoryName').value = category.name;
          document.getElementById('categoryDescription').value = category.description || '';
          document.getElementById('categorySort').value = category.sort || '0';
          document.getElementById('categoryImgUrl').value = category.img_url || '';
          document.getElementById('categoryIsHot').checked = category.is_hot || false;
          
          // Show image preview if available
          if (category.img_url) {
            const imagePreview = document.getElementById('imagePreview');
            imagePreview.src = category.img_url;
            document.getElementById('imagePreviewContainer').classList.remove('hidden');
            
            // Extract file name from URL
            const fileName = category.img_url.split('/').pop();
            document.getElementById('uploadFileName').textContent = fileName || 'Current image';
          } else {
            document.getElementById('imagePreviewContainer').classList.add('hidden');
            document.getElementById('uploadFileName').textContent = '';
          }
          
          showCategoryModal();
        } catch (error) {
          handleApiError(error);
        }
      }

      // Show category modal
      function showCategoryModal() {
        document.getElementById('categoryModal').classList.remove('hidden');
        document.getElementById('categoryModal').classList.remove('opacity-0');
        document.getElementById('categoryModal').classList.add('opacity-100');
        document.getElementById('categoryModal').classList.add('pointer-events-auto');
        document.body.classList.add('modal-active');
      }

      // Hide category modal
      function hideCategoryModal() {
        document.getElementById('categoryModal').classList.add('opacity-0');
        document.getElementById('categoryModal').classList.remove('opacity-100');
        document.getElementById('categoryModal').classList.add('pointer-events-none');
        setTimeout(() => {
          document.getElementById('categoryModal').classList.add('hidden');
          document.body.classList.remove('modal-active');
        }, 300);
      }

      // Show delete confirmation modal
      function showDeleteModal(id) {
        deleteCategoryId = id;
        
        // Show warning if category has products
        document.getElementById('deleteWarning').classList.add('hidden');
        
        document.getElementById('deleteModal').classList.remove('hidden');
        document.getElementById('deleteModal').classList.remove('opacity-0');
        document.getElementById('deleteModal').classList.add('opacity-100');
        document.getElementById('deleteModal').classList.add('pointer-events-auto');
        document.body.classList.add('modal-active');
      }

      // Hide delete confirmation modal
      function hideDeleteModal() {
        document.getElementById('deleteModal').classList.add('opacity-0');
        document.getElementById('deleteModal').classList.remove('opacity-100');
        document.getElementById('deleteModal').classList.add('pointer-events-none');
        setTimeout(() => {
          document.getElementById('deleteModal').classList.add('hidden');
          document.body.classList.remove('modal-active');
        }, 300);
        deleteCategoryId = null;
      }

      // Save category (create or update)
      async function saveCategory(event) {
        event.preventDefault();
        
        const categoryId = document.getElementById('categoryId').value;
        const name = document.getElementById('categoryName').value;
        const description = document.getElementById('categoryDescription').value;
        const sort = parseInt(document.getElementById('categorySort').value) || 0;
        const img_url = document.getElementById('categoryImgUrl').value;
        const is_hot = document.getElementById('categoryIsHot').checked;
        
        try {
          if (categoryId) {
            // Update existing category
            await axios.put('/products/categories/' + categoryId, { name, description, img_url, sort, is_hot });
            showToast('Category updated successfully', 'success');
          } else {
            // Create new category
            await axios.post('/products/categories', { name, description, img_url, sort, is_hot });
            showToast('Category created successfully', 'success');
          }
          
          hideCategoryModal();
          loadCategories(currentPage);
        } catch (error) {
          handleApiError(error);
        }
      }

      // Delete category
      async function deleteCategory() {
        if (!deleteCategoryId) return;

        try {
          await axios.delete('/products/categories/' + deleteCategoryId);
          hideDeleteModal();
          showToast('Category deleted successfully');
          loadCategories(currentPage);
        } catch (error) {
          hideDeleteModal();
          handleApiError(error);
        }
      }

      // Remove image
      function removeImage() {
        document.getElementById('categoryImgUrl').value = '';
        document.getElementById('uploadFileName').textContent = '';
        document.getElementById('imagePreviewContainer').classList.add('hidden');
        document.getElementById('categoryImageUpload').value = '';
      }

      // Show toast notification
      function showToast(message, type = 'success') {
        const backgroundColor = type === 'success' ? '#48bb78' : '#f56565';

        Toastify({
          text: message,
          duration: 3000,
          close: true,
          gravity: "top",
          position: "right",
          backgroundColor,
          stopOnFocus: true
        }).showToast();
      }

      // Handle API error
      function handleApiError(error) {
        console.error('API Error:', error);
        let errorMessage = 'An error occurred. Please try again.';
        
        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.message) {
          errorMessage = error.message;
        }
        
        showToast(errorMessage, 'error');
      }

      // Document ready
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial load
        loadCategories(currentPage);

        // Pagination
        document.getElementById('prevPageBtn').addEventListener('click', () => {
          if (currentPage > 1) {
            currentPage--;
            loadCategories(currentPage);
          }
        });

        document.getElementById('nextPageBtn').addEventListener('click', () => {
          const totalPages = parseInt(document.getElementById('totalPages').textContent);
          if (currentPage < totalPages) {
            currentPage++;
            loadCategories(currentPage);
          }
        });

        // Create new category
        document.getElementById('createCategoryBtn').addEventListener('click', showCreateCategoryModal);

        // Back to products
        document.getElementById('backToProductsBtn').addEventListener('click', () => {
          window.location.href = '/admin/products';
        });

        // Form submission
        document.getElementById('categoryForm').addEventListener('submit', saveCategory);
        
        // Image upload button click
        document.getElementById('uploadImageBtn').addEventListener('click', () => {
          document.getElementById('categoryImageUpload').click();
        });
        
        // File input change
        document.getElementById('categoryImageUpload').addEventListener('change', async (e) => {
          if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            await uploadImage(file);
          }
        });
        
        // Remove image button
        document.getElementById('removeImageBtn').addEventListener('click', removeImage);
      });
    </script>
  `
}) %>