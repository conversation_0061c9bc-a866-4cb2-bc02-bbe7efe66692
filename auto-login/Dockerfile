# Build stage
FROM node:18-alpine AS builder

WORKDIR /usr/src/app

# Install netcat for database connection check
RUN apk add --no-cache netcat-openbsd

# Copy package files first for better caching
COPY package*.json ./
COPY yarn.lock ./
COPY tsconfig*.json ./

# Install dependencies using npm
RUN npm config set registry https://registry.npmjs.org/ && \
    npm install

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Make scripts executable
RUN chmod +x ./scripts/init.sh
RUN chmod +x ./scripts/run-migrations.js

# Production stage
FROM node:18-alpine

WORKDIR /usr/src/app

# Install netcat for database connection check
RUN apk add --no-cache netcat-openbsd

# This is needed for the crypto module
ENV NODE_OPTIONS="--require=crypto"

# Copy package files
COPY package*.json ./
COPY yarn.lock ./

# Install dependencies using npm
RUN npm config set registry https://registry.npmjs.org/ && \
    npm install --production=false && \
    # Ensure TypeORM CLI is available
    npm install -g typeorm typeorm-cli ts-node tsconfig-paths

# Copy built files and necessary configs
COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/src/migrations ./src/migrations
COPY --from=builder /usr/src/app/src/config ./src/config
COPY --from=builder /usr/src/app/scripts/init.sh ./scripts/init.sh
COPY --from=builder /usr/src/app/scripts/run-migrations.js ./scripts/run-migrations.js
COPY --from=builder /usr/src/app/scripts/crypto-patch.js ./scripts/crypto-patch.js
COPY --from=builder /usr/src/app/tsconfig*.json ./

# Copy source files for TypeORM (needed for migrations)
COPY --from=builder /usr/src/app/src ./src

# Make scripts executable in production stage
RUN chmod +x ./scripts/init.sh
RUN chmod +x ./scripts/run-migrations.js

EXPOSE 3000

CMD ["/usr/src/app/scripts/init.sh"]