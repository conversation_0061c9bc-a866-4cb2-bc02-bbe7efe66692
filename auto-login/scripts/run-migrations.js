/* eslint-disable @typescript-eslint/no-var-requires */
/**
 * This script runs TypeORM migrations directly using the JavaScript API
 */
const path = require('path');
const { DataSource } = require('typeorm');
const fs = require('fs');

// Log environment variables for debugging
console.log('Environment variables:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_USERNAME:', process.env.DB_USERNAME);
console.log('DB_DATABASE:', process.env.DB_DATABASE);

// Try to use the temporary config file if it exists
let config;
try {
  if (fs.existsSync('/tmp/ormconfig.js')) {
    config = require('/tmp/ormconfig.js');
    console.log('Using temporary ormconfig.js');
  } else {
    console.log('Using manual configuration');
    config = {
      type: 'postgres',
      host: process.env.DB_HOST || 'postgres',
      port: parseInt(process.env.DB_PORT || '5432', 10),
      username: process.env.DB_USERNAME || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: process.env.DB_DATABASE || 'auto_login',
      entities: [path.join(__dirname, '../dist/**/*.entity.js')],
      migrations: [path.join(__dirname, '../dist/migrations/*.js')],
      migrationsRun: false,
      synchronize: false,
      logging: true,
    };
  }
} catch (error) {
  console.log(
    'Error loading config, using manual configuration:',
    error.message,
  );
  config = {
    type: 'postgres',
    host: process.env.DB_HOST || 'postgres',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_DATABASE || 'auto_login',
    entities: [path.join(__dirname, '../dist/**/*.entity.js')],
    migrations: [path.join(__dirname, '../dist/migrations/*.js')],
    migrationsRun: false,
    synchronize: false,
    logging: true,
  };
}

// Create data source with the config
const dataSource = new DataSource(config);

// Initialize the data source and run migrations
async function runMigrations() {
  try {
    console.log(
      'Initializing data source with config:',
      JSON.stringify(config, null, 2),
    );
    await dataSource.initialize();
    console.log('Data source initialized successfully');

    console.log('Running migrations...');
    const migrations = await dataSource.runMigrations();
    console.log('Migrations completed successfully:', migrations);

    await dataSource.destroy();
    process.exit(0);
  } catch (error) {
    console.error('Error running migrations:', error);
    if (dataSource.isInitialized) {
      await dataSource.destroy();
    }
    process.exit(1);
  }
}

runMigrations();
