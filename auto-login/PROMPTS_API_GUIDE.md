# Prompts API Guide

## Tổng quan

Tôi đã tạo thành công hệ thống Prompts API với các tính năng sau:

### 1. Database Schema

Đã tạo 3 bảng chính:

- **prompt_categories**: <PERSON><PERSON><PERSON><PERSON> lý danh mục prompts
- **topics**: <PERSON><PERSON><PERSON><PERSON> lý chủ đề
- **prompts**: <PERSON><PERSON><PERSON> tr<PERSON> các prompts với đầy đủ thông tin

### 2. API Endpoints

#### Public APIs (không cần authentication):

1. **GET /prompt-categories**
   - Lấy danh sách tất cả categories
   - Response: Array of PromptCategory objects

2. **GET /prompts/by-category**
   - <PERSON><PERSON><PERSON> danh sách prompts theo category với pagination và search
   - Query parameters:
     - `page` (optional): S<PERSON> trang (default: 1)
     - `pageSize` (optional): Số items per page (default: 12)
     - `category_id` (optional): ID của category
     - `search_text` (optional): Text để search trong title, description, content
   - Response: 
     ```json
     {
       "data": [/* array of prompts */],
       "pagination": {
         "page": 1,
         "pageSize": 12,
         "total": 100,
         "totalPages": 9
       }
     }
     ```

3. **GET /prompts/:id**
   - Lấy chi tiết một prompt theo ID
   - Tự động tăng view_count khi được gọi
   - Response: Prompt object với đầy đủ thông tin

4. **GET /topics**
   - Lấy danh sách tất cả topics
   - Response: Array of Topic objects

#### Admin APIs (cần authentication + admin role):

1. **POST /admin/prompt-categories**
   - Tạo category mới
   - Body: CreatePromptCategoryDto

2. **POST /admin/prompts**
   - Tạo prompt mới
   - Body: CreatePromptDto

3. **GET /admin/prompts**
   - Lấy tất cả prompts (admin view)

### 3. Cấu trúc dữ liệu

#### PromptCategory
```typescript
{
  id: number;
  name: string;
  description?: string;
  image_url?: string;
  image_card_url?: string;
  prompt_count: number;
  is_coming_soon: boolean;
  created_at: Date;
  updated_at: Date;
}
```

#### Prompt
```typescript
{
  id: number;
  title: string;
  short_description?: string;
  content?: string;
  prompt_text?: string;
  optimization_guide?: string;
  category_id?: number;
  topic_id?: number;
  is_type: number;
  sub_type?: number;
  what_field?: string;
  tips_field?: string;
  how_field?: string;
  input_field?: string;
  output_field?: string;
  add_tip?: string;
  additional_information?: string;
  view_count: number;
  created_at: Date;
  updated_at: Date;
  category?: PromptCategory;
  topic?: Topic;
}
```

### 4. Cách chạy Migration

1. Build project:
   ```bash
   npm run build
   # hoặc
   yarn build
   ```

2. Chạy migration:
   ```bash
   npm run migration:run
   # hoặc
   yarn migration:run
   ```

### 5. Files đã tạo

```
src/
├── migrations/
│   ├── 1840000000000-CreatePromptTables.ts
│   └── 1840000100000-SeedPromptData.ts
└── modules/
    └── prompts/
        ├── dto/
        │   ├── create-prompt-category.dto.ts
        │   ├── create-prompt.dto.ts
        │   └── query-prompts.dto.ts
        ├── entities/
        │   ├── prompt-category.entity.ts
        │   ├── prompt.entity.ts
        │   └── topic.entity.ts
        ├── prompts.controller.ts
        ├── prompts.service.ts
        └── prompts.module.ts
```

### 6. Tính năng đặc biệt

- **Pagination**: Hỗ trợ phân trang với page và pageSize
- **Search**: Tìm kiếm trong title, description, content
- **View tracking**: Tự động tăng view_count khi xem chi tiết
- **Relations**: Tự động load category và topic khi cần
- **Validation**: Sử dụng class-validator cho input validation
- **Error handling**: Xử lý lỗi đầy đủ với HTTP status codes phù hợp

### 7. Sample Data

Migration seed đã tạo sẵn:
- 5 categories mẫu (Marketing, Content Writing, Business, Education, Technology)
- 8 topics mẫu
- 5 prompts mẫu với đầy đủ thông tin

### 8. Cách test API

Sau khi chạy migration và start server, bạn có thể test các endpoint:

```bash
# Lấy danh sách categories
curl http://localhost:3000/prompt-categories

# Lấy prompts theo category với pagination
curl "http://localhost:3000/prompts/by-category?page=1&pageSize=12&category_id=1"

# Lấy prompts với search
curl "http://localhost:3000/prompts/by-category?search_text=social"

# Lấy chi tiết prompt
curl http://localhost:3000/prompts/1
```

Tất cả đã được implement theo đúng yêu cầu của bạn!
