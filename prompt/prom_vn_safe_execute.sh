#!/bin/bash
# Safe batch execution script for prompt data (no dollar quotes)
# Generated at: 2025-06-10 21:33:12

echo 'Starting SAFE prompt data import (traditional escaping only)...'

echo 'Step 1: Importing categories...'
psql -d $DATABASE_NAME -f prom_vn_categories.sql
if [ $? -eq 0 ]; then
    echo '✅ Categories imported successfully'
else
    echo '❌ Error importing categories'
    exit 1
fi

echo 'Step 2: Importing topics...'
psql -d $DATABASE_NAME -f prom_vn_topics.sql
if [ $? -eq 0 ]; then
    echo '✅ Topics imported successfully'
else
    echo '❌ Error importing topics'
    exit 1
fi

echo 'Step 3: Importing prompts (SAFE VERSION)...'
psql -d $DATABASE_NAME -f prom_vn_prompts_safe.sql
if [ $? -eq 0 ]; then
    echo '✅ Prompts imported successfully'
else
    echo '❌ Error importing prompts'
    exit 1
fi

echo 'Step 4: Verifying data integrity...'
psql -d $DATABASE_NAME -f verify_data.sql
if [ $? -eq 0 ]; then
    echo '✅ Data verification completed'
else
    echo '⚠️  Warning: Data verification had issues (check manually)'
fi

echo '🎉 All data imported successfully!'
echo ''
echo '📊 Summary:'
echo '   - Categories: 20 records (max_id: 54)'
echo '   - Topics: 293 records (max_id: 351)' 
echo '   - Prompts: 4438 records (max_id: 5117) - SAFE VERSION'
echo ''
echo '🔧 Next steps:'
echo '   1. Check verification results above'
echo '   2. Test your application with the imported data'
echo '   3. Sequences are already reset for future inserts'
echo ''
echo '⚡ Using traditional escaping (no dollar quotes) for maximum compatibility' 