-- Test file to check if duplicate key issues are resolved
-- Run this before full import to test

-- Test categories insert
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (54, '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'http://prom.vn/uploads/1749281929205.webp', 'http://prom.vn/uploads/1749281929235.webp', 100, FALSE, '2025-06-06 15:22:15', '2025-06-07 07:38:49')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;

-- Test topics insert
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (329, '<PERSON><PERSON><PERSON><PERSON> thị v<PERSON> hàng <PERSON> phẩm', '', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();

-- Test prompts insert (using the problematic ID 4969)
INSERT INTO prompts (id, title, short_description, content, prompt_text, optimization_guide, category_id, topic_id, is_type, sub_type, what_field, tips_field, how_field, input_field, output_field, add_tip, additional_information, view_count, created_at, updated_at) 
VALUES (4969, 'Test Prompt', 'Test Description', 'Test Content', 'Test Text', 'Test Guide', 54, 329, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
content = EXCLUDED.content,
prompt_text = EXCLUDED.prompt_text,
optimization_guide = EXCLUDED.optimization_guide,
category_id = EXCLUDED.category_id,
topic_id = EXCLUDED.topic_id,
is_type = EXCLUDED.is_type,
sub_type = EXCLUDED.sub_type,
what_field = EXCLUDED.what_field,
tips_field = EXCLUDED.tips_field,
how_field = EXCLUDED.how_field,
input_field = EXCLUDED.input_field,
output_field = EXCLUDED.output_field,
add_tip = EXCLUDED.add_tip,
additional_information = EXCLUDED.additional_information,
view_count = EXCLUDED.view_count,
updated_at = EXCLUDED.updated_at;

-- Check results
SELECT 'Test completed successfully' as result; 