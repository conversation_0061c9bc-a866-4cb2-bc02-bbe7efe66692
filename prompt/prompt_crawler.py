import requests
import json
import time
from datetime import datetime
import os

class PromVNCrawler:
    def __init__(self):
        # <PERSON><PERSON> từ cURL
        self.cookies = {
            '_ga': 'GA1.1.417857401.1749199852',
            '_fbc': 'fb.1.1749199852394.IwY2xjawKviztleHRuA2FlbQIxMABicmlkETFDOExBa0FBc3RTMTJoU01uAR4wZdhH6jWPxwkCwQ3CIwbVkhsXN16MpdJC9tjObJE2th3_8IKf82JtSd0k9w_aem_3xh1vcxMAlVQ0oo81bnzVw',
            '_fbp': 'fb.1.1749199852401.157385255813603962',
            'g_state': '{"i_l":0}',
            '_ga_GV7S1N8KL0': 'GS2.1.s1749449682$o3$g1$t1749449753$j60$l0$h0',
        }
        
        # Headers từ cURL
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5',
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************._1qSUA4drfK5Q9Cdraf5b6Gevqbi0_-QUA04lDedylY',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?1',
            'sec-ch-ua-platform': '"Android"',
        }
        
        self.base_url = 'https://prom.vn/api'
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.session.cookies.update(self.cookies)
        
    def update_referer(self, url):
        """Cập nhật Referer header cho từng request"""
        self.session.headers['Referer'] = url
        
    def get_categories(self, section_id=1):
        """Lấy danh sách categories"""
        url = f"{self.base_url}/categories/by-sectionId/{section_id}"
        self.update_referer('https://prom.vn/thu-vien-prompt')
        
        params = {
            'searchTxt': '',
            'listCategory': 'null'
        }
        
        try:
            print(f"Requesting categories from: {url}")
            response = self.session.get(url, params=params)
            print(f"Status code: {response.status_code}")
            
            response.raise_for_status()
            data = response.json()
            
            if isinstance(data, list):
                return data
            elif isinstance(data, dict) and 'data' in data:
                return data['data']
            else:
                print(f"Unexpected response format: {type(data)}")
                return []
                
        except requests.RequestException as e:
            print(f"Error getting categories: {e}")
            return []
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            return []
    
    def get_prompts_by_category(self, category_id, page=1, page_size=12):
        """Lấy danh sách prompts theo category với pagination"""
        url = f"{self.base_url}/prompts/by-category"
        self.update_referer(f'https://prom.vn/thu-vien-prompt/category/{category_id}')
        
        params = {
            'page': page,
            'pageSize': page_size,
            'category_id': category_id,
            'search_text': '',
            'is_type': 1,
            'sub_type': ''
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
                
        except requests.RequestException as e:
            print(f"Error getting prompts for category {category_id}, page {page}: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            return None
    
    def get_prompt_detail(self, prompt_id):
        """Lấy chi tiết prompt"""
        url = f"{self.base_url}/prompts/{prompt_id}"
        self.update_referer(f'https://prom.vn/thu-vien-prompt/detail-prompts/{prompt_id}')
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"Error getting prompt detail {prompt_id}: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON decode error for prompt {prompt_id}: {e}")
            return None
    
    def get_all_prompts_for_category(self, category_id, category_name):
        """Lấy tất cả prompts của một category (xử lý pagination)"""
        all_prompts = []
        page = 1
        page_size = 12
        
        print(f"Crawling category: {category_name} (ID: {category_id})")
        
        while True:
            print(f"  - Fetching page {page}...")
            result = self.get_prompts_by_category(category_id, page, page_size)
            
            if not result or not isinstance(result, dict) or 'data' not in result:
                print(f"  - No more data or invalid response format")
                break
                
            prompts = result['data']
            if not prompts or not isinstance(prompts, list):
                print(f"  - No prompts found or invalid data format")
                break
                
            all_prompts.extend(prompts)
            
            # Check if có thêm trang nữa không
            total = result.get('total', 0)
            current_count = (page - 1) * page_size + len(prompts)
            
            print(f"  - Page {page}: {len(prompts)} prompts, Total so far: {len(all_prompts)}/{total}")
            
            if current_count >= total:
                break
                
            page += 1
            time.sleep(0.5)  # Delay để tránh spam API
        
        print(f"  - Found {len(all_prompts)} prompts")
        return all_prompts
    
    def get_detailed_prompts(self, prompts):
        """Lấy chi tiết cho tất cả prompts"""
        detailed_prompts = []
        
        for i, prompt in enumerate(prompts, 1):
            if not isinstance(prompt, dict) or 'id' not in prompt:
                print(f"    - Skipping invalid prompt at index {i}")
                continue
                
            prompt_id = prompt['id']
            print(f"    - Getting detail for prompt {prompt_id} ({i}/{len(prompts)})")
            
            detail = self.get_prompt_detail(prompt_id)
            if detail:
                detailed_prompts.append(detail)
            
            time.sleep(0.3)  # Delay để tránh spam API
        
        return detailed_prompts
    
    def crawl_all_data(self):
        """Crawl toàn bộ data"""
        print("Starting to crawl data from prom.vn...")
        
        # Lấy danh sách categories
        print("1. Getting categories...")
        categories = self.get_categories()
        
        if not categories:
            print("No categories found!")
            return {}
        
        print(f"Found {len(categories)} categories")
        
        all_data = {
            'crawl_time': datetime.now().isoformat(),
            'categories': [],
            'total_categories': len(categories),
            'total_prompts': 0
        }
        
        # Crawl từng category
        for cat_index, category in enumerate(categories, 1):
            # Kiểm tra xem category có phải là dict không
            if not isinstance(category, dict):
                print(f"Skipping invalid category at index {cat_index}: {type(category)} - {category}")
                continue
                
            if 'id' not in category or 'name' not in category:
                print(f"Skipping category missing required fields: {category}")
                continue
            
            category_id = category['id']
            category_name = category['name']
            
            print(f"\n2. Processing category {cat_index}/{len(categories)}: {category_name}")
            
            # Lấy tất cả prompts của category này
            prompts = self.get_all_prompts_for_category(category_id, category_name)
            
            if prompts:
                # Lấy chi tiết cho tất cả prompts
                print(f"3. Getting detailed information for {len(prompts)} prompts...")
                detailed_prompts = self.get_detailed_prompts(prompts)
                
                category_data = {
                    'category_info': category,
                    'prompts': detailed_prompts,
                    'prompt_count': len(detailed_prompts)
                }
                
                all_data['categories'].append(category_data)
                all_data['total_prompts'] += len(detailed_prompts)
                
                print(f"✓ Completed category: {category_name} ({len(detailed_prompts)} prompts)")
            else:
                print(f"✗ No prompts found for category: {category_name}")
        
        return all_data
    
    def save_to_txt(self, data, filename=None):
        """Lưu data ra file txt"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"prom_vn_data_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("PROM.VN DATA CRAWL RESULT\n")
                f.write("=" * 80 + "\n")
                f.write(f"Crawl Time: {data['crawl_time']}\n")
                f.write(f"Total Categories: {data['total_categories']}\n")
                f.write(f"Total Prompts: {data['total_prompts']}\n")
                f.write("=" * 80 + "\n\n")
                
                for cat_data in data['categories']:
                    category = cat_data['category_info']
                    prompts = cat_data['prompts']
                    
                    f.write(f"CATEGORY: {category['name']} (ID: {category['id']})\n")
                    f.write(f"Description: {category.get('description', 'N/A')}\n")
                    f.write(f"Prompt Count: {len(prompts)}\n")
                    f.write("-" * 60 + "\n")
                    
                    for prompt in prompts:
                        f.write(f"\nPROMPT ID: {prompt['id']}\n")
                        f.write(f"Title: {prompt['title']}\n")
                        f.write(f"Short Description: {prompt.get('short_description', 'N/A')}\n")
                        f.write(f"Content: {prompt.get('content', 'N/A')}\n")
                        f.write(f"Optimization Guide: {prompt.get('OptimationGuide', 'N/A')}\n")
                        f.write(f"Created: {prompt.get('created_at', 'N/A')}\n")
                        f.write(f"Updated: {prompt.get('updated_at', 'N/A')}\n")
                        
                        # Thêm các fields khác nếu có
                        for key in ['what', 'tips', 'text', 'how', 'input', 'output', 'addtip', 'addinformation']:
                            if prompt.get(key):
                                f.write(f"{key.title()}: {prompt[key]}\n")
                        
                        f.write("-" * 40 + "\n")
                    
                    f.write("\n" + "=" * 80 + "\n\n")
            
            print(f"\n✓ Data saved to: {filename}")
            return filename
            
        except Exception as e:
            print(f"Error saving file: {e}")
            return None

def main():
    # Tạo crawler instance
    crawler = PromVNCrawler()
    
    # Test API access trước
    print("Testing API access...")
    test_categories = crawler.get_categories()
    if not test_categories:
        print("❌ Cannot access API. Please check:")
        print("   - Your internet connection")
        print("   - Cookies and headers are still valid")
        print("   - You are logged in to prom.vn")
        return
    
    # Crawl all data
    print("✅ API access successful! Starting crawl process...")
    data = crawler.crawl_all_data()
    
    if data and data.get('total_prompts', 0) > 0:
        # Save to txt file
        filename = crawler.save_to_txt(data)
        
        # Save to JSON file (optional)
        json_filename = filename.replace('.txt', '.json') if filename else 'prom_vn_data.json'
        try:
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"✓ JSON data saved to: {json_filename}")
        except Exception as e:
            print(f"Error saving JSON: {e}")
        
        print(f"\n🎉 Crawl completed successfully!")
        print(f"📊 Summary:")
        print(f"   - Categories: {data['total_categories']}")
        print(f"   - Total Prompts: {data['total_prompts']}")
        print(f"   - Output files: {filename}, {json_filename}")
        
    else:
        print("❌ No data was crawled. Please check cookies and authentication.")

if __name__ == "__main__":
    main()
