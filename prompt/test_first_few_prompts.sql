-- Test file with first few prompts only
-- This helps isolate any issues

-- Clear data first
DELETE FROM prompts;
DELETE FROM topics;
DELETE FROM prompt_categories;

-- Reset sequences
ALTER SEQUENCE prompts_id_seq RESTART WITH 1;
ALTER SEQUENCE topics_id_seq RESTART WITH 1;
ALTER SEQUENCE prompt_categories_id_seq RESTART WITH 1;

-- Test categories
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (54, '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>ẩ<PERSON>', 'http://prom.vn/uploads/1749281929205.webp', 'http://prom.vn/uploads/1749281929235.webp', 100, FALSE, '2025-06-06 15:22:15', '2025-06-07 07:38:49')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;

-- Test topics
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (329, 'Tiếp thị và Bán hàng Dược phẩm', '', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();

-- Test first prompt (the one that was problematic)
INSERT INTO prompts (id, title, short_description, content, prompt_text, optimization_guide, category_id, topic_id, is_type, sub_type, what_field, tips_field, how_field, input_field, output_field, add_tip, additional_information, view_count, created_at, updated_at) 
VALUES (4968, 'Tạo Tiêu đề Email Hấp Dẫn', 
$$$Soạn thảo các tiêu đề email thu hút, gây ấn tượng với khán giả của bạn trong ngành dược phẩm. Nâng cao nỗ lực bán hàng và tiếp thị của bạn với các cụm từ nổi bật thúc đẩy sự tương tác và chuyển đổi.$$$, 
$$$Soạn thảo các tiêu đề email thu hút, gây ấn tượng với khán giả của bạn trong ngành dược phẩm. Nâng cao nỗ lực bán hàng và tiếp thị của bạn với các cụm từ nổi bật thúc đẩy sự tương tác và chuyển đổi.$$$, 
'Tiêu đề huyền bí', 
$$$#BỐI CẢNH
Bạn đang cần thực hiện nhiệm vụ: Là một chuyên gia bán hàng và tiếp thị trong ngành dược phẩm, bạn sẽ hỗ trợ tôi trong việc soạn thảo các tiêu đề email hấp dẫn có sức ảnh hưởng đến khán giả mục tiêu của chúng tôi: '[đối tượng mục tiêu]'. Các tiêu đề nên làm nổi bật lợi ích chính của sản phẩm của chúng tôi: '[tên sản phẩm]' và truyền đạt giọng điệu phù hợp với danh tính thương hiệu của chúng tôi: '[giọng điệu]'. Đảm bảo rằng các cụm từ ngắn gọn, hấp dẫn và được thiết kế để thúc đẩy mở và chuyển đổi. Vui lòng trình bày các tiêu đề trong định dạng danh sách rõ ràng.
#VAI TRÒ
Bạn là một chuyên gia trong lĩnh vực tương ứng, có nhiệm vụ phân tích, xử lý hoặc sáng tạo nội dung một cách chính xác, hiệu quả và mang tính thực tiễn.
#HƯỚNG DẪN PHẢN HỒI
1. Làm rõ mục tiêu của yêu cầu.
2. Triển khai phản hồi theo từng bước cụ thể, logic.
3. Nếu phù hợp, gợi ý công cụ hoặc ví dụ minh họa.
4. Ưu tiên cách trình bày rõ ràng, dễ triển khai.
#TIÊU CHÍ
• Chính xác, bám sát yêu cầu.
• Có tính ứng dụng cao, rõ ràng.
• Không quá chung chung hay trừu tượng.
• Giữ giọng điệu chuyên nghiệp và thực tế.
#THÔNG TIN VỀ TÔI
• Đối tượng mục tiêu: [Đối tượng mục tiêu]
• Tên sản phẩm: [Tên sản phẩm]
• Giọng điệu: [Giọng điệu]
#ĐỊNH DẠNG PHẢN HỒI
• Phản hồi có thể được trình bày theo từng bước đánh số.
• Sử dụng bullet points khi cần.
• Ưu tiên cách trình bày dễ đọc, mạch lạc.
• Có thể kết thúc bằng một tóm tắt ngắn hoặc khuyến nghị hành động.$$$, 
54, 329, 1, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-06 15:22:31', '2025-06-06 15:22:31')
ON CONFLICT (id) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
content = EXCLUDED.content,
prompt_text = EXCLUDED.prompt_text,
optimization_guide = EXCLUDED.optimization_guide,
category_id = EXCLUDED.category_id,
topic_id = EXCLUDED.topic_id,
is_type = EXCLUDED.is_type,
sub_type = EXCLUDED.sub_type,
what_field = EXCLUDED.what_field,
tips_field = EXCLUDED.tips_field,
how_field = EXCLUDED.how_field,
input_field = EXCLUDED.input_field,
output_field = EXCLUDED.output_field,
add_tip = EXCLUDED.add_tip,
additional_information = EXCLUDED.additional_information,
view_count = EXCLUDED.view_count,
updated_at = EXCLUDED.updated_at;

-- Check results
SELECT 'Test completed successfully!' as message;
SELECT id, title, category_id, topic_id FROM prompts WHERE id = 4968;
SELECT COUNT(*) as total_prompts FROM prompts; 