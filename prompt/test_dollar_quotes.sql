-- Test dollar quotes functionality
-- Simple test to verify dollar quoting works correctly

-- Test 1: Simple insert with unique dollar tag
INSERT INTO prompts (id, title, optimization_guide, category_id, topic_id, is_type, created_at, updated_at) 
VALUES (99999, 'Test Dollar Quote', 
$tag5cf136e3$#BỐI CẢNH
Bạn đang cần thực hiện nhiệm vụ: Là một chuyên gia bán hàng và tiếp thị trong ngành dược phẩm, bạn sẽ hỗ trợ tôi trong việc soạn thảo các tiêu đề email hấp dẫn có sức ảnh hưởng đến khán giả mục tiêu của chúng tôi: '[đối tượng mục tiêu]'. Các tiêu đề nên làm nổi bật lợi ích chính của sản phẩm của chúng tôi: '[tên sản phẩm]' và truyền đạt giọng điệu phù hợp với danh tính thương hiệu của chúng tôi: '[giọng điệu]'. <PERSON><PERSON><PERSON> bảo rằng các cụm từ ngắn gọn, hấp dẫn và được thiết kế để thúc đẩy mở và chuyển đổi. Vui lòng trình bày các tiêu đề trong định dạng danh sách rõ ràng.
#VAI TRÒ
Bạn là một chuyên gia trong lĩnh vực tương ứng, có nhiệm vụ phân tích, xử lý hoặc sáng tạo nội dung một cách chính xác, hiệu quả và mang tính thực tiễn.
#HƯỚNG DẪN PHẢN HỒI
1. Làm rõ mục tiêu của yêu cầu.
2. Triển khai phản hồi theo từng bước cụ thể, logic.
3. Nếu phù hợp, gợi ý công cụ hoặc ví dụ minh họa.
4. Ưu tiên cách trình bày rõ ràng, dễ triển khai.
#TIÊU CHÍ
• Chính xác, bám sát yêu cầu.
• Có tính ứng dụng cao, rõ ràng.
• Không quá chung chung hay trừu tượng.
• Giữ giọng điệu chuyên nghiệp và thực tế.
#THÔNG TIN VỀ TÔI
• Đối tượng mục tiêu: [Đối tượng mục tiêu]
• Tên sản phẩm: [Tên sản phẩm]
• Giọng điệu: [Giọng điệu]
#ĐỊNH DẠNG PHẢN HỒI
• Phản hồi có thể được trình bày theo từng bước đánh số.
• Sử dụng bullet points khi cần.
• Ưu tiên cách trình bày dễ đọc, mạch lạc.
• Có thể kết thúc bằng một tóm tắt ngắn hoặc khuyến nghị hành động.$tag5cf136e3$, 
54, 329, 1, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
title = EXCLUDED.title,
optimization_guide = EXCLUDED.optimization_guide,
updated_at = NOW();

-- Test 2: Simple text without dollar quotes
INSERT INTO prompts (id, title, optimization_guide, category_id, topic_id, is_type, created_at, updated_at) 
VALUES (99998, 'Test Simple Quote', 'Simple text without special characters', 54, 329, 1, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
title = EXCLUDED.title,
optimization_guide = EXCLUDED.optimization_guide,
updated_at = NOW();

-- Check results
SELECT 'Dollar quote test completed!' as message;
SELECT id, title, LENGTH(optimization_guide) as guide_length FROM prompts WHERE id IN (99999, 99998);

-- Clean up test data
DELETE FROM prompts WHERE id IN (99999, 99998); 