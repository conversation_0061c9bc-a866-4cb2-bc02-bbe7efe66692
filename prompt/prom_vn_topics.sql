-- SQL INSERT Statements for topics
-- Generated at: 2025-06-13 09:32:42
-- Records: 293

-- Disable triggers and constraints temporarily (PostgreSQL)
SET session_replication_role = replica;

-- Clear existing topics data to avoid conflicts
DELETE FROM topics;
ALTER SEQUENCE topics_id_seq RESTART WITH 1;

INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (329, '<PERSON><PERSON><PERSON><PERSON> thị và <PERSON>án hàng <PERSON>ợc phẩm', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (330, 'Sản xuất và Vận hành Chuỗi cung ứng <PERSON>ợc phẩm', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (331, '<PERSON><PERSON><PERSON><PERSON> cứu và <PERSON>t triển <PERSON>c phẩm (R&D)', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (332, 'Giáo dục Bệnh nhân và Tuân thủ Điều trị (Patient Engagement)', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (333, 'Dược lâm sàng và Truyền thông Y khoa (Medical Affairs)', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (334, 'Quản lý Tuân thủ và Pháp lý', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (335, 'Giám sát An toàn Dược phẩm (Pharmacovigilance)', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (336, 'Dịch thuật Tài liệu Y khoa và Kỹ thuật', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (337, 'Dịch thuật Tài liệu Người dùng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (338, 'Đào tạo nội bộ và Phát triển Nhân sự', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (339, 'Phân tích Dữ liệu và Dược lý học', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (340, 'Đảm bảo Chất lượng và Kiểm soát Quy trình', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (341, 'Dịch thuật Tài liệu Khoa học', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (342, 'Bố trí phòng khách', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (343, 'Bàn làm việc theo mệnh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (344, 'Năng lượng theo ngày sinh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (345, 'Chọn màu trang phục', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (346, 'Chọn hướng nhà', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (347, 'Phong thủy phòng ngủ', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (348, 'Trang trí cửa chính', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (349, 'Đặt cây phong thủy', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (350, 'Bếp và hướng bếp', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (351, 'Kích hoạt tài lộc tại bàn làm việc', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (48, 'Chiến lược bán hàng trực tuyến', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (49, 'Phễu bán hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (50, 'Khuyến khích bán hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (51, 'Nuôi dưỡng khách hàng tiềm năng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (52, 'Quản lý quan hệ đối tác', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (53, 'Phân tích thị trường', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (56, 'Quản lý mạng xã hội', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (54, 'Tự động hóa & chuỗi email', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (55, 'Quản lý quan hệ khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (59, 'Chỉ số & KPIs bán hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (57, 'Xây dựng đề xuất bán hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (58, 'Vận hành bán hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (84, 'Tạo khách hàng tiềm năng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (83, 'Quản lý quy trình bán hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (88, 'Quan hệ đối tác & khuyến khích bán hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (87, 'Nghiên cứu tiếp thị', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (85, 'Tương tác với khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (86, 'Xây dựng đề xuất bán hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (39, 'Nghiên cứu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (38, 'Học sinh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (40, 'Phân tích dữ liệu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (41, 'Học tập trọn đời', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (135, 'Giảng dạy', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (42, 'Phát triển chương trình giảng dạy', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (134, 'Học tập', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (131, 'Phát triển chuyên môn', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (43, 'Phát triển sự nghiệp', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (44, 'Học trực tuyến', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (45, 'Học tập kết hợp trò chơi', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (46, 'Học tập cộng tác', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (47, 'Học tập cá nhân hóa', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (132, 'Chiến lược học tập', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (136, 'Giáo dục tại nhà', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (133, 'Nghiên cứu học thuật', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (65, 'Ý tưởng tiếp thị lan truyền', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (69, 'Tiếp thị trên mạng xã hội', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (68, 'Quản lý mạng xã hội', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (61, 'Tự động hóa tiếp thị', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (140, 'Nghiên cứu thị trường', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (141, 'Tiếp thị nội dung', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (138, 'Chiến lược tiếp thị', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (142, 'Tối ưu hóa', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (70, 'Tạo khách hàng tiềm năng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (63, 'Tiếp thị qua người ảnh hưởng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (67, 'Tiếp thị qua email', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (60, 'Tiếp thị kỹ thuật số', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (62, 'Kể chuyện thương hiệu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (66, 'Định vị thương hiệu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (64, 'Tiếp thị liên kết', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (137, 'Xây dựng thương hiệu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (139, 'Quảng cáo', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (21, 'Thương hiệu cá nhân', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (22, 'Việc làm thêm', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (23, 'Sản phẩm kỹ thuật số', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (143, 'Khởi tạo doanh nghiệp', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (147, 'Phễu tiếp thị', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (144, 'Xây dựng thương hiệu & khán giả', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (148, 'Vận hành & tăng trưởng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (146, 'Sáng tạo nội dung', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (24, 'Thiết kế khóa học online', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (25, 'Kỹ thuật khởi nghiệp tinh gọn', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (145, 'Phát triển sản phẩm', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (26, 'Tiếp thị trực tuyến', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (27, 'Tips cho freelancer', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (30, 'Dịch vụ trợ lý ảo', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (35, 'Kiếm Tiền', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (36, 'Nguồn thu nhập thụ động', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (37, 'Bí kíp tăng năng suất', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (126, 'Quản lý đội nhóm', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (122, 'Quản lý rủi ro', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (127, 'Báo cáo kinh doanh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (130, 'Quản lý dự án', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (120, 'Chiến lược xây dựng mạng lưới', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (129, 'Phân tích thị trường', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (33, 'Quản lý kinh doanh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (28, 'Chiến lược kinh doanh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (34, 'Truyền thông kinh doanh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (31, 'Phân tích & Nghiên cứu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (29, 'Phát triển kinh doanh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (32, 'Đổi mới & Tăng trưởng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (124, 'Tạo ý tưởng kinh doanh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (121, 'Viết đề xuất xin tài trợ', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (128, 'Lập kế hoạch nội dung', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (119, 'Lập kế hoạch kinh doanh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (125, 'Vận hành doanh nghiệp', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (123, 'Tự động hóa kinh doanh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (117, 'Viết lách tổng quát', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (113, 'Viết bài blog', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (115, 'Viết đơn xin tài trợ', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (112, 'Viết đề xuất', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (110, 'Viết quảng cáo phản hồi trực tiếp', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (111, 'Viết lại nội dung', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (109, 'Viết bài phát biểu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (114, 'Hiệu đính nội dung', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (108, 'Viết ẩn danh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (116, 'Viết sách', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (118, 'Viết nội dung', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (158, 'Viết sáng tạo', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (160, 'Viết quảng cáo', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (155, 'Viết học thuật', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (157, 'Viết tài liệu kỹ thuật', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (161, 'Báo chí', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (156, 'Viết nội dung pháp lý', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (159, 'Viết theo chân dung khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (71, 'Phương pháp tăng năng suất', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (72, 'Công cụ hỗ trợ tăng năng suất', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (79, 'Ngăn ngừa kiệt sức', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (76, 'Sắp xếp nhiệm vụ ưu tiên', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (81, 'Tối ưu quy trình làm việc', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (73, 'Brainstorm ý tưởng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (74, 'Phát triển bản thân', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (78, 'Tự kinh doanh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (77, 'Lập kế hoạch du lịch', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (75, 'Lên kế hoạch cho bữa ăn', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (80, 'Cân bằng công việc và cuộc sống', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (82, 'Phát triển chuyên môn', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (104, 'Đặt mục tiêu & theo dõi tiến độ', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (102, 'Quản lý công việc', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (105, 'Phát triển bản thân', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (107, 'Quản lý căng thẳng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (103, 'Quản lý thời gian', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (98, 'Xây dựng liên kết', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (90, 'Kiểm tra kỹ thuật SEO', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (92, 'Phân tích đối thủ cạnh tranh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (95, 'SEO video', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (89, 'SEO thương mại điện tử', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (100, 'SEO nội dung', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (96, 'SEO trên thiết bị di động', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (99, 'Nghiên cứu từ khóa', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (101, 'SEO tự động hóa', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (91, 'Tối ưu tìm kiếm bằng giọng nói', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (93, 'Xây dựng liên kết SEO', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (94, 'Đánh dấu Schema', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (97, 'Quản lý thông tin doanh nghiệp địa phương', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (150, 'Phân tích SEO', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (151, 'SEO trực tuyến', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (149, 'SEO cơ bản', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (154, 'SEO địa phương', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (153, 'Kỹ thuật SEO', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (152, 'SEO ngoại tuyến', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (292, 'Prompt Chuyên Dùng - Hiếu AI', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (256, 'Quản lý dự án CNTT', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (257, 'Tối ưu hóa mã nguồn', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (258, 'Ứng dụng trí tuệ nhân tạo và công nghệ máy học', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (259, 'Đảm bảo an ninh mạng và tuân thủ chính sách', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (260, 'Phát triển phần mềm và tối ưu hóa mã nguồn', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (261, 'Quản lý cơ sở dữ liệu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (262, 'Thiết kế UI/UX và tối ưu trải nghiệm người dùng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (263, 'Phân tích dữ liệu và ứng dụng khoa học dữ liệu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (264, 'Điện toán đám mây và hạ tầng CNTT', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (265, 'Quản trị hoạt động kỹ thuật và vận hành', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (266, 'Dịch thuật và truyền đạt kiến thức kỹ thuật', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (267, 'Thông tin công nghệ', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (268, 'Đào tạo và phát triển nhân lực CNTT', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (293, 'Quản lý phản hồi và khảo sát khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (294, 'Marketing và quảng bá dịch vụ khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (295, 'Truyền thông xã hội và quản lý cộng đồng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (296, 'Cập nhật đơn hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (297, 'Giao tiếp qua email và tin nhắn tự động', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (298, 'Gửi email chào mừng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (299, 'Hỗ trợ kỹ thuật và giải đáp sự cố', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (300, 'Tối ưu hóa trải nghiệm khách hàng và cá nhân hóa tương tác', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (301, 'Dịch thuật và bản địa hóa nội dung chăm sóc khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (302, 'Phân tích sở thích giao hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (303, 'Đào tạo và phát triển nhân sự hỗ trợ khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (304, 'Quản lý vé hỗ trợ và phân loại yêu cầu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (305, 'Phân tích xu hướng thanh toán', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (306, 'Quản lý nội dung và cơ sở tri thức', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (307, 'Giải thích chính sách hoàn trả', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (308, 'Quản lý khiếu nại và giải quyết sự cố', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (309, 'Dịch vụ bán hàng và chăm sóc khách hàng tiềm năng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (310, 'Giáo dục cho khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (311, 'Quản lý phiếu hỗ trợ và phân loại yêu cầu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (312, 'Tối ưu hóa quy trình thanh toán', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (313, 'Dịch vụ khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (314, 'Quản lý khiếu nại và khảo sát khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (315, 'Kiểm tra và khảo sát khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (316, 'Quản lý khiếu nại và dịch vụ hậu mãi', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (317, 'Dịch vụ hậu mãi và quản lý khiếu nại', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (203, 'Phân tích dữ liệu và dự đoán nhu cầu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (204, 'Tiếp thị và bán hàng trực tuyến', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (205, 'Hệ thống gợi ý và đề xuất sản phẩm', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (206, 'Hỗ trợ khách hàng và dịch vụ hậu mãi', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (207, 'Tối ưu hóa nội dung và SEO', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (208, 'Cá nhân hóa trải nghiệm khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (209, 'Phát hiện gian lận và Bảo mật', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (210, 'Dịch thuật và Đa ngôn ngữ', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (211, 'Quản lý hoàn trả và hoàn tiền', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (212, 'Quản lý sản phẩm và danh mục', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (213, 'Phân khúc khách hàng và Thấu hiểu hành vi người tiêu dùng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (214, 'Thương mại điện tử', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (215, 'Quản lý chuỗi cung ứng và tồn kho', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (216, 'Trải nghiệm người dùng (UX) và Giao diện mua sắm', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (217, 'Gợi ý sản phẩm thay thế', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (218, 'Dự đoán nhu cầu và Phân tích dữ liệu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (219, 'Tối ưu hóa chi phí cung ứng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (220, 'Quản lý đơn hàng và danh mục', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (221, 'Giáo dục và nâng cao nhận thức sức khỏe', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (222, 'Hướng dẫn điều trị và chăm sóc da liễu', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (223, 'Hỗ trợ điều trị ung thư và giảm tác dụng phụ', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (224, 'Chẩn đoán và điều trị bệnh lý', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (225, 'Dịch vụ y tế nhi khoa và tiêm chủng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (226, 'Chăm sóc và phòng ngừa bệnh mãn tính', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (227, 'Tư vấn lâm sàng và phối hợp đa chuyên khoa', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (228, 'Hỗ trợ tâm lý và sức khỏe tinh thần', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (229, 'Hỗ trợ điều trị các bệnh truyền nhiễm và kiểm soát dịch bệnh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (230, 'Chăm sóc cấp cứu và hồi sức', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (231, 'Đảm bảo sự sạch sẽ trong phòng chăm sóc tích cực', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (232, 'Cung cấp chăm sóc tim mạch khẩn cấp', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (233, 'Chăm sóc ICU và hồi sức tích cực', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (234, 'Quản lý các tình huống cấp cứu ở trẻ em', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (235, 'Ổn định bệnh nhân nguy kịch', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (236, 'Truyền thông y tế và giao tiếp với bệnh nhân', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (237, 'Quản lý thuốc', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (238, 'Quản lý điều trị khẩn cấp', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (239, 'Phân tích dữ liệu y tế và nghiên cứu lâm sàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (240, 'Quản lý chăm sóc vết thương', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (198, 'Xử lý khiếu nại và chăm sóc khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (199, 'Quản lý sự cố và quy trình vận hành', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (200, 'Chiến lược đầu tư và truyền thông tài chính', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (201, 'Đánh giá tín dụng và phê duyệt tài chính', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (202, 'Phân tích dữ liệu và báo cáo', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (318, 'Quản lý cộng đồng và chăm sóc hội viên', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (319, 'Marketing và quản lý thương hiệu Fitness', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (320, 'Dinh dưỡng và lối sống lành mạnh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (321, 'Huấn luyện cá nhân và xây dựng chương trình tập luyện', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (322, 'Tổ chức sự kiện và hoạt động cộng đồng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (323, 'Quản lý cơ sở vật chất và thiết bị tập luyện', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (324, 'Phục hồi chức năng và phòng ngừa chấn thương', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (325, 'Giáo dục thể chất và chứng nhận chuyên môn', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (326, 'Phân tích dữ liệu và đo lường tiến trình tập luyện', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (327, 'Dịch thuật và bản địa hóa nội dung Fitness', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (328, 'Quản lý nội dung kỹ thuật số và mạng xã hội ngành Fitness', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (269, 'Tiếp thị và quảng bá du lịch', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (270, 'Quản lý nội dung và chiến lược trên mạng xã hội', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (271, 'Biên dịch và điều chỉnh nội dung theo từng thị trường', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (272, 'Hỗ trợ và chăm sóc khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (273, 'Tối ưu hóa công cụ tìm kiếm (SEO)', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (274, 'Tạo nội dung và Viết sáng tạo', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (275, 'Tư vấn và Gợi ý cá nhân hóa', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (276, 'Cung cấp thông tin và hướng dẫn du lịch', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (277, 'Quản lý đặt chỗ và hành trình khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (278, 'Phân tích dữ liệu và thấu hiểu khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (279, 'Ứng dụng tự động hóa và AI trong du lịch', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (280, 'Quản lý danh tiếng thương hiệu và xử lý phản hồi khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (241, 'Tiếp thị và phân phối sản phẩm bảo hiểm', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (242, 'Chăm sóc khách hàng và duy trì khách hàng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (243, 'Phân tích dữ liệu và quản trị rủi ro', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (244, 'Điều tra và xử lý yêu cầu bồi thường', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (245, 'Tối ưu hóa nội dung và SEO cho ngành bảo hiểm', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (246, 'Phát triển sản phẩm bảo hiểm', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (247, 'Phát hiện gian lận và kiểm soát tổn thất', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (248, 'Thiết kế chương trình khách hàng thân thiết và win-back', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (249, 'Tuân thủ pháp lý và báo cáo quy định', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (250, 'Dịch thuật và truyền đạt tài liệu chuyên ngành', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (251, 'Quản lý rủi ro và kiểm soát tổn thất', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (252, 'Quản lý đơn bảo hiểm và hành chính hợp đồng', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (253, 'Theo dõi và xử lý yêu cầu bồi thường', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (254, 'Dịch vụ khách hàng và hỗ trợ tài chính', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (255, 'Quản lý đại lý và đội ngũ tư vấn', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (281, 'Cân bằng Nash và Chiến lược hỗn hợp', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (286, 'Áp dụng cân bằng Nash hỗn hợp để tối ưu chiến lược', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (283, 'Dilemma của Tù nhân và Stag Hunt', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (287, 'Chicken Game', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (288, 'Pareto Optimal', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (282, 'Trò chơi lặp và Chiến lược Tit-for-Tat', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (284, 'Trò chơi hợp tác và Trò chơi Zero-Sum', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (289, 'Signaling', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (290, 'Screening', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();
INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES (285, 'Đấu giá và Cạnh tranh', NULL, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();

-- Reset sequence for topics after custom ID insertion
SELECT setval(pg_get_serial_sequence('topics', 'id'), 351, true);

-- Re-enable triggers and constraints (PostgreSQL)
SET session_replication_role = default;
