-- Verification queries for imported prompt data
-- Run these queries after importing data to verify integrity

-- 1. Check total records in each table
SELECT 'prompt_categories' as table_name, COUNT(*) as record_count FROM prompt_categories
UNION ALL
SELECT 'topics' as table_name, COUNT(*) as record_count FROM topics
UNION ALL
SELECT 'prompts' as table_name, COUNT(*) as record_count FROM prompts;

-- 2. Check foreign key integrity
SELECT 
    'Prompts with invalid category_id' as check_name,
    COUNT(*) as count
FROM prompts p
LEFT JOIN prompt_categories pc ON p.category_id = pc.id
WHERE p.category_id IS NOT NULL AND pc.id IS NULL

UNION ALL

SELECT 
    'Prompts with invalid topic_id' as check_name,
    COUNT(*) as count
FROM prompts p
LEFT JOIN topics t ON p.topic_id = t.id
WHERE p.topic_id IS NOT NULL AND t.id IS NULL;

-- 3. Check sequence values
SELECT 
    'prompt_categories_sequence' as sequence_name,
    last_value as current_value,
    is_called
FROM prompt_categories_id_seq

UNION ALL

SELECT 
    'topics_sequence' as sequence_name,
    last_value as current_value,
    is_called
FROM topics_id_seq

UNION ALL

SELECT 
    'prompts_sequence' as sequence_name,
    last_value as current_value,
    is_called
FROM prompts_id_seq;

-- 4. Check max IDs in each table
SELECT 
    'prompt_categories_max_id' as table_name,
    MAX(id) as max_id
FROM prompt_categories

UNION ALL

SELECT 
    'topics_max_id' as table_name,
    MAX(id) as max_id
FROM topics

UNION ALL

SELECT 
    'prompts_max_id' as table_name,
    MAX(id) as max_id
FROM prompts;

-- 5. Sample data from each table
SELECT 'Categories sample:' as info;
SELECT id, name, prompt_count FROM prompt_categories ORDER BY id LIMIT 5;

SELECT 'Topics sample:' as info;
SELECT id, name FROM topics ORDER BY id LIMIT 5;

SELECT 'Prompts sample:' as info;
SELECT id, title, category_id, topic_id FROM prompts ORDER BY id LIMIT 5;

-- 6. Check relationship statistics
SELECT 
    pc.name as category_name,
    COUNT(p.id) as prompt_count_actual,
    pc.prompt_count as prompt_count_stored
FROM prompt_categories pc
LEFT JOIN prompts p ON pc.id = p.category_id
GROUP BY pc.id, pc.name, pc.prompt_count
ORDER BY pc.name; 