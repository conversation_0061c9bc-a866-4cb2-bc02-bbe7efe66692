import json
import re
from datetime import datetime

class SafeEscapeSQLGenerator:
    def __init__(self, json_file_path):
        self.json_file_path = json_file_path
        self.data = self.load_json_data()
        
    def load_json_data(self):
        """Load dữ liệu từ file JSON"""
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading JSON: {e}")
            return None
    
    def escape_sql_string(self, value):
        """Traditional escaping only - no dollar quotes"""
        if value is None:
            return 'NULL'
        
        # Convert to string
        str_value = str(value)
        
        # Handle various null-like values
        if str_value.lower() in ['none', 'null', 'undefined', 'nan', '']:
            return 'NULL'
        
        # Remove or replace problematic characters
        str_value = str_value.replace('\x00', '')  # Remove null bytes
        str_value = str_value.replace('\ufffd', '')  # Remove replacement characters
        str_value = str_value.replace('\r\n', ' ')  # Replace line breaks with spaces
        str_value = str_value.replace('\r', ' ')    # Replace line breaks with spaces
        str_value = str_value.replace('\n', ' ')    # Replace line breaks with spaces
        str_value = str_value.replace('\t', ' ')    # Replace tabs with spaces
        
        # Remove HTML tags
        str_value = re.sub(r'<[^>]+>', '', str_value)
        
        # Escape single quotes by doubling them
        str_value = str_value.replace("'", "''")
        
        # Escape backslashes
        str_value = str_value.replace("\\", "\\\\")
        
        # Remove or replace other special characters that might cause issues
        str_value = re.sub(r'[^\w\s\.,!?;:()\[\]{}+=@#$%^&*/-]', ' ', str_value)
        
        # Normalize multiple spaces
        str_value = re.sub(r'\s+', ' ', str_value)
        str_value = str_value.strip()
        
        # Limit length to prevent too long strings
        if len(str_value) > 3000:
            str_value = str_value[:3000] + "..."
        
        return f"'{str_value}'"
    
    def safe_format_value(self, value, field_name=""):
        """Safely format any value for SQL"""
        if value is None:
            return 'NULL'
        
        # Handle numeric values
        if isinstance(value, (int, float)) and not isinstance(value, bool):
            return str(value)
        
        # Handle boolean values
        if isinstance(value, bool):
            return 'TRUE' if value else 'FALSE'
        
        # Handle string values
        return self.escape_sql_string(value)
    
    def format_datetime(self, datetime_str):
        """Format datetime cho SQL"""
        if not datetime_str:
            return 'NOW()'
        
        try:
            # Parse ISO datetime
            dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            return f"'{dt.strftime('%Y-%m-%d %H:%M:%S')}'"
        except:
            return 'NOW()'
    
    def write_sql_header(self, f, table_name, record_count):
        """Write SQL file header"""
        f.write(f"-- SQL INSERT Statements for {table_name}\n")
        f.write(f"-- Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"-- Records: {record_count}\n")
        f.write("-- Using traditional escaping (no dollar quotes)\n\n")
        f.write("-- Disable triggers and constraints temporarily (PostgreSQL)\n")
        f.write("SET session_replication_role = replica;\n\n")
        
        # Clear existing data first to avoid conflicts
        if table_name == 'prompts':
            f.write("-- Clear existing prompts data to avoid conflicts\n")
            f.write("DELETE FROM prompts;\n")
            f.write("ALTER SEQUENCE prompts_id_seq RESTART WITH 1;\n\n")
        elif table_name == 'topics':
            f.write("-- Clear existing topics data to avoid conflicts\n") 
            f.write("DELETE FROM topics;\n")
            f.write("ALTER SEQUENCE topics_id_seq RESTART WITH 1;\n\n")
        elif table_name == 'prompt_categories':
            f.write("-- Clear existing categories data to avoid conflicts\n")
            f.write("DELETE FROM prompt_categories;\n") 
            f.write("ALTER SEQUENCE prompt_categories_id_seq RESTART WITH 1;\n\n")
    
    def write_sql_footer(self, f, table_name, max_id):
        """Write SQL file footer with sequence reset"""
        f.write(f"\n-- Reset sequence for {table_name} after custom ID insertion\n")
        if table_name == 'prompt_categories':
            f.write(f"SELECT setval(pg_get_serial_sequence('prompt_categories', 'id'), {max_id}, true);\n")
        elif table_name == 'topics':
            f.write(f"SELECT setval(pg_get_serial_sequence('topics', 'id'), {max_id}, true);\n")
        elif table_name == 'prompts':
            f.write(f"SELECT setval(pg_get_serial_sequence('prompts', 'id'), {max_id}, true);\n")
        
        f.write("\n-- Re-enable triggers and constraints (PostgreSQL)\n")
        f.write("SET session_replication_role = default;\n")
    
    def generate_prompts_inserts(self):
        """Generate INSERT statements cho bảng prompts với traditional escaping"""
        if not self.data or 'categories' not in self.data:
            return [], 0
        
        inserts = []
        max_id = 0
        
        for category_data in self.data['categories']:
            prompts = category_data.get('prompts', [])
            
            for prompt in prompts:
                prompt_id = prompt.get('id')
                if prompt_id:
                    max_id = max(max_id, prompt_id)
                    
                title = self.safe_format_value(prompt.get('title'))
                short_description = self.safe_format_value(prompt.get('short_description'))
                content = self.safe_format_value(prompt.get('content'))
                prompt_text = self.safe_format_value(prompt.get('text'))
                optimization_guide = self.safe_format_value(prompt.get('OptimationGuide'))
                
                # Lấy category_id và topic_id trực tiếp từ dữ liệu với null handling
                category_id = self.safe_format_value(prompt.get('category_id'))
                topic_id = self.safe_format_value(prompt.get('topic_id'))
                
                is_type = self.safe_format_value(prompt.get('is_type', 1))
                sub_type = self.safe_format_value(prompt.get('sub_type'))
                what_field = self.safe_format_value(prompt.get('what'))
                tips_field = self.safe_format_value(prompt.get('tips'))
                how_field = self.safe_format_value(prompt.get('how'))
                input_field = self.safe_format_value(prompt.get('input'))
                output_field = self.safe_format_value(prompt.get('output'))
                add_tip = self.safe_format_value(prompt.get('addtip'))
                additional_information = self.safe_format_value(prompt.get('addinformation'))
                view_count = self.safe_format_value(prompt.get('sum_view', 0))
                created_at = self.format_datetime(prompt.get('created_at'))
                updated_at = self.format_datetime(prompt.get('updated_at'))
                
                # Insert với traditional escaping
                sql = f"""INSERT INTO prompts (id, title, short_description, content, prompt_text, optimization_guide, category_id, topic_id, is_type, sub_type, what_field, tips_field, how_field, input_field, output_field, add_tip, additional_information, view_count, created_at, updated_at) 
VALUES ({prompt_id}, {title}, {short_description}, {content}, {prompt_text}, {optimization_guide}, {category_id}, {topic_id}, {is_type}, {sub_type}, {what_field}, {tips_field}, {how_field}, {input_field}, {output_field}, {add_tip}, {additional_information}, {view_count}, {created_at}, {updated_at})
ON CONFLICT (id) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
content = EXCLUDED.content,
prompt_text = EXCLUDED.prompt_text,
optimization_guide = EXCLUDED.optimization_guide,
category_id = EXCLUDED.category_id,
topic_id = EXCLUDED.topic_id,
is_type = EXCLUDED.is_type,
sub_type = EXCLUDED.sub_type,
what_field = EXCLUDED.what_field,
tips_field = EXCLUDED.tips_field,
how_field = EXCLUDED.how_field,
input_field = EXCLUDED.input_field,
output_field = EXCLUDED.output_field,
add_tip = EXCLUDED.add_tip,
additional_information = EXCLUDED.additional_information,
view_count = EXCLUDED.view_count,
updated_at = EXCLUDED.updated_at;"""
                
                inserts.append(sql)
        
        return inserts, max_id
    
    def generate_safe_prompts_file(self, filename='prom_vn_prompts_safe.sql'):
        """Generate safe prompts file with traditional escaping only"""
        print("Generating safe prompts SQL with traditional escaping...")
        
        prompts_inserts, prompts_max_id = self.generate_prompts_inserts()
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                self.write_sql_header(f, "prompts", len(prompts_inserts))
                for insert in prompts_inserts:
                    f.write(insert + '\n')
                self.write_sql_footer(f, "prompts", prompts_max_id)
            print(f"✅ Safe prompts file created: {filename} (max_id: {prompts_max_id})")
            print(f"📊 Records: {len(prompts_inserts)}")
        except Exception as e:
            print(f"❌ Error saving safe prompts file: {e}")

def main():
    generator = SafeEscapeSQLGenerator('prom_vn_data_20250609_155410.json')
    
    if generator.data is None:
        print("❌ Cannot load JSON data.")
        return
    
    # Generate safe prompts file
    generator.generate_safe_prompts_file()

if __name__ == "__main__":
    main() 