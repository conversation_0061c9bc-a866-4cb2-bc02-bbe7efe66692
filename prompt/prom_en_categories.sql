-- SQL INSERT Statements for prompt_categories_translations (English)
-- Generated at: 2025-01-27 10:00:00
-- Records: 20

-- Disable triggers and constraints temporarily (PostgreSQL)
SET session_replication_role = replica;

-- Clear existing English translations to avoid conflicts
DELETE FROM prompt_categories_translations WHERE lang = 'en';

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (54, 'en', 'Pharmaceuticals', 'Pharmaceuticals', NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (53, 'en', 'Feng Shui', NULL, NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (8, 'en', 'Sales', NULL, NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (1, 'en', 'Education', NULL, NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (2, 'en', 'Marketing', NULL, NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (3, 'en', 'Startup', NULL, NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (4, 'en', 'Business', NULL, NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (5, 'en', 'Writing', NULL, NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (6, 'en', 'Productivity', NULL, NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (7, 'en', 'SEO', NULL, NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (16, 'en', 'Video Making - Hieu AI', NULL, NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (17, 'en', 'Information Technology', NULL, NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (18, 'en', 'Customer Service', 'Customer Service', NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (19, 'en', 'E-commerce', 'E-commerce', NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (20, 'en', 'Healthcare', 'Healthcare', NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (22, 'en', 'Finance', 'Finance', NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (23, 'en', 'Fitness', 'Fitness', NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (24, 'en', 'Travel', 'Travel', NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (27, 'en', 'Insurance', 'Insurance', NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

INSERT INTO prompt_categories_translations (category_id, lang, title, short_description, created_at, updated_at) 
VALUES (28, 'en', 'Game Theory', NULL, NOW(), NOW())
ON CONFLICT (category_id, lang) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
updated_at = EXCLUDED.updated_at;

-- Re-enable triggers and constraints (PostgreSQL)
SET session_replication_role = default;
