import json
import re

def find_problematic_records():
    """Tìm records có thể gây ra lỗi SQL"""
    try:
        with open('prom_vn_data_20250609_155410.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"Error loading JSON: {e}")
        return

    print("🔍 Analyzing data for potential SQL issues...")
    
    problem_count = 0
    total_prompts = 0
    
    for category_data in data['categories']:
        prompts = category_data.get('prompts', [])
        
        for prompt in prompts:
            total_prompts += 1
            prompt_id = prompt.get('id')
            
            # Check for problematic values
            issues = []
            
            # Check each field for potential issues
            fields_to_check = [
                'title', 'short_description', 'content', 'text', 'OptimationGuide',
                'what', 'tips', 'how', 'input', 'output', 'addtip', 'addinformation'
            ]
            
            for field in fields_to_check:
                value = prompt.get(field)
                if value is not None:
                    str_value = str(value)
                    # Check for unquoted none/null values
                    if str_value.lower() in ['none', 'undefined'] and value != 'none':
                        issues.append(f"{field}: contains unquoted '{str_value}'")
                    # Check for very long values
                    elif len(str_value) > 10000:
                        issues.append(f"{field}: very long ({len(str_value)} chars)")
                    # Check for special characters that might cause issues
                    elif any(char in str_value for char in ['\x00', '\ufffd']):
                        issues.append(f"{field}: contains special chars")
            
            # Check numeric fields
            if prompt.get('category_id') is not None:
                cat_id = str(prompt.get('category_id'))
                if cat_id.lower() in ['none', 'undefined']:
                    issues.append(f"category_id: invalid value '{cat_id}'")
            
            if prompt.get('topic_id') is not None:
                top_id = str(prompt.get('topic_id'))
                if top_id.lower() in ['none', 'undefined']:
                    issues.append(f"topic_id: invalid value '{top_id}'")
            
            if issues:
                problem_count += 1
                print(f"\n❌ Prompt ID {prompt_id} has issues:")
                for issue in issues:
                    print(f"   - {issue}")
                
                if problem_count <= 5:  # Show first 5 problematic records in detail
                    print(f"   Full record: {json.dumps(prompt, indent=2, ensure_ascii=False)[:500]}...")
    
    print(f"\n📊 Summary:")
    print(f"   - Total prompts: {total_prompts}")
    print(f"   - Problematic records: {problem_count}")
    
    if problem_count == 0:
        print("✅ No obvious issues found in data")
    else:
        print(f"⚠️  Found {problem_count} records with potential issues")

def check_specific_position():
    """Check around position 1750 where error occurred"""
    print("\n🎯 Checking SQL generation around error position...")
    
    # Generate first few prompts to see the structure
    from generate_sql import SQLGenerator
    
    generator = SQLGenerator('prom_vn_data_20250609_155410.json')
    if generator.data:
        # Get first category and first few prompts
        category_data = generator.data['categories'][0]
        prompts = category_data.get('prompts', [])[:3]  # First 3 prompts
        
        for i, prompt in enumerate(prompts):
            print(f"\n--- Prompt {i+1} (ID: {prompt.get('id')}) ---")
            
            # Show what each field would become in SQL
            fields = ['title', 'what', 'tips', 'how', 'input', 'output', 'addtip', 'addinformation']
            for field in fields:
                value = prompt.get(field)
                escaped = generator.escape_sql_string(value)
                print(f"{field}: {value} -> {escaped}")

if __name__ == "__main__":
    find_problematic_records()
    check_specific_position() 