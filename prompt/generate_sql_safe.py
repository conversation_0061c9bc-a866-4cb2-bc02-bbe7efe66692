import json
import re
from datetime import datetime

class SafeSQLGenerator:
    def __init__(self, json_file_path):
        self.json_file_path = json_file_path
        self.data = self.load_json_data()
        
    def load_json_data(self):
        """Load dữ liệu từ file JSON"""
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading JSON: {e}")
            return None
    
    def escape_sql_string(self, value):
        """Escape string cho SQL với xử lý tốt hơn"""
        if value is None:
            return 'NULL'
        
        # Convert to string
        str_value = str(value)
        
        # Handle various null-like values
        if str_value.lower() in ['none', 'null', 'undefined', 'nan', '']:
            return 'NULL'
        
        # Remove or replace problematic characters
        str_value = str_value.replace('\x00', '')  # Remove null bytes
        str_value = str_value.replace('\ufffd', '')  # Remove replacement characters
        str_value = str_value.replace('\r\n', '\n')  # Normalize line endings
        str_value = str_value.replace('\r', '\n')    # Normalize line endings
        
        # Remove HTML tags
        str_value = re.sub(r'<[^>]+>', '', str_value)
        
        # Escape single quotes by doubling them
        str_value = str_value.replace("'", "''")
        
        # Escape backslashes
        str_value = str_value.replace("\\", "\\\\")
        
        # Limit length to prevent too long strings
        if len(str_value) > 5000:
            str_value = str_value[:5000] + "..."
        
        return f"'{str_value}'"
    
    def safe_format_value(self, value, field_name=""):
        """Safely format any value for SQL"""
        if value is None:
            return 'NULL'
        
        # Handle numeric values
        if isinstance(value, (int, float)) and not isinstance(value, bool):
            return str(value)
        
        # Handle boolean values
        if isinstance(value, bool):
            return 'TRUE' if value else 'FALSE'
        
        # Handle string values
        return self.escape_sql_string(value)
    
    def format_datetime(self, datetime_str):
        """Format datetime cho SQL"""
        if not datetime_str:
            return 'NOW()'
        
        try:
            # Parse ISO datetime
            dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            return f"'{dt.strftime('%Y-%m-%d %H:%M:%S')}'"
        except:
            return 'NOW()'
    
    def generate_test_insert(self):
        """Generate a simple test insert để kiểm tra"""
        if not self.data or 'categories' not in self.data:
            return "-- No data found"
        
        # Get first prompt
        category_data = self.data['categories'][0]
        prompt = category_data.get('prompts', [])[0] if category_data.get('prompts') else None
        
        if not prompt:
            return "-- No prompts found"
        
        prompt_id = prompt.get('id', 1)
        title = self.safe_format_value(prompt.get('title'), 'title')
        
        sql = f"""-- Test INSERT
INSERT INTO prompts (id, title, short_description, content, prompt_text, optimization_guide, category_id, topic_id, is_type, sub_type, what_field, tips_field, how_field, input_field, output_field, add_tip, additional_information, view_count, created_at, updated_at) 
VALUES (
    {prompt_id}, 
    {title},
    {self.safe_format_value(prompt.get('short_description'), 'short_description')},
    {self.safe_format_value(prompt.get('content'), 'content')},
    {self.safe_format_value(prompt.get('text'), 'text')},
    {self.safe_format_value(prompt.get('OptimationGuide'), 'OptimationGuide')},
    {self.safe_format_value(prompt.get('category_id'), 'category_id')},
    {self.safe_format_value(prompt.get('topic_id'), 'topic_id')},
    {self.safe_format_value(prompt.get('is_type', 1), 'is_type')},
    {self.safe_format_value(prompt.get('sub_type'), 'sub_type')},
    {self.safe_format_value(prompt.get('what'), 'what')},
    {self.safe_format_value(prompt.get('tips'), 'tips')},
    {self.safe_format_value(prompt.get('how'), 'how')},
    {self.safe_format_value(prompt.get('input'), 'input')},
    {self.safe_format_value(prompt.get('output'), 'output')},
    {self.safe_format_value(prompt.get('addtip'), 'addtip')},
    {self.safe_format_value(prompt.get('addinformation'), 'addinformation')},
    {self.safe_format_value(prompt.get('sum_view', 0), 'sum_view')},
    {self.format_datetime(prompt.get('created_at'))},
    {self.format_datetime(prompt.get('updated_at'))}
)
ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    short_description = EXCLUDED.short_description,
    content = EXCLUDED.content,
    prompt_text = EXCLUDED.prompt_text,
    optimization_guide = EXCLUDED.optimization_guide,
    category_id = EXCLUDED.category_id,
    topic_id = EXCLUDED.topic_id,
    is_type = EXCLUDED.is_type,
    sub_type = EXCLUDED.sub_type,
    what_field = EXCLUDED.what_field,
    tips_field = EXCLUDED.tips_field,
    how_field = EXCLUDED.how_field,
    input_field = EXCLUDED.input_field,
    output_field = EXCLUDED.output_field,
    add_tip = EXCLUDED.add_tip,
    additional_information = EXCLUDED.additional_information,
    view_count = EXCLUDED.view_count,
    updated_at = EXCLUDED.updated_at;

-- Check the result
SELECT id, title, LENGTH(optimization_guide) as guide_length FROM prompts WHERE id = {prompt_id};
"""
        return sql

def main():
    generator = SafeSQLGenerator('prom_vn_data_20250609_155410.json')
    
    if generator.data is None:
        print("❌ Cannot load JSON data.")
        return
    
    # Generate test SQL
    test_sql = generator.generate_test_insert()
    
    with open('test_safe_insert.sql', 'w', encoding='utf-8') as f:
        f.write(test_sql)
    
    print("✅ Test SQL file created: test_safe_insert.sql")
    print("Run this file first to test if the escaping works correctly.")

if __name__ == "__main__":
    main() 