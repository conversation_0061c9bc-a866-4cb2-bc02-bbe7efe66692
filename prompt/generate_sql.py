import json
import re
from datetime import datetime

class SQLGenerator:
    def __init__(self, json_file_path):
        self.json_file_path = json_file_path
        self.data = self.load_json_data()
        
    def load_json_data(self):
        """Load dữ liệu từ file JSON"""
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading JSON: {e}")
            return None
    
    def escape_sql_string(self, value, preserve_html=False):
        """Escape string cho SQL để tránh lỗi injection"""
        if value is None:
            return 'NULL'

        # Convert to string
        str_value = str(value)

        # Handle various null-like values
        if str_value.lower() in ['none', 'null', 'undefined', 'nan', '']:
            return 'NULL'

        # Remove or replace problematic characters
        str_value = str_value.replace('\x00', '')  # Remove null bytes
        str_value = str_value.replace('\ufffd', '')  # Remove replacement characters

        # Remove HTML tags only if preserve_html is False
        if not preserve_html:
            str_value = re.sub(r'<[^>]+>', '', str_value)

        # For very long strings, try dollar quoting with unique tag
        if len(str_value) > 1000:
            # Generate unique dollar quote tag to avoid conflicts
            import hashlib
            hash_tag = hashlib.md5(str_value[:100].encode()).hexdigest()[:8]
            dollar_tag = f"$tag{hash_tag}$"

            # Check if the tag appears in the content
            if dollar_tag not in str_value:
                return f"{dollar_tag}{str_value}{dollar_tag}"

        # Fallback to traditional escaping for all other cases
        # Escape single quotes by doubling them
        str_value = str_value.replace("'", "''")

        # Escape backslashes if needed (but preserve \r\n if preserve_html is True)
        if '\\' in str_value:
            if preserve_html:
                # Only escape backslashes that are not part of \r\n sequences
                str_value = re.sub(r'\\(?![rn])', r'\\\\', str_value)
            else:
                str_value = str_value.replace("\\", "\\\\")

        # Limit length to prevent too long strings
        if len(str_value) > 5000:
            str_value = str_value[:5000] + "..."

        return f"'{str_value}'"

    def escape_optimization_guide(self, value):
        """Special method to escape optimization_guide field while preserving HTML structure and \r\n"""
        return self.escape_sql_string(value, preserve_html=True)
    
    def format_datetime(self, datetime_str):
        """Format datetime cho SQL"""
        if not datetime_str:
            return 'NOW()'
        
        try:
            # Parse ISO datetime
            dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            return f"'{dt.strftime('%Y-%m-%d %H:%M:%S')}'"
        except:
            return 'NOW()'
    
    def write_sql_header(self, f, table_name, record_count):
        """Write SQL file header"""
        f.write(f"-- SQL INSERT Statements for {table_name}\n")
        f.write(f"-- Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"-- Records: {record_count}\n\n")
        f.write("-- Disable triggers and constraints temporarily (PostgreSQL)\n")
        f.write("SET session_replication_role = replica;\n\n")
        
        # Clear existing data first to avoid conflicts
        if table_name == 'prompts':
            f.write("-- Clear existing prompts data to avoid conflicts\n")
            f.write("DELETE FROM prompts;\n")
            f.write("ALTER SEQUENCE prompts_id_seq RESTART WITH 1;\n\n")
        elif table_name == 'topics':
            f.write("-- Clear existing topics data to avoid conflicts\n") 
            f.write("DELETE FROM topics;\n")
            f.write("ALTER SEQUENCE topics_id_seq RESTART WITH 1;\n\n")
        elif table_name == 'prompt_categories':
            f.write("-- Clear existing categories data to avoid conflicts\n")
            f.write("DELETE FROM prompt_categories;\n") 
            f.write("ALTER SEQUENCE prompt_categories_id_seq RESTART WITH 1;\n\n")
    
    def write_sql_footer(self, f, table_name, max_id):
        """Write SQL file footer with sequence reset"""
        f.write(f"\n-- Reset sequence for {table_name} after custom ID insertion\n")
        if table_name == 'prompt_categories':
            f.write(f"SELECT setval(pg_get_serial_sequence('prompt_categories', 'id'), {max_id}, true);\n")
        elif table_name == 'topics':
            f.write(f"SELECT setval(pg_get_serial_sequence('topics', 'id'), {max_id}, true);\n")
        elif table_name == 'prompts':
            f.write(f"SELECT setval(pg_get_serial_sequence('prompts', 'id'), {max_id}, true);\n")
        
        f.write("\n-- Re-enable triggers and constraints (PostgreSQL)\n")
        f.write("SET session_replication_role = default;\n")
    
    def generate_prompt_categories_inserts(self):
        """Generate INSERT statements cho bảng prompt_categories với ID cụ thể"""
        if not self.data or 'categories' not in self.data:
            return [], 0
        
        inserts = []
        processed_categories = set()  # Tránh duplicate
        max_id = 0
        
        for category_data in self.data['categories']:
            category_info = category_data.get('category_info', {})
            
            if not category_info or category_info.get('id') in processed_categories:
                continue
                
            category_id = category_info.get('id')
            if category_id:
                max_id = max(max_id, category_id)
                
            name = self.escape_sql_string(category_info.get('name'))
            description = self.escape_sql_string(category_info.get('description'))
            image_url = self.escape_sql_string(category_info.get('image'))
            image_card_url = self.escape_sql_string(category_info.get('image_card'))
            prompt_count = category_info.get('prompt_count', 0)
            is_coming_soon = 'TRUE' if category_info.get('is_comming_soon', False) else 'FALSE'
            created_at = self.format_datetime(category_info.get('created_at'))
            updated_at = self.format_datetime(category_info.get('updated_at'))
            
            # Insert với ID cụ thể từ dữ liệu gốc và ON CONFLICT clause
            sql = f"""INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES ({category_id}, {name}, {description}, {image_url}, {image_card_url}, {prompt_count}, {is_coming_soon}, {created_at}, {updated_at})
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;"""
            
            inserts.append(sql)
            processed_categories.add(category_id)
        
        return inserts, max_id
    
    def generate_topics_inserts(self):
        """Generate INSERT statements cho bảng topics với ID cụ thể"""
        if not self.data or 'categories' not in self.data:
            return [], 0
        
        inserts = []
        processed_topics = set()  # Tránh duplicate
        max_id = 0
        
        for category_data in self.data['categories']:
            prompts = category_data.get('prompts', [])
            
            for prompt in prompts:
                topic_info = prompt.get('topic', {})
                
                if not topic_info or not isinstance(topic_info, dict) or topic_info.get('id') in processed_topics:
                    continue
                
                topic_id = topic_info.get('id')
                if topic_id:
                    max_id = max(max_id, topic_id)
                    
                name = self.escape_sql_string(topic_info.get('name'))
                description = self.escape_sql_string(topic_info.get('description', ''))
                
                # Insert với ID cụ thể từ dữ liệu gốc và ON CONFLICT clause
                sql = f"""INSERT INTO topics (id, name, description, created_at, updated_at) 
VALUES ({topic_id}, {name}, {description}, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
updated_at = NOW();"""
                
                inserts.append(sql)
                processed_topics.add(topic_id)
        
        return inserts, max_id
    
    def generate_prompts_inserts(self):
        """Generate INSERT statements cho bảng prompts với ID và foreign key cụ thể"""
        if not self.data or 'categories' not in self.data:
            return [], 0
        
        inserts = []
        max_id = 0
        
        for category_data in self.data['categories']:
            prompts = category_data.get('prompts', [])
            
            for prompt in prompts:
                prompt_id = prompt.get('id')
                if prompt_id:
                    max_id = max(max_id, prompt_id)
                    
                title = self.escape_sql_string(prompt.get('title'))
                short_description = self.escape_sql_string(prompt.get('short_description'))
                content = self.escape_sql_string(prompt.get('content'))
                prompt_text = self.escape_sql_string(prompt.get('text'))
                optimization_guide = self.escape_optimization_guide(prompt.get('OptimationGuide'))
                
                # Lấy category_id và topic_id trực tiếp từ dữ liệu với null handling
                category_id = prompt.get('category_id')
                if category_id is None or str(category_id).lower() in ['none', 'null', 'undefined']:
                    category_id = 'NULL'
                
                topic_id = prompt.get('topic_id')
                if topic_id is None or str(topic_id).lower() in ['none', 'null', 'undefined']:
                    topic_id = 'NULL'
                
                is_type = prompt.get('is_type', 1)
                
                sub_type = prompt.get('sub_type')
                if sub_type is None or str(sub_type).lower() in ['none', 'null', 'undefined']:
                    sub_type = 'NULL'
                
                what_field = self.escape_sql_string(prompt.get('what'))
                tips_field = self.escape_sql_string(prompt.get('tips'))
                how_field = self.escape_sql_string(prompt.get('how'))
                input_field = self.escape_sql_string(prompt.get('input'))
                output_field = self.escape_sql_string(prompt.get('output'))
                add_tip = self.escape_sql_string(prompt.get('addtip'))
                additional_information = self.escape_sql_string(prompt.get('addinformation'))
                view_count = prompt.get('sum_view', 0)
                created_at = self.format_datetime(prompt.get('created_at'))
                updated_at = self.format_datetime(prompt.get('updated_at'))
                
                # Insert với ID cụ thể và foreign key từ dữ liệu gốc và ON CONFLICT clause
                sql = f"""INSERT INTO prompts (id, title, short_description, content, prompt_text, optimization_guide, category_id, topic_id, is_type, sub_type, what_field, tips_field, how_field, input_field, output_field, add_tip, additional_information, view_count, created_at, updated_at) 
VALUES ({prompt_id}, {title}, {short_description}, {content}, {prompt_text}, {optimization_guide}, {category_id}, {topic_id}, {is_type}, {sub_type}, {what_field}, {tips_field}, {how_field}, {input_field}, {output_field}, {add_tip}, {additional_information}, {view_count}, {created_at}, {updated_at})
ON CONFLICT (id) DO UPDATE SET
title = EXCLUDED.title,
short_description = EXCLUDED.short_description,
content = EXCLUDED.content,
prompt_text = EXCLUDED.prompt_text,
optimization_guide = EXCLUDED.optimization_guide,
category_id = EXCLUDED.category_id,
topic_id = EXCLUDED.topic_id,
is_type = EXCLUDED.is_type,
sub_type = EXCLUDED.sub_type,
what_field = EXCLUDED.what_field,
tips_field = EXCLUDED.tips_field,
how_field = EXCLUDED.how_field,
input_field = EXCLUDED.input_field,
output_field = EXCLUDED.output_field,
add_tip = EXCLUDED.add_tip,
additional_information = EXCLUDED.additional_information,
view_count = EXCLUDED.view_count,
updated_at = EXCLUDED.updated_at;"""
                
                inserts.append(sql)
        
        return inserts, max_id
    
    def generate_prompts_with_relations_inserts(self):
        """Generate INSERT statements cho bảng prompts với foreign key relationships"""
        if not self.data or 'categories' not in self.data:
            return []
        
        inserts = []
        
        for category_data in self.data['categories']:
            category_info = category_data.get('category_info', {})
            category_name = category_info.get('name', '')
            
            prompts = category_data.get('prompts', [])
            
            for prompt in prompts:
                title = self.escape_sql_string(prompt.get('title'))
                short_description = self.escape_sql_string(prompt.get('short_description'))
                content = self.escape_sql_string(prompt.get('content'))
                prompt_text = self.escape_sql_string(prompt.get('text'))
                optimization_guide = self.escape_optimization_guide(prompt.get('OptimationGuide'))
                
                # Lấy topic name từ prompt
                topic_info = prompt.get('topic', {})
                topic_name = topic_info.get('name', '') if topic_info else ''
                
                is_type = prompt.get('is_type', 1)
                sub_type = prompt.get('sub_type') if prompt.get('sub_type') is not None else 'NULL'
                what_field = self.escape_sql_string(prompt.get('what'))
                tips_field = self.escape_sql_string(prompt.get('tips'))
                how_field = self.escape_sql_string(prompt.get('how'))
                input_field = self.escape_sql_string(prompt.get('input'))
                output_field = self.escape_sql_string(prompt.get('output'))
                add_tip = self.escape_sql_string(prompt.get('addtip'))
                additional_information = self.escape_sql_string(prompt.get('addinformation'))
                view_count = prompt.get('sum_view', 0)
                created_at = self.format_datetime(prompt.get('created_at'))
                updated_at = self.format_datetime(prompt.get('updated_at'))
                
                # Sử dụng subquery để lấy category_id và topic_id
                category_subquery = f"(SELECT id FROM prompt_categories WHERE name = {self.escape_sql_string(category_name)} LIMIT 1)" if category_name else 'NULL'
                topic_subquery = f"(SELECT id FROM topics WHERE name = {self.escape_sql_string(topic_name)} LIMIT 1)" if topic_name else 'NULL'
                
                sql = f"""INSERT INTO prompts (title, short_description, content, prompt_text, optimization_guide, category_id, topic_id, is_type, sub_type, what_field, tips_field, how_field, input_field, output_field, add_tip, additional_information, view_count, created_at, updated_at) 
VALUES ({title}, {short_description}, {content}, {prompt_text}, {optimization_guide}, {category_subquery}, {topic_subquery}, {is_type}, {sub_type}, {what_field}, {tips_field}, {how_field}, {input_field}, {output_field}, {add_tip}, {additional_information}, {view_count}, {created_at}, {updated_at});"""
                
                inserts.append(sql)
        
        return inserts
    
    def generate_all_inserts(self, base_filename='prom_vn'):
        """Generate tất cả INSERT statements và lưu vào nhiều file riêng biệt"""
        print("Generating SQL INSERT statements with specific IDs...")
        
        # 1. Generate prompt_categories inserts
        print("1. Generating prompt_categories inserts...")
        categories_inserts, categories_max_id = self.generate_prompt_categories_inserts()
        categories_file = f"{base_filename}_categories.sql"
        
        try:
            with open(categories_file, 'w', encoding='utf-8') as f:
                self.write_sql_header(f, "prompt_categories", len(categories_inserts))
                for insert in categories_inserts:
                    f.write(insert + '\n')
                self.write_sql_footer(f, "prompt_categories", categories_max_id)
            print(f"✅ Categories file created: {categories_file} (max_id: {categories_max_id})")
        except Exception as e:
            print(f"❌ Error saving categories file: {e}")
        
        # 2. Generate topics inserts
        print("2. Generating topics inserts...")
        topics_inserts, topics_max_id = self.generate_topics_inserts()
        topics_file = f"{base_filename}_topics.sql"
        
        try:
            with open(topics_file, 'w', encoding='utf-8') as f:
                self.write_sql_header(f, "topics", len(topics_inserts))
                for insert in topics_inserts:
                    f.write(insert + '\n')
                self.write_sql_footer(f, "topics", topics_max_id)
            print(f"✅ Topics file created: {topics_file} (max_id: {topics_max_id})")
        except Exception as e:
            print(f"❌ Error saving topics file: {e}")
        
        # 3. Generate prompts inserts với ID cụ thể
        print("3. Generating prompts inserts...")
        prompts_inserts, prompts_max_id = self.generate_prompts_inserts()
        prompts_file = f"{base_filename}_prompts.sql"
        
        try:
            with open(prompts_file, 'w', encoding='utf-8') as f:
                self.write_sql_header(f, "prompts", len(prompts_inserts))
                for insert in prompts_inserts:
                    f.write(insert + '\n')
                self.write_sql_footer(f, "prompts", prompts_max_id)
            print(f"✅ Prompts file created: {prompts_file} (max_id: {prompts_max_id})")
        except Exception as e:
            print(f"❌ Error saving prompts file: {e}")
        
        # 4. Create master execution file
        master_file = f"{base_filename}_execute_all.sql"
        try:
            with open(master_file, 'w', encoding='utf-8') as f:
                f.write("-- Master execution file for all prompt data\n")
                f.write(f"-- Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("-- Execute this file to run all inserts in correct order\n\n")
                f.write("-- Step 1: Insert Categories\n")
                f.write(f"\\i {categories_file}\n\n")
                f.write("-- Step 2: Insert Topics\n")
                f.write(f"\\i {topics_file}\n\n")
                f.write("-- Step 3: Insert Prompts\n")
                f.write(f"\\i {prompts_file}\n\n")
                f.write("-- All inserts completed!\n")
            print(f"✅ Master execution file created: {master_file}")
        except Exception as e:
            print(f"❌ Error saving master file: {e}")
        
        # 5. Create batch execution script
        batch_file = f"{base_filename}_batch_execute.sh"
        try:
            with open(batch_file, 'w', encoding='utf-8') as f:
                f.write("#!/bin/bash\n")
                f.write("# Batch execution script for prompt data\n")
                f.write(f"# Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write("echo 'Starting prompt data import...'\n\n")
                f.write("echo 'Step 1: Importing categories...'\n")
                f.write(f"psql -d $DATABASE_NAME -f {categories_file}\n")
                f.write("if [ $? -eq 0 ]; then\n")
                f.write("    echo '✅ Categories imported successfully'\n")
                f.write("else\n")
                f.write("    echo '❌ Error importing categories'\n")
                f.write("    exit 1\n")
                f.write("fi\n\n")
                f.write("echo 'Step 2: Importing topics...'\n")
                f.write(f"psql -d $DATABASE_NAME -f {topics_file}\n")
                f.write("if [ $? -eq 0 ]; then\n")
                f.write("    echo '✅ Topics imported successfully'\n")
                f.write("else\n")
                f.write("    echo '❌ Error importing topics'\n")
                f.write("    exit 1\n")
                f.write("fi\n\n")
                f.write("echo 'Step 3: Importing prompts...'\n")
                f.write(f"psql -d $DATABASE_NAME -f {prompts_file}\n")
                f.write("if [ $? -eq 0 ]; then\n")
                f.write("    echo '✅ Prompts imported successfully'\n")
                f.write("else\n")
                f.write("    echo '❌ Error importing prompts'\n")
                f.write("    exit 1\n")
                f.write("fi\n\n")
                f.write("echo '🎉 All data imported successfully!'\n")
            
            # Make script executable
            import os
            os.chmod(batch_file, 0o755)
            print(f"✅ Batch execution script created: {batch_file}")
        except Exception as e:
            print(f"❌ Error saving batch file: {e}")
        
        print(f"\n🎉 All files generated successfully!")
        print(f"📊 Summary:")
        print(f"   - Categories: {len(categories_inserts)} records → {categories_file} (max_id: {categories_max_id})")
        print(f"   - Topics: {len(topics_inserts)} records → {topics_file} (max_id: {topics_max_id})")
        print(f"   - Prompts: {len(prompts_inserts)} records → {prompts_file} (max_id: {prompts_max_id})")
        print(f"\n📋 How to execute:")
        print(f"   Option 1: Use PostgreSQL \\i command:")
        print(f"            psql -d your_database_name -f {master_file}")
        print(f"   Option 2: Execute files individually:")
        print(f"            psql -d your_database_name -f {categories_file}")
        print(f"            psql -d your_database_name -f {topics_file}")
        print(f"            psql -d your_database_name -f {prompts_file}")
        print(f"   Option 3: Use bash script (set DATABASE_NAME env var first):")
        print(f"            export DATABASE_NAME=your_database_name")
        print(f"            ./{batch_file}")

def main():
    # Thay đổi tên file JSON của bạn ở đây
    json_file = "prom_vn_data_20250609_155410.json"  # Thay bằng tên file thực tế
    
    # Tạo generator instance
    generator = SQLGenerator(json_file)
    
    if generator.data is None:
        print("❌ Cannot load JSON data. Please check file path.")
        return
    
    # Generate SQL inserts
    generator.generate_all_inserts('prom_vn')

if __name__ == "__main__":
    main()
