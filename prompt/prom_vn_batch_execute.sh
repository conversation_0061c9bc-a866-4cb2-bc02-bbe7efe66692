#!/bin/bash
# Batch execution script for prompt data
# Generated at: 2025-06-13 09:32:42

echo 'Starting prompt data import...'

echo 'Step 1: Importing categories...'
psql -d $DATABASE_NAME -f prom_vn_categories.sql
if [ $? -eq 0 ]; then
    echo '✅ Categories imported successfully'
else
    echo '❌ Error importing categories'
    exit 1
fi

echo 'Step 2: Importing topics...'
psql -d $DATABASE_NAME -f prom_vn_topics.sql
if [ $? -eq 0 ]; then
    echo '✅ Topics imported successfully'
else
    echo '❌ Error importing topics'
    exit 1
fi

echo 'Step 3: Importing prompts...'
psql -d $DATABASE_NAME -f prom_vn_prompts.sql
if [ $? -eq 0 ]; then
    echo '✅ Prompts imported successfully'
else
    echo '❌ Error importing prompts'
    exit 1
fi

echo '🎉 All data imported successfully!'
