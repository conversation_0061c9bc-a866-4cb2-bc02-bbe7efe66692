-- SQL INSERT Statements for prompt_categories
-- Generated at: 2025-06-13 09:32:42
-- Records: 20

-- Disable triggers and constraints temporarily (PostgreSQL)
SET session_replication_role = replica;

-- Clear existing categories data to avoid conflicts
DELETE FROM prompt_categories;
ALTER SEQUENCE prompt_categories_id_seq RESTART WITH 1;

INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (54, '<PERSON><PERSON><PERSON><PERSON>ẩ<PERSON>', '<PERSON>ư<PERSON><PERSON>ẩ<PERSON>', 'http://prom.vn/uploads/1749281929205.webp', 'http://prom.vn/uploads/1749281929235.webp', 100, FALSE, '2025-06-06 15:22:15', '2025-06-07 07:38:49')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (53, 'Phong Thủy', NULL, 'http://prom.vn/uploads/1749282186850.PNG', 'http://prom.vn/uploads/1749282187003.PNG', 50, FALSE, '2025-06-06 14:56:14', '2025-06-07 07:46:21')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (8, 'Sales', NULL, 'http://prom.vn/uploads/1741591763141.png', 'https://cdn.prod.website-files.com/64808cc9f88d76f4355b870a/673deb8ef4b01cc7471388d6_ChatGPT%20Sales%20icon.svg', 280, FALSE, '2025-03-10 07:29:23', '2025-03-10 07:29:23')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (1, 'Giáo Dục', NULL, 'http://prom.vn/uploads/1741590286654.png', 'https://cdn.prod.website-files.com/64808cc9f88d76f4355b870a/673deb8815318749fe1374ef_ChatGPT%20Education%20icon.svg', 473, FALSE, '2025-03-10 06:29:23', '2025-03-10 07:04:46')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (2, 'Marketing', NULL, 'http://prom.vn/uploads/1741591710495.png', 'https://cdn.prod.website-files.com/64808cc9f88d76f4355b870a/66059843422eaa071687f119_MEGA%20marketing%20icon.avif', 462, FALSE, '2025-03-10 05:29:23', '2025-03-10 07:28:30')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (3, 'Khởi Nghiệp', NULL, 'http://prom.vn/uploads/1741591720795.png', 'https://cdn.prod.website-files.com/64808cc9f88d76f4355b870a/673deb8e973441f7c5aca58f_ChatGPT%20Solopreneurs%20icon.svg', 435, FALSE, '2025-03-10 04:29:23', '2025-03-10 07:28:40')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (4, 'Kinh Doanh', NULL, 'http://prom.vn/uploads/1741591728555.png', 'https://cdn.prod.website-files.com/64808cc9f88d76f4355b870a/673deb9260687793c0ca5f08_ChatGPT%20Business%20icon.svg', 526, FALSE, '2025-03-10 03:29:23', '2025-03-10 07:28:48')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (5, 'Viết Lách', NULL, 'http://prom.vn/uploads/1741591736972.png', 'https://cdn.prod.website-files.com/64808cc9f88d76f4355b870a/673deb8e41c90fe2401c397d_ChatGPT%20Writing%20%20icon.svg', 417, FALSE, '2025-03-10 02:29:23', '2025-03-10 07:28:56')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (6, 'Hiệu Suất', NULL, 'http://prom.vn/uploads/1741591744558.png', 'https://cdn.prod.website-files.com/64808cc9f88d76f4355b870a/673deb92428198854254c129_ChatGPT%20Productivity%20%20icon.svg', 239, FALSE, '2025-03-10 01:29:23', '2025-03-10 07:29:04')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (7, 'SEO', NULL, 'http://prom.vn/uploads/1741591755808.png', 'https://cdn.prod.website-files.com/64808cc9f88d76f4355b870a/673deb852a8f074d29be401b_ChatGPT%20SEO%20icon.svg', 476, FALSE, '2025-03-09 07:29:23', '2025-03-10 07:29:15')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (16, 'Làm Video - Hiếu AI', NULL, 'http://prom.vn/uploads/1742571677129.png', 'http://prom.vn/uploads/1742571677131.png', 15, FALSE, '2025-03-08 15:41:17', '2025-05-22 01:38:42')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (17, 'Công Nghệ Thông Tin', NULL, 'http://prom.vn/uploads/1742642947746.webp', 'http://prom.vn/uploads/1742642947746.webp', 100, FALSE, '2025-03-08 15:40:48', '2025-03-22 13:23:08')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (18, 'Dịch Vụ Khách Hàng', 'Dịch Vụ Khách Hàng', 'http://prom.vn/uploads/1742643250482.webp', 'http://prom.vn/uploads/1742643250483.webp', 150, FALSE, '2025-03-08 15:40:48', '2025-06-02 15:09:54')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (19, 'Thương Mại Điện Tử', 'Thương Mại Điện Tử', 'http://prom.vn/uploads/1742643770602.png', 'http://prom.vn/uploads/1742643770603.png', 100, FALSE, '2025-03-08 15:40:48', '2025-03-22 11:42:50')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (20, 'Chăm Sóc Sức Khỏe', 'Chăm Sóc Sức Khỏe
', 'http://prom.vn/uploads/1742643310384.webp', 'http://prom.vn/uploads/1742643310384.webp', 100, FALSE, '2025-03-08 15:40:48', '2025-04-14 15:17:28')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (22, 'Tài Chính', 'Tài Chính', 'http://prom.vn/uploads/1742643343006.webp', 'http://prom.vn/uploads/1742643343006.webp', 200, FALSE, '2025-03-08 15:40:48', '2025-03-22 11:35:43')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (23, 'Fitness', 'Fitness', 'http://prom.vn/uploads/1742643356284.webp', 'http://prom.vn/uploads/1742643356285.webp', 100, FALSE, '2025-03-08 15:40:48', '2025-06-02 15:17:07')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (24, 'Du Lịch', 'Du Lịch', 'http://prom.vn/uploads/1742643382245.webp', 'http://prom.vn/uploads/1742643382245.webp', 100, FALSE, '2025-03-08 15:40:48', '2025-04-14 15:19:52')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (27, 'Bảo Hiểm', 'Bảo Hiểm', 'http://prom.vn/uploads/1742643490106.webp', 'http://prom.vn/uploads/1742643490106.webp', 100, FALSE, '2025-03-08 15:40:48', '2025-04-14 15:16:10')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;
INSERT INTO prompt_categories (id, name, description, image_url, image_card_url, prompt_count, is_coming_soon, created_at, updated_at) 
VALUES (28, 'Game Theory', NULL, 'http://prom.vn/uploads/1745303371327.png', 'http://prom.vn/uploads/1745303371329.png', 15, TRUE, '2025-04-21 13:59:34', '2025-06-06 14:21:05')
ON CONFLICT (id) DO UPDATE SET
name = EXCLUDED.name,
description = EXCLUDED.description,
image_url = EXCLUDED.image_url,
image_card_url = EXCLUDED.image_card_url,
prompt_count = EXCLUDED.prompt_count,
is_coming_soon = EXCLUDED.is_coming_soon,
updated_at = EXCLUDED.updated_at;

-- Reset sequence for prompt_categories after custom ID insertion
SELECT setval(pg_get_serial_sequence('prompt_categories', 'id'), 54, true);

-- Re-enable triggers and constraints (PostgreSQL)
SET session_replication_role = default;
