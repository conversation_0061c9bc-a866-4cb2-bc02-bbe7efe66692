import React, { useState, useCallback, useMemo, useEffect, useRef } from 'react';

// Reusable Component for multi-select dropdown fields
const MultiSelectDropdown = ({ label, options = [], selectedValues, onChange, placeholder }) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef(null);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    const handleOptionClick = (optionValue) => {
        const newSelectedValues = selectedValues.includes(optionValue)
            ? selectedValues.filter((value) => value !== optionValue)
            : [...selectedValues, optionValue];
        onChange(newSelectedValues);
    };
    
    // Extract the Vietnamese part for display
    const getDisplayValue = (value) => {
        // Find the matching option to get its label for display
        const option = options.find(opt => (typeof opt === 'object' ? opt.value : opt) === value);
        if (typeof option === 'object' && option.label) {
            return option.label.split('(')[0].trim();
        }
        return value.split('(')[0].trim();
    };

    const displayString = selectedValues.length > 0
        ? selectedValues.map(getDisplayValue).join(', ')
        : placeholder;

    return (
        <div className="mb-4" ref={dropdownRef}>
            <label className="block text-gray-700 text-sm font-semibold mb-2">
                {label}:
            </label>
            <div className="relative">
                <button
                    type="button"
                    className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:border-transparent transition duration-200 ease-in-out text-gray-800 bg-white text-left flex justify-between items-center"
                    onClick={() => setIsOpen(!isOpen)}
                >
                    <span className="truncate">{displayString}</span>
                    <svg className={`fill-current h-4 w-4 transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                </button>
                {isOpen && (
                    <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                        {options.map((option, index) => {
                            const optionValue = typeof option === 'object' ? option.value : option;
                            const optionLabel = typeof option === 'object' ? option.label : option;
                            return (
                                <div key={index} className="flex items-center p-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleOptionClick(optionValue)}>
                                    <input
                                        type="checkbox"
                                        checked={selectedValues.includes(optionValue)}
                                        onChange={() => handleOptionClick(optionValue)}
                                        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                    />
                                    <label className="ml-2 text-gray-700 w-full cursor-pointer">
                                        {optionLabel}
                                    </label>
                                </div>
                            );
                        })}
                    </div>
                )}
            </div>
        </div>
    );
};

// Reusable InputField Component
const InputField = ({ label, value, onChange, placeholder, children, containerClassName = '', labelClassName = '', inputClassName = '' }) => {
    return (
        <div className={`mb-4 ${containerClassName}`}>
            <label className={`block text-gray-700 text-sm font-semibold mb-2 ${labelClassName}`}>
                {label}:
            </label>
            <div className="relative">
                <input
                    type="text"
                    className={`w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:border-transparent transition duration-200 ease-in-out text-gray-800 ${inputClassName}`}
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    placeholder={placeholder}
                />
                {children && <div className="absolute right-2 top-1/2 -translate-y-1/2">{children}</div>}
            </div>
        </div>
    );
};

// Simple Toast Notification Component
const Toast = ({ message, show }) => {
    if (!show) return null;

    return (
        <div className="fixed bottom-5 right-5 bg-green-500 text-white py-2 px-4 rounded-lg shadow-lg animate-fade-in-out z-50">
            {message}
        </div>
    );
};


// Main App component
function App() {
    // --- STATE MANAGEMENT ---
    const [showNote, setShowNote] = useState(false);
    const fileInputRef = useRef(null); // Ref for file input

    // Placeholder for logo URL
    const logoUrl = 'https://img.icons8.com/color/48/film-reel.png';

    // Panoramic Image Analysis
    const [panoramicImage, setPanoramicImage] = useState(null);
    const [panoramicImagePreview, setPanoramicImagePreview] = useState('');
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [analysisError, setAnalysisError] = useState('');
    
    // Context Image Analysis
    const [isAnalyzingContext, setIsAnalyzingContext] = useState(false);
    const [contextAnalysisError, setContextAnalysisError] = useState('');

    // Section 1: Setting
    const [location, setLocation] = useState('');
    const [time, setTime] = useState('');
    const [weather, setWeather] = useState('');
    const [lightingDetails, setLightingDetails] = useState('');
    const [settingImage, setSettingImage] = useState(null);
    const [settingImagePreview, setSettingImagePreview] = useState('');

    // Section 2: Characters
    const [characters, setCharacters] = useState([
        { id: 1, description: '', appearance: '', emotion: '', mainAction: '', relatedObject: '', dialogue: '', imageRef: null, imageRefPreviewUrl: '' }
    ]);
    const [isDetailingCharacter, setIsDetailingCharacter] = useState(null);

    // Section 3: Visual Style & Post-processing
    const [visualStyle, setVisualStyle] = useState([]);
    const [lightingStyle, setLightingStyle] = useState([]);
    const [colorGrading, setColorGrading] = useState([]);
    const [dominantColors, setDominantColors] = useState([]);
    const [postProcessingEffects, setPostProcessingEffects] = useState([]);
    const [motionEffects, setMotionEffects] = useState([]);
    const [creativeEffects, setCreativeEffects] = useState([]);
    const [surrealElements, setSurrealElements] = useState('');

    // Section 4: Cinematography
    const [composition, setComposition] = useState([]);
    const [cameraAngle, setCameraAngle] = useState([]);
    const [cameraMotion, setCameraMotion] = useState([]);
    const [focusPoint, setFocusPoint] = useState([]);

    // Section 5: Editing & Audio
    const [editingPace, setEditingPace] = useState([]);
    const [transitions, setTransitions] = useState([]);
    const [mood, setMood] = useState([]);
    const [backgroundMusic, setBackgroundMusic] = useState([]);
    const [soundEffects, setSoundEffects] = useState([]);
    const [typography, setTypography] = useState('');
    const [subtitleInstructions, setSubtitleInstructions] = useState([]);

    // Section 6: Technical Specs
    const [resolution, setResolution] = useState([]);
    const [aspectRatio, setAspectRatio] = useState([]);
    const [duration, setDuration] = useState([]);

    // Prompt Generation & UI State
    const [generatedPrompt, setGeneratedPrompt] = useState('');
    const [translatedVietnamesePrompt, setTranslatedVietnamesePrompt] = useState('');
    const [isTranslating, setIsTranslating] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [toastMessage, setToastMessage] = useState('');


    // --- OPTIONS DATA ---
    const options = useMemo(() => ({
        lightingStyle: [
            { value: 'natural light', label: 'Ánh sáng tự nhiên (Natural Light)' },
            { value: 'studio lighting (key, fill, back light)', label: 'Ánh sáng studio (Studio Lighting)' },
            { value: 'high-contrast lighting (chiaroscuro)', label: 'Tương phản cao (High-Contrast)' },
            { value: 'soft light', label: 'Ánh sáng mềm (Soft Light)' },
            { value: 'hard light', label: 'Ánh sáng gắt (Hard Light)' },
            { value: 'neon glow', label: 'Ánh sáng neon (Neon Glow)' },
        ],
        visualStyle: [
            { value: "cinematic", label: "Điện ảnh (Cinematic)" }, { value: "photorealistic", label: "Chân thực (Photorealistic)" }, { value: "hyper-realistic", label: "Siêu thực (Hyper-realistic)" }, { value: "dreamy", label: "Mộng mơ (Dreamy)" },
            { value: "fantasy", label: "Giả tưởng (Fantasy)" }, { value: "surreal", label: "Trừu tượng (Surreal)" }, { value: "sci-fi", label: "Khoa học viễn tưởng (Sci-fi)" }, { value: "futuristic", label: "Tương lai (Futuristic)" },
            { value: "cyberpunk", label: "Cyberpunk" }, { value: "steampunk", label: "Steampunk" },
            { value: "vintage", label: "Cổ điển (Vintage)" }, { value: "retro", label: "Hoài cổ (Retro)" }, { value: "noir", label: "Phim đen (Noir)" },
            { value: "minimalist", label: "Tối giản (Minimalist)" }, { value: "abstract", label: "Trừu tượng (Abstract)" },
            { value: "animated", label: "Hoạt hình (Animated)" }, { value: "hand-drawn", label: "Vẽ tay (Hand-drawn)" }, { value: "sketch style", label: "Phác họa (Sketch)" }, { value: "watercolor", label: "Màu nước (Watercolor)" }, { value: "pixel art", label: "Nghệ thuật pixel (Pixel Art)" },
            { value: "gritty", label: "Gai góc (Gritty)" }, { value: "baroque", label: "Baroque" }, { value: "impressionistic", label: "Ấn tượng (Impressionistic)" }
        ],
        postProcessingEffects: [
            { value: 'Color Grading', label: 'Chỉnh màu điện ảnh (Color Grading)' }, { value: 'Glow', label: 'Ánh sáng dịu mờ (Glow)' }, { value: 'Vignette', label: 'Tối góc (Vignette)' }, { value: 'Film Grain', label: 'Hạt phim (Film Grain)' },
            { value: 'Light Leaks', label: 'Loang sáng (Light Leaks)' }, { value: 'Lens Flare', label: 'Lóe sáng ống kính (Lens Flare)' }, { value: 'Sepia Tone', label: 'Tông nâu đỏ (Sepia Tone)' }, { value: 'Monochrome', label: 'Đơn sắc (Monochrome)' },
            { value: 'Bokeh', label: 'Hậu cảnh mờ (Bokeh)' }
        ],
        motionAndSpeedEffects: [
            { value: 'Motion Blur', label: 'Mờ chuyển động (Motion Blur)' }, { value: 'Speed Ramping', label: 'Thay đổi tốc độ (Speed Ramping)' }, { value: 'Freeze Frame', label: 'Đóng băng khung hình (Freeze Frame)' },
            { value: 'Zoom / Spin / Whip Pan effects', label: 'Hiệu ứng máy quay (Zoom/Spin/Whip Pan)' }, { value: 'Tilt-Shift', label: 'Hiệu ứng mô hình (Tilt-Shift)' }
        ],
        creativeAndArtisticEffects: [
            { value: 'Double Exposure', label: 'Chồng ảnh (Double Exposure)' }, { value: 'Chromatic Aberration', label: 'Lệch màu (Chromatic Aberration)' }, { value: 'Glitch Effect', label: 'Hiệu ứng nhiễu (Glitch Effect)' },
            { value: 'Pixelate', label: 'Vỡ hạt (Pixelate)' }, { value: 'Scan Lines', label: 'Dòng quét TV (Scan Lines)' }
        ],
        composition: [
            { value: 'rule of thirds composition', label: 'Quy tắc 1/3 (Rule of Thirds)' }, { value: 'symmetrical composition', label: 'Đối xứng (Symmetrical)' }, { value: 'centered composition', label: 'Trung tâm (Centered)' },
            { value: 'leading lines', label: 'Đường dẫn (Leading Lines)' }, { value: 'frame within a frame', label: 'Khung trong khung (Frame within a Frame)' }, { value: 'unconventional framing', label: 'Phá cách (Unconventional)' },
            { value: 'use of negative space', label: 'Không gian âm (Negative Space)' },
        ],
        cameraAngle: [
            { value: "wide shot", label: "Toàn cảnh (Wide Shot)" }, { value: "long shot", label: "Viễn cảnh (Long Shot)" }, { value: "establishing shot", label: "Cảnh thiết lập (Establishing Shot)" }, { value: "full shot", label: "Toàn thân (Full Shot)" }, { value: "medium shot", label: "Trung cảnh (Medium Shot)" },
            { value: "two-shot", label: "Cảnh hai người (Two-shot)" }, { value: "close-up", label: "Cận cảnh (Close-up)" }, { value: "extreme close-up", label: "Siêu cận cảnh (Extreme Close-up)" }, { value: "POV shot", label: "Góc nhìn thứ nhất (POV)" },
            { value: "over-the-shoulder", label: "Qua vai (Over-the-shoulder)" }, { value: "low angle", label: "Góc thấp (Low Angle)" }, { value: "high angle", label: "Góc cao (High Angle)" }, { value: "overhead shot / top-down shot", label: "Từ trên xuống (Overhead)" },
            { value: "dutch angle", label: "Góc nghiêng (Dutch Angle)" }, { value: "drone shot", label: "Flycam (Drone Shot)" }
        ],
        cameraMotion: [
            { value: "static shot (tripod)", label: "Tĩnh (Static)" }, { value: "slow camera movement", label: "Chuyển động chậm (Slow Movement)" }, { value: "fast camera movement", label: "Chuyển động nhanh (Fast Movement)" }, { value: "smooth gimbal movement", label: "Chuyển động mượt (Gimbal)" },
            { value: "shaky handheld camera", label: "Cầm tay rung (Handheld)" }, { value: "panning", label: "Lia ngang (Panning)" }, { value: "tilting", label: "Lia dọc (Tilting)" }, { value: "tracking shot / following shot", label: "Theo dấu (Tracking)" },
            { value: "dolly zoom (Vertigo effect)", label: "Dolly Zoom" }, { value: "dolly-in", label: "Tiến vào (Dolly-in)" }, { value: "dolly-out", label: "Lùi ra (Dolly-out)" }, { value: "crane shot", label: "Cẩu máy (Crane)" }, { value: "slow motion", label: "Chuyển động chậm (Slow Motion)" },
            { value: "timelapse", label: "Tua nhanh (Timelapse)" }, { value: "reverse dolly", label: "Lùi xa (Reverse Dolly)" }
        ],
        transitions: [
            { value: 'Fade In / Out', label: 'Mờ dần (Fade In/Out)' }, { value: 'Wipe (left/right)', label: 'Gạt cảnh (Wipe)' }, { value: 'Cross Dissolve', label: 'Hòa tan (Cross Dissolve)' },
            { value: 'Match Cut', label: 'Cắt nối logic (Match Cut)' }, { value: 'Jump Cut', label: 'Cắt giật (Jump Cut)' }, { value: 'Zoom Transition', label: 'Chuyển cảnh Zoom (Zoom Transition)' },
            { value: 'Whip Pan Transition', label: 'Chuyển cảnh lia nhanh (Whip Pan)' }, { value: 'Rotate Transition', label: 'Chuyển cảnh xoay (Rotate)' }, { value: 'Hard cut', label: 'Cắt thẳng (Hard Cut)' },
        ],
        editingPace: [
            { value: 'fast-paced, energetic editing', label: 'Dựng nhanh, dồn dập (Fast-paced)' }, { value: 'slow, smooth, and deliberate editing', label: 'Dựng chậm, mượt mà (Slow-paced)' },
        ],
        focusPoint: [
            { value: 'Main character', label: 'Nhân vật chính (Main Character)' }, { value: 'Background', label: 'Hậu cảnh (Background)' }, { value: 'Foreground', label: 'Tiền cảnh (Foreground)' },
            { value: 'A specific object', label: 'Vật thể cụ thể (Specific Object)' }, { value: 'Shallow depth of field', label: 'Độ sâu trường ảnh nông (Shallow DOF)' }, { value: 'Deep depth of field', label: 'Độ sâu trường ảnh sâu (Deep DOF)' }
        ],
        mood: [
            { value: "mysterious", label: "Huyền bí (Mysterious)" }, { value: "surreal", label: "Kỳ ảo (Surreal)" }, { value: "romantic", label: "Lãng mạn (Romantic)" }, { value: "dramatic", label: "Kịch tính (Dramatic)" },
            { value: "peaceful", label: "Yên bình (Peaceful)" }, { value: "joyful", label: "Vui vẻ (Joyful)" }, { value: "sad", label: "Buồn bã (Sad)" }, { value: "suspenseful", label: "Hồi hộp (Suspenseful)" },
            { value: "adventurous", label: "Phiêu lưu (Adventurous)" }, { value: "epic", label: "Sử thi (Epic)" }
        ],
        dominantColors: [
            { value: "warm tones", label: "Tông nóng (Warm Tones)" }, { value: "cool tones", label: "Tông lạnh (Cool Tones)" }, { value: "earth tones", label: "Tông màu đất (Earth Tones)" }, { value: "vibrant colors", label: "Màu rực rỡ (Vibrant Colors)" },
            { value: "monochromatic", label: "Đơn sắc (Monochromatic)" }, { value: "pastel colors", label: "Màu pastel (Pastel Colors)" }
        ],
        backgroundMusic: [
            { value: "gentle piano", label: "Piano nhẹ nhàng (Gentle Piano)" }, { value: "mysterious electronic music", label: "Nhạc điện tử huyền bí (Mysterious Electronic)" }, { value: "epic orchestral score", label: "Nhạc giao hưởng hoành tráng (Epic Orchestral)" },
            { value: "ambient soundscape", label: "Âm thanh không gian (Ambient)" }, { value: "upbeat pop music", label: "Nhạc pop sôi động (Upbeat Pop)" }, { value: "heavy rock", label: "Rock mạnh mẽ (Heavy Rock)" }, { value: "no background music", label: "Không có nhạc nền (No Music)" }
        ],
        soundEffects: [
            { value: "wind blowing", label: "Gió thổi (Wind Blowing)" }, { value: "footsteps", label: "Tiếng bước chân (Footsteps)" }, { value: "city ambience", label: "Không khí thành phố (City Ambience)" }, { value: "rain falling", label: "Tiếng mưa rơi (Rain Falling)" },
            { value: "ocean waves", label: "Sóng biển (Ocean Waves)" }, { value: "explosions", label: "Tiếng nổ (Explosions)" }, { value: "no sound effects", label: "Không có hiệu ứng âm thanh (No SFX)" }
        ],
        subtitleOptions: [
            { value: "No subtitles", label: "Không phụ đề (No Subtitles)" }, { value: "English subtitles only", label: "Chỉ phụ đề tiếng Anh (English Subtitles)" }, { value: "Vietnamese subtitles only", label: "Chỉ phụ đề tiếng Việt (Vietnamese Subtitles)" }
        ],
        resolution: [{ value: "720p", label: "720p" }, { value: "1080p", label: "1080p" }, { value: "2K", label: "2K" }, { value: "4K", label: "4K" }, { value: "8K", label: "8K" }],
        aspectRatio: [{ value: "1:1 (square)", label: "1:1 (vuông)" }, { value: "4:3 (classic TV)", label: "4:3 (cổ điển)" }, { value: "16:9 (widescreen)", label: "16:9 (màn ảnh rộng)" }, { value: "9:16 (vertical)", label: "9:16 (dọc)" }, { value: "2.35:1 (cinemascope)", label: "2.35:1 (điện ảnh)" }],
        duration: [{ value: "5", label: "5 giây" }, { value: "8", label: "8 giây" }, { value: "10", label: "10 giây" }, { value: "15", label: "15 giây" }, { value: "20", label: "20 giây" }, { value: "30", label: "30 giây" }, { value: "60", label: "60 giây" }],
    }), []);


    // --- HELPER & HANDLER FUNCTIONS ---

    const getBase64 = useCallback((file) => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result.split(',')[1]);
            reader.onerror = (error) => reject(error);
        });
    }, []);

    const showToast = (message) => {
        setToastMessage(message);
        setTimeout(() => setToastMessage(''), 3000);
    };

    const copyToClipboard = (text, language) => {
        const el = document.createElement('textarea');
        el.value = text;
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        showToast(`Đã sao chép prompt tiếng ${language} vào clipboard!`);
    };

    const handleSettingImageChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            setSettingImage(file);
            setSettingImagePreview(URL.createObjectURL(file));
        } else {
            setSettingImage(null);
            setSettingImagePreview('');
        }
    };

    const handleAddCharacter = () => {
        setCharacters([...characters, {
            id: characters.length > 0 ? Math.max(...characters.map(c => c.id)) + 1 : 1,
            description: '', appearance: '', emotion: '', mainAction: '', relatedObject: '', dialogue: '', imageRef: null, imageRefPreviewUrl: ''
        }]);
    };

    const handleRemoveCharacter = (idToRemove) => {
        setCharacters(characters.filter(char => char.id !== idToRemove));
    };

    const handleCharacterChange = (id, field, value) => {
        setCharacters(characters.map(char =>
            char.id === id ? { ...char, [field]: value } : char
        ));
    };

    const handleCharacterImageChange = (event, id) => {
        const file = event.target.files[0];
        if (file) {
            setCharacters(chars => chars.map(char =>
                char.id === id ? { ...char, imageRef: file, imageRefPreviewUrl: URL.createObjectURL(file) } : char
            ));
        } else {
            setCharacters(chars => chars.map(char =>
                char.id === id ? { ...char, imageRef: null, imageRefPreviewUrl: '' } : char
            ));
        }
    };

    const handleDetailCharacterDescription = async (charId) => {
        const character = characters.find(c => c.id === charId);
        if (!character || !character.description) {
            setError('Vui lòng nhập mô tả nhân vật trước khi làm chi tiết.');
            return;
        }
        setIsDetailingCharacter(charId);
        setError('');
        const detailPrompt = `Based on the following short description, expand it into a detailed character description suitable for a video generation prompt. Focus on visual details, clothing, and potential demeanor. Output ONLY the detailed description, with no introductory text or explanation.\n\nShort description: "${character.description}"`;
        try {
            const payload = { contents: [{ role: "user", parts: [{ text: detailPrompt }] }] };
            const apiKey = ""; // Provided by Canvas
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            const result = await response.json();
            if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
                const detailedDescription = result.candidates[0].content.parts[0].text;
                handleCharacterChange(charId, 'description', detailedDescription.trim());
            } else {
                setError('Không thể tạo mô tả chi tiết. Vui lòng thử lại.');
            }
        } catch (err) {
            setError('Lỗi kết nối AI khi tạo mô tả chi tiết.');
            console.error("Detailing error:", err);
        } finally {
            setIsDetailingCharacter(null);
        }
    };

    const handlePanoramicImageChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            setPanoramicImage(file);
            setPanoramicImagePreview(URL.createObjectURL(file));
            setAnalysisError('');
        } else {
            setPanoramicImage(null);
            setPanoramicImagePreview('');
        }
    };

    // Function to analyze context image and fill setting fields
    const analyzeContextImage = async () => {
        if (!settingImage) {
            setContextAnalysisError('Vui lòng chọn ảnh bối cảnh để phân tích.');
            return;
        }
        setIsAnalyzingContext(true);
        setContextAnalysisError('');
        try {
            const base64Data = await getBase64(settingImage);
            const imagePart = { inlineData: { mimeType: settingImage.type, data: base64Data } };
            const analysisPrompt = `Analyze the provided image and describe the scene. Provide the output as a single, minified JSON object with NO extra text or markdown. The JSON object must contain these exact keys: "location", "time", "weather".
- location: A brief description of the location in Vietnamese.
- time: The time of day in Vietnamese (e.g., 'hoàng hôn', 'ban đêm', 'buổi trưa').
- weather: The weather conditions in Vietnamese (e.g., 'trời quang', 'sương mù', 'nắng gắt').`;

            const payload = {
                contents: [{
                    role: "user",
                    parts: [{ text: analysisPrompt }, imagePart]
                }]
            };

            const apiKey = ""; // Provided by Canvas
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            const result = await response.json();

            if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
                let textResponse = result.candidates[0].content.parts[0].text.replace(/```json|```/g, '').trim();
                try {
                    const jsonResponse = JSON.parse(textResponse);
                    setLocation(jsonResponse.location || '');
                    setTime(jsonResponse.time || '');
                    setWeather(jsonResponse.weather || '');
                    showToast('Đã phân tích và điền thông tin bối cảnh!');
                } catch (parseError) {
                    console.error('Context JSON parsing error:', parseError, 'Raw response:', textResponse);
                    setContextAnalysisError('Lỗi xử lý phản hồi từ AI. Vui lòng thử lại.');
                }
            } else {
                setContextAnalysisError('Không thể phân tích hình ảnh. Lỗi: ' + (result.error?.message || 'Lỗi API không xác định'));
            }
        } catch (err) {
            console.error('Context image analysis error:', err);
            setContextAnalysisError('Lỗi xảy ra trong quá trình phân tích bối cảnh.');
        } finally {
            setIsAnalyzingContext(false);
        }
    };


    const analyzeAndGeneratePrompt = async () => {
        if (!panoramicImage) {
            setAnalysisError('Vui lòng chọn một hình ảnh để phân tích.');
            return;
        }
        setIsAnalyzing(true);
        setAnalysisError('');
        setGeneratedPrompt('');
        setTranslatedVietnamesePrompt('');

        try {
            const base64Data = await getBase64(panoramicImage);
            const imagePart = { inlineData: { mimeType: panoramicImage.type, data: base64Data } };
            const analysisPrompt = `Analyze the provided image for creating a video prompt. Extract the following details and provide the output as a single, minified JSON object with NO extra text or markdown formatting. The JSON object must contain these exact keys: "location", "time", "weather", "lightingStyle", "visualStyle", "composition", "cameraAngle", "mood", "dominantColors", "generatedPrompt".

- location: A brief description of the location in Vietnamese.
- time: The time of day in Vietnamese (e.g., 'hoàng hôn', 'ban đêm').
- weather: The weather conditions in Vietnamese (e.g., 'trời quang', 'sương mù').
- lightingStyle: Suggest a single, appropriate lighting style value from: 'natural light', 'studio lighting (key, fill, back light)', 'high-contrast lighting (chiaroscuro)', 'soft light', 'hard light', 'neon glow'.
- visualStyle: Suggest a single, appropriate visual style value from: cinematic, photorealistic, hyper-realistic, dreamy, fantasy, surreal, sci-fi, futuristic, cyberpunk, vintage, retro, noir, minimalist, animated, hand-drawn, sketch style, gritty, impressionistic.
- composition: Suggest a single composition value from: 'rule of thirds composition', 'symmetrical composition', 'centered composition', 'leading lines'.
- cameraAngle: Suggest a single camera angle value from: wide shot, long shot, establishing shot, full shot, medium shot, close-up, extreme close-up, POV shot, low angle, high angle, overhead shot.
- mood: Suggest a mood in English from: mysterious, surreal, romantic, dramatic, peaceful, joyful, sad, suspenseful, adventurous, epic.
- dominantColors: Describe the dominant colors value from: 'warm tones', 'cool tones', 'earth tones', 'vibrant colors', 'monochromatic', 'pastel colors'.
- generatedPrompt: A complete, coherent prompt paragraph in English based on the full analysis of the image.`;

            const payload = {
                contents: [{
                    role: "user",
                    parts: [{ text: analysisPrompt }, imagePart]
                }]
            };

            const apiKey = ""; // Provided by Canvas
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            const result = await response.json();

            if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
                let textResponse = result.candidates[0].content.parts[0].text.replace(/```json|```/g, '').trim();
                try {
                    const jsonResponse = JSON.parse(textResponse);
                    setLocation(jsonResponse.location || '');
                    setTime(jsonResponse.time || '');
                    setWeather(jsonResponse.weather || '');
                    setLightingStyle(jsonResponse.lightingStyle ? [jsonResponse.lightingStyle] : []);
                    setVisualStyle(jsonResponse.visualStyle ? [jsonResponse.visualStyle] : []);
                    setComposition(jsonResponse.composition ? [jsonResponse.composition] : []);
                    setCameraAngle(jsonResponse.cameraAngle ? [jsonResponse.cameraAngle] : []);
                    setMood(jsonResponse.mood ? [jsonResponse.mood] : []);
                    setDominantColors(jsonResponse.dominantColors ? [jsonResponse.dominantColors] : []);
                    setGeneratedPrompt(jsonResponse.generatedPrompt || '');
                } catch (parseError) {
                    console.error('JSON parsing error:', parseError);
                    console.error('Raw response was:', textResponse);
                    setAnalysisError('Lỗi phân tích cú pháp phản hồi từ AI. Vui lòng thử lại.');
                }
            } else {
                setAnalysisError('Không thể phân tích hình ảnh. Lỗi: ' + (result.error?.message || 'Unknown API Error'));
            }

        } catch (err) {
            console.error('Image analysis error:', err);
            setAnalysisError('Lỗi xảy ra trong quá trình phân tích.');
        } finally {
            setIsAnalyzing(false);
        }
    };


    // Main prompt generation logic from form
    const generatePromptFromForm = async () => {
        setIsLoading(true);
        setError('');
        setGeneratedPrompt('');
        setTranslatedVietnamesePrompt('');

        // --- Build Character Prompts ---
        let charactersPrompt = '';
        const imagePayloads = [];

        for (let i = 0; i < characters.length; i++) {
            const char = characters[i];
            const charType = i === 0 ? 'Main character' : `Supporting character ${i + 1}`;
            let charDetails = [];
            if (char.description) charDetails.push(char.description);
            if (char.appearance) charDetails.push(`wearing ${char.appearance}`);
            if (char.emotion) charDetails.push(`with a ${char.emotion} expression`);
            if (char.mainAction) charDetails.push(`is ${char.mainAction}`);
            if (char.relatedObject) charDetails.push(`holding ${char.relatedObject}`);
            if (char.dialogue) charDetails.push(`speaking Vietnamese: '${char.dialogue}' (with a clear, neutral accent)`);

            if (char.imageRef) {
                try {
                    const base64Data = await getBase64(char.imageRef);
                    imagePayloads.push({ inlineData: { mimeType: char.imageRef.type, data: base64Data } });
                    charDetails.push(`(use image_${imagePayloads.length} as visual reference for appearance)`);
                } catch (e) {
                    setError(`Lỗi khi đọc ảnh tham chiếu cho nhân vật ${i + 1}.`);
                    setIsLoading(false);
                    return;
                }
            }
            if (charDetails.length > 0) {
                charactersPrompt += `${charType}: ${charDetails.join(', ')}. `;
            }
        }

        // --- Build Setting Prompt with Image ---
        let settingPromptParts = [];
        if (location) settingPromptParts.push(`Location: ${location}`);
        if (time) settingPromptParts.push(`Time: ${time}`);
        if (weather) settingPromptParts.push(`Weather: ${weather}`);

        if (settingImage) {
            try {
                const base64Data = await getBase64(settingImage);
                imagePayloads.push({ inlineData: { mimeType: settingImage.type, data: base64Data } });
                settingPromptParts.push(`(use image_${imagePayloads.length} as visual reference for the setting)`);
            } catch (e) {
                setError('Lỗi khi đọc ảnh bối cảnh.');
                setIsLoading(false);
                return;
            }
        }
        
        const allEffects = [...postProcessingEffects, ...motionEffects, ...creativeEffects].filter(Boolean).join(', ');
        const promptInput = `
Based on the following details, generate a video prompt. Provide the output as a single, minified JSON object with NO extra text or markdown. The JSON object must contain ONE key: "englishPrompt". The prompt should be a single, coherent paragraph in English. Omit any empty or unselected fields.

**Scene & Setting:**
${settingPromptParts.join(', ')}

**Characters:**
${charactersPrompt.trim()}

**Visuals & Style:**
Overall Style: ${visualStyle.join(', ')}.
Color and Grading: ${dominantColors.join(', ')}.
Lighting: ${lightingStyle.join(', ')}, specifically ${lightingDetails}.
Post-Processing & Special Effects: ${allEffects}.
Surreal Elements: ${surrealElements}.

**Cinematography:**
Composition: ${composition.join(', ')}.
Camera Angle: ${cameraAngle.join(', ')}.
Camera Motion: ${cameraMotion.join(', ')}.
Focus: ${focusPoint.join(', ')}.

**Pacing, Mood & Sound:**
Overall Mood: ${mood.join(', ')}.
Editing Pace: ${editingPace.join(', ')}.
Transitions: ${transitions.join(', ')}.
Background Music: ${backgroundMusic.join(', ')}.
Sound Effects: ${soundEffects.join(', ')}.
Typography & Graphics: ${typography}.
Subtitles: ${subtitleInstructions.join(', ')}.

**Technical Specs:**
Resolution: ${resolution.join(', ')}.
Aspect Ratio: ${aspectRatio.join(', ')}.
Duration: ${duration.join(', ')} seconds.
`;

        try {
            const payload = {
                contents: [{
                    role: "user",
                    parts: [{ text: promptInput }, ...imagePayloads]
                }]
            };

            const apiKey = ""; // Provided by Canvas
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            const result = await response.json();

            if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
                let textResponse = result.candidates[0].content.parts[0].text.replace(/```json|```/g, '').trim();
                 try {
                    const jsonResponse = JSON.parse(textResponse);
                    setGeneratedPrompt(jsonResponse.englishPrompt || 'Không thể tạo prompt tiếng Anh.');
                } catch (parseError) {
                    console.error('Prompt JSON parsing error:', parseError, 'Raw response:', textResponse);
                    setError('Lỗi xử lý phản hồi từ AI. AI có thể đã trả về prompt thô. Hiển thị phản hồi thô.');
                    setGeneratedPrompt(textResponse); // Show raw response on error
                }
            } else {
                setError('Không thể tạo prompt. Lỗi: ' + (result.error?.message || 'Unknown API Error'));
                console.error('Unexpected API response structure:', result);
            }

        } catch (err) {
            setError('Đã xảy ra lỗi khi kết nối với AI.');
            console.error('Error fetching from API:', err);
        } finally {
            setIsLoading(false);
        }
    };
    
    // Function to translate the English prompt to Vietnamese
    const handleTranslate = async () => {
        if (!generatedPrompt) {
            setError('Không có prompt tiếng Anh để dịch.');
            return;
        }
        setIsTranslating(true);
        setError('');
        setTranslatedVietnamesePrompt('');

        const translationPrompt = `Translate the following English text to Vietnamese. Provide only the translated text, without any introductory phrases or explanations.\n\nEnglish text: "${generatedPrompt}"`;

        try {
            const payload = { contents: [{ role: "user", parts: [{ text: translationPrompt }] }] };
            const apiKey = ""; // Provided by Canvas
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            const result = await response.json();

            if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
                setTranslatedVietnamesePrompt(result.candidates[0].content.parts[0].text.trim());
            } else {
                setError('Không thể dịch prompt. Vui lòng thử lại.');
                console.error('Translation API response error:', result);
            }
        } catch (err) {
            setError('Đã xảy ra lỗi khi kết nối với AI để dịch.');
            console.error('Translation error:', err);
        } finally {
            setIsTranslating(false);
        }
    };

    // --- NEW: Save/Load Form Data as File ---
    const handleSaveFormToFile = () => {
        const formData = {
            location, time, weather, lightingDetails,
            // Previews are not saved directly, but their existence can be noted.
            // When loading, user will need to re-upload files.
            settingImagePreview: !!settingImagePreview,
            characters: characters.map(c => ({
                ...c,
                imageRef: null, // Don't save File object
                imageRefPreviewUrl: !!c.imageRefPreviewUrl, // Save boolean for preview existence
                id: c.id // ensure id is saved
            })),
            visualStyle, lightingStyle, dominantColors, postProcessingEffects, motionEffects,
            creativeEffects, surrealElements, composition, cameraAngle, cameraMotion, focusPoint,
            editingPace, transitions, mood, backgroundMusic, soundEffects, typography,
            subtitleInstructions, resolution, aspectRatio, duration,
        };

        try {
            const jsonString = JSON.stringify(formData, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'prompt_form_data.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            showToast('Đã lưu form ra tệp JSON thành công!');
        } catch (error) {
            console.error("Failed to save form to file:", error);
            setError('Không thể lưu form ra tệp.');
        }
    };

    const handleLoadFormFromFile = (event) => {
        const file = event.target.files[0];
        if (!file) {
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const text = e.target.result;
                const formData = JSON.parse(text);
                
                // --- Populate State from Loaded Data ---
                setLocation(formData.location || '');
                setTime(formData.time || '');
                setWeather(formData.weather || '');
                setLightingDetails(formData.lightingDetails || '');

                // Image previews are not restorable from JSON, clear them
                setSettingImage(null);
                setSettingImagePreview('');
                
                setCharacters(formData.characters.map(c => ({
                    ...c,
                    imageRef: null, // Ensure file object is null on load
                    imageRefPreviewUrl: '' // Clear preview URL
                })) || [{ id: 1, description: '', appearance: '', emotion: '', mainAction: '', relatedObject: '', dialogue: '', imageRef: null, imageRefPreviewUrl: '' }]);
                
                setVisualStyle(formData.visualStyle || []);
                setLightingStyle(formData.lightingStyle || []);
                setDominantColors(formData.dominantColors || []);
                setPostProcessingEffects(formData.postProcessingEffects || []);
                setMotionEffects(formData.motionEffects || []);
                setCreativeEffects(formData.creativeEffects || []);
                setSurrealElements(formData.surrealElements || '');
                setComposition(formData.composition || []);
                setCameraAngle(formData.cameraAngle || []);
                setCameraMotion(formData.cameraMotion || []);
                setFocusPoint(formData.focusPoint || []);
                setEditingPace(formData.editingPace || []);
                setTransitions(formData.transitions || []);
                setMood(formData.mood || []);
                setBackgroundMusic(formData.backgroundMusic || []);
                setSoundEffects(formData.soundEffects || []);
                setTypography(formData.typography || '');
                setSubtitleInstructions(formData.subtitleInstructions || []);
                setResolution(formData.resolution || []);
                setAspectRatio(formData.aspectRatio || []);
                setDuration(formData.duration || []);

                showToast('Đã tải form từ tệp thành công!');
                setError('Lưu ý: Các tệp ảnh cần được tải lên lại theo cách thủ công.');

            } catch (error) {
                console.error("Failed to load form from file:", error);
                setError('Không thể tải dữ liệu từ tệp. Tệp có thể bị hỏng hoặc không đúng định dạng.');
            } finally {
                // Reset file input value to allow loading the same file again
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
            }
        };

        reader.onerror = () => {
            setError('Lỗi khi đọc tệp.');
        };
        
        reader.readAsText(file);
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-100 flex items-center justify-center p-4 font-sans">
            <Toast message={toastMessage} show={!!toastMessage} />
            <input 
                type="file" 
                ref={fileInputRef} 
                onChange={handleLoadFormFromFile} 
                className="hidden" 
                accept=".json,application/json"
            />
            <div className="bg-white p-8 rounded-xl shadow-2xl w-full max-w-5xl border border-gray-200">
                {/* --- Main Title --- */}
                <h1 className="text-4xl font-extrabold text-center text-gray-800 mb-2 flex items-center justify-center">
                    <img src={logoUrl} alt="logo" className="h-12 w-12 mr-4" />
                    <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-blue-500">
                        Công cụ tạo Prompt cho Video/Image
                    </span>
                    <img src={logoUrl} alt="logo" className="h-12 w-12 ml-4" />
                </h1>

                <p className="text-center text-gray-600 mb-6 text-lg">
                    Sử dụng AI để phân tích ảnh hoặc điền form chi tiết để tạo prompt.
                </p>

                {/* --- Panoramic Image Analysis Section --- */}
                <div className="mb-10 p-6 bg-gray-100 rounded-lg shadow-md border border-gray-300 text-center">
                    {/* MODIFIED: Styled title - icon removed */}
                    <h2 className="text-2xl font-bold text-green-700 mb-4 flex items-center justify-center">
                        Tạo Prompt nhanh từ hình ảnh tổng thể
                    </h2>
                    <div className="flex flex-col items-center justify-center gap-4">
                        <input
                            type="file"
                            accept="image/*"
                            onChange={handlePanoramicImageChange}
                            id="panoramic-upload"
                            className="hidden"
                        />
                        <label
                            htmlFor="panoramic-upload"
                            className="cursor-pointer bg-white text-blue-600 font-semibold py-2 px-4 border border-blue-400 rounded-lg shadow-sm hover:bg-blue-50 transition duration-200"
                        >
                            Chọn tệp ảnh tổng thể
                        </label>
                        {panoramicImagePreview && (
                            <div className="mt-2">
                                <img src={panoramicImagePreview} alt="Panoramic Preview" className="max-w-xs h-auto max-h-48 object-contain rounded-lg shadow-sm mx-auto" />
                            </div>
                        )}
                        {/* MODIFIED: Button alignment and wrapping fixed */}
                        <div className="w-full max-w-md flex flex-col sm:flex-row gap-4">
                            <button
                                onClick={() => fileInputRef.current && fileInputRef.current.click()}
                                className="flex-1 bg-gray-500 text-white font-bold py-3 px-6 rounded-lg shadow-lg hover:bg-gray-600 transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-gray-300 text-base whitespace-nowrap"
                            >
                                Tải lên Form đã lưu trữ
                            </button>
                            <button
                                onClick={analyzeAndGeneratePrompt}
                                disabled={isAnalyzing || !panoramicImage}
                                className="flex-1 bg-gradient-to-r from-green-500 to-teal-500 text-white font-bold py-3 px-6 rounded-lg shadow-lg hover:from-green-600 hover:to-teal-600 transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-green-300 disabled:opacity-50 disabled:cursor-not-allowed text-base whitespace-nowrap"
                            >
                                {isAnalyzing ? (
                                    <div className="flex items-center justify-center">
                                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                                        Đang phân tích...
                                    </div>
                                ) : 'Phân tích & Tự điền Form'}
                            </button>
                        </div>
                    </div>
                    {analysisError && <p className="text-red-600 mt-3">{analysisError}</p>}
                    <p className="text-center text-gray-500 mt-4 text-sm">
                        Thông tin phân tích được sẽ tự điền vào các mục bên dưới để chỉnh sửa. Prompt hiện ở cuối trang.
                    </p>
                </div>

                {/* --- MODIFIED: Attention/Hint Section --- */}
                <div className="mb-8 p-4 bg-gray-100 rounded-lg border border-gray-200">
                    <div className="cursor-pointer flex justify-between items-center" onClick={() => setShowNote(!showNote)}>
                         <div className="flex-grow flex justify-between items-center pr-4">
                            <h3 className="text-lg font-bold text-gray-700">
                                <span className="text-blue-500">👨‍💻</span> Tool được tạo bởi 𝐍𝐓𝐇 • Group Nghiện
                            </h3>
                            <h3 className="text-lg font-bold text-gray-700">
                                Gợi ý sử dụng hiệu ứng
                            </h3>
                        </div>
                        <span className={`text-blue-500 font-bold text-xl transform transition-transform duration-300 ${showNote ? 'rotate-90' : 'rotate-0'}`}>▶</span>
                    </div>
                    {showNote && (
                        <div className="mt-4 p-4 bg-blue-50 rounded-md border border-blue-200 text-sm text-gray-800 space-y-2 animate-fade-in">
                            <p><strong>🎬 Với video ngắn, TikTok, MV:</strong> Ưu tiên nhóm hiệu ứng <strong className="text-yellow-600">"Màu sắc & Ánh sáng"</strong> + <strong className="text-red-600">"Chuyển động & Tốc độ"</strong>.</p>
                            <p><strong>🎨 Với video nghệ thuật / điện ảnh:</strong> Sử dụng mạnh mẽ cả 3 nhóm hiệu ứng để tạo phong cách độc đáo.</p>
                            <p><strong>🛒 Với video quảng cáo / sản phẩm:</strong> Tập trung vào <strong className="text-yellow-600">"Màu sắc & Ánh sáng"</strong> và các hiệu ứng chuyển cảnh sạch sẽ từ mục <strong className="text-teal-600">"Dựng phim & Âm thanh"</strong>.</p>
                        </div>
                    )}
                </div>


                {/* --- FORM SECTIONS --- */}

                {/* 1. Setting Section */}
                <div className="mb-8 p-6 bg-blue-50 rounded-lg shadow-inner border border-blue-200">
                    <h2 className="text-2xl font-bold text-blue-700 mb-4 flex items-center">
                        <svg className="w-6 h-6 mr-2 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"></path></svg>
                        1. Bối cảnh
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6">
                        <InputField label="Địa điểm" value={location} onChange={setLocation} placeholder="Ví dụ: trên bề mặt sao Hỏa, khu rừng nhiệt đới" />
                        <InputField label="Thời gian" value={time} onChange={setTime} placeholder="Ví dụ: ban đêm, bình minh, buổi chiều tà" />
                        <InputField label="Thời tiết" value={weather} onChange={setWeather} placeholder="Ví dụ: sương mù nhẹ, trời mưa, tuyết rơi" />
                        <InputField label="Chi tiết ánh sáng (tùy chọn)" value={lightingDetails} onChange={setLightingDetails} placeholder="Ví dụ: tia nắng xuyên qua kẽ lá" />
                    </div>
                    <div className="mt-4 p-3 bg-blue-100 rounded-md border border-blue-200">
                        <label className="block text-gray-700 text-sm font-semibold mb-2">Tải lên ảnh bối cảnh (tùy chọn):</label>
                        <div className="flex items-center gap-4">
                            <input type="file" accept="image/*" onChange={handleSettingImageChange}
                                   className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-white file:text-blue-700 hover:file:bg-blue-50"/>
                            <button onClick={analyzeContextImage} disabled={isAnalyzingContext || !settingImage} className="px-4 py-2 bg-blue-500 text-white rounded-lg shadow-md hover:bg-blue-600 transition disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap">
                                {isAnalyzingContext ? 'Đang...' : 'Phân tích'}
                            </button>
                        </div>
                         {contextAnalysisError && <p className="text-red-600 mt-2 text-sm">{contextAnalysisError}</p>}
                        {settingImagePreview && (
                            <div className="mt-2 text-center">
                                <img src={settingImagePreview} alt="Setting Preview" className="max-w-full h-40 object-contain rounded-lg shadow-sm mx-auto" />
                            </div>
                        )}
                    </div>
                </div>

                {/* 2. Characters Section */}
                <div className="mb-8 p-6 bg-purple-50 rounded-lg shadow-inner border border-purple-200">
                  <h2 className="text-2xl font-bold text-purple-700 mb-4 flex items-center">
                    <svg className="w-6 h-6 mr-2 text-purple-500" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path></svg>
                    2. Nhân vật
                  </h2>
                  {characters.map((char, index) => (
                    <div key={char.id} className="mb-6 p-4 border border-purple-100 rounded-lg bg-white shadow-sm">
                      <h3 className="text-xl font-semibold text-purple-600 mb-3">{index === 0 ? 'Nhân vật chính' : `Nhân vật phụ ${index + 1}`}</h3>
                      <div className="mb-4 p-3 bg-purple-100 rounded-md border border-purple-200">
                          <label className="block text-gray-700 text-sm font-semibold mb-2">Ảnh tham chiếu (giúp nhất quán ngoại hình):</label>
                          <input type="file" accept="image/*" onChange={(e) => handleCharacterImageChange(e, char.id)}
                                 className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-white file:text-purple-700 hover:file:bg-purple-50"/>
                          {char.imageRefPreviewUrl && (
                              <div className="mt-2 text-center">
                                  <img src={char.imageRefPreviewUrl} alt={`Character ${char.id} Preview`} className="max-w-full h-32 object-contain rounded-lg shadow-sm mx-auto" />
                              </div>
                          )}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          <InputField label="Mô tả nhân vật" value={char.description} onChange={(val) => handleCharacterChange(char.id, 'description', val)} placeholder="Ví dụ: một phi hành gia, một cô gái trẻ">
                              <button onClick={() => handleDetailCharacterDescription(char.id)} disabled={isDetailingCharacter === char.id}
                                      className="p-1 bg-purple-200 text-purple-700 rounded-full hover:bg-purple-300 transition text-xs disabled:opacity-50">
                                  {isDetailingCharacter === char.id ? '...' : 'Chi tiết'}
                              </button>
                          </InputField>
                          <InputField label="Ngoại hình/Trang phục" value={char.appearance} onChange={(val) => handleCharacterChange(char.id, 'appearance', val)} placeholder="Ví dụ: bộ giáp màu bạc, váy trắng bay" />
                          <InputField label="Cảm xúc/Trạng thái" value={char.emotion} onChange={(val) => handleCharacterChange(char.id, 'emotion', val)} placeholder="Ví dụ: ánh mắt mơ màng, vẻ mặt kiên quyết" />
                          <InputField label="Hành động chính" value={char.mainAction} onChange={(val) => handleCharacterChange(char.id, 'mainAction', val)} placeholder="Ví dụ: bước chậm, lướt qua các tòa nhà" />
                          <InputField label="Vật thể liên quan" value={char.relatedObject} onChange={(val) => handleCharacterChange(char.id, 'relatedObject', val)} placeholder="Ví dụ: cầm lồng đèn, mang theo kiếm" />
                          <InputField label="Lời thoại (tiếng Việt)" value={char.dialogue} onChange={(val) => handleCharacterChange(char.id, 'dialogue', val)} placeholder="Ví dụ: 'Thế giới thật kỳ diệu.'" />
                      </div>
                      {characters.length > 1 && (
                          <button onClick={() => handleRemoveCharacter(char.id)} className="mt-4 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition text-sm">Xóa nhân vật</button>
                      )}
                    </div>
                  ))}
                  <button onClick={handleAddCharacter} className="mt-2 px-6 py-2 bg-purple-500 text-white rounded-lg shadow-md hover:bg-purple-600 transition">Thêm nhân vật</button>
                </div>


                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* 3. Visual Style & Post-processing */}
                    <div className="p-6 bg-yellow-50 rounded-lg shadow-inner border border-yellow-200 flex flex-col">
                        <h2 className="text-2xl font-bold text-yellow-700 mb-4 flex items-center"><svg className="w-6 h-6 mr-2 text-yellow-500" fill="currentColor" viewBox="0 0 20 20"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"></path></svg>3. Phong cách & Hậu kỳ</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6">
                            <MultiSelectDropdown label="Phong cách hình ảnh" selectedValues={visualStyle} onChange={setVisualStyle} placeholder="Chọn phong cách..." options={options.visualStyle} />
                            <MultiSelectDropdown label="Bảng màu chủ đạo" selectedValues={dominantColors} onChange={setDominantColors} placeholder="Chọn bảng màu..." options={options.dominantColors} />
                            <MultiSelectDropdown label="Phong cách ánh sáng" selectedValues={lightingStyle} onChange={setLightingStyle} placeholder="Chọn phong cách..." options={options.lightingStyle} />
                            <MultiSelectDropdown label="Hiệu ứng Màu sắc & Ánh sáng" selectedValues={postProcessingEffects} onChange={setPostProcessingEffects} placeholder="Chọn hiệu ứng..." options={options.postProcessingEffects} />
                            <MultiSelectDropdown label="Hiệu ứng Chuyển động & Tốc độ" selectedValues={motionEffects} onChange={setMotionEffects} placeholder="Chọn hiệu ứng..." options={options.motionAndSpeedEffects} />
                            <MultiSelectDropdown label="Hiệu ứng Sáng tạo / Nghệ thuật" selectedValues={creativeEffects} onChange={setCreativeEffects} placeholder="Chọn hiệu ứng..." options={options.creativeAndArtisticEffects} />
                        </div>
                        <InputField
                            label="Yếu tố siêu thực (Nếu có)"
                            value={surrealElements}
                            onChange={setSurrealElements}
                            placeholder="Ví dụ: cá voi bay, cây phát sáng"
                            labelClassName="text-center"
                            inputClassName="text-center"
                            containerClassName="mt-auto"
                        />
                    </div>

                    {/* 4. Cinematography */}
                    <div className="p-6 bg-red-50 rounded-lg shadow-inner border border-red-200">
                        <h2 className="text-2xl font-bold text-red-700 mb-4 flex items-center"><svg className="w-6 h-6 mr-2 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 009.172 3H6.828a2 2 0 00-1.414.586L4.293 4.707A1 1 0 013.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"></path></svg>4. Quay phim</h2>
                        <MultiSelectDropdown label="Bố cục khung hình" selectedValues={composition} onChange={setComposition} placeholder="Chọn bố cục..." options={options.composition} />
                        <MultiSelectDropdown label="Góc quay" selectedValues={cameraAngle} onChange={setCameraAngle} placeholder="Chọn góc quay..." options={options.cameraAngle} />
                        <MultiSelectDropdown label="Chuyển động máy quay" selectedValues={cameraMotion} onChange={setCameraMotion} placeholder="Chọn chuyển động..." options={options.cameraMotion} />
                        <MultiSelectDropdown label="Điểm lấy nét" selectedValues={focusPoint} onChange={setFocusPoint} placeholder="Chọn điểm nét..." options={options.focusPoint} />
                    </div>
                </div>

                {/* 5. Editing & Audio */}
                <div className="mt-8 p-6 bg-teal-50 rounded-lg shadow-inner border border-teal-200">
                    <h2 className="text-2xl font-bold text-teal-700 mb-4 flex items-center"><svg className="w-6 h-6 mr-2 text-teal-500" fill="currentColor" viewBox="0 0 20 20"><path d="M12 4a1 1 0 011 1v10a1 1 0 11-2 0V5a1 1 0 011-1zM5 4a1 1 0 011 1v10a1 1 0 11-2 0V5a1 1 0 011-1zM9 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1z"></path></svg>5. Dựng phim & Âm thanh</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <MultiSelectDropdown label="Tốc độ dựng" selectedValues={editingPace} onChange={setEditingPace} placeholder="Chọn tốc độ..." options={options.editingPace} />
                        <MultiSelectDropdown label="Hiệu ứng chuyển cảnh" selectedValues={transitions} onChange={setTransitions} placeholder="Chọn chuyển cảnh..." options={options.transitions} />
                        <MultiSelectDropdown label="Tông cảm xúc (Mood)" selectedValues={mood} onChange={setMood} placeholder="Chọn tông cảm xúc..." options={options.mood} />
                        <MultiSelectDropdown label="Nhạc nền" selectedValues={backgroundMusic} onChange={setBackgroundMusic} placeholder="Chọn nhạc nền..." options={options.backgroundMusic} />
                        <MultiSelectDropdown label="Hiệu ứng âm thanh" selectedValues={soundEffects} onChange={setSoundEffects} placeholder="Chọn hiệu ứng..." options={options.soundEffects} />
                        <MultiSelectDropdown label="Hướng dẫn phụ đề" selectedValues={subtitleInstructions} onChange={setSubtitleInstructions} placeholder="Chọn phụ đề..." options={options.subtitleOptions} />
                    </div>
                     <InputField
                        label="Kiểu chữ & Đồ họa"
                        value={typography}
                        onChange={setTypography}
                        placeholder="Ví dụ: font chữ serif, chữ hiện ra mờ ảo"
                        containerClassName="mt-4"
                        labelClassName="text-center"
                        inputClassName="text-center"
                       />
                </div>

                {/* 6. Technical Specs */}
                <div className="mt-8 p-6 bg-indigo-50 rounded-lg shadow-inner border border-indigo-200">
                    <h2 className="text-2xl font-bold text-indigo-700 mb-4 flex items-center"><svg className="w-6 h-6 mr-2 text-indigo-500" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.5-.83-3.37.58-2.6 2.37a1.532 1.532 0 01-.342 2.38c-1.57.37-1.57 2.6 0 2.98a1.532 1.532 0 01.342 2.38c-.77 1.79 1.05 3.2 2.6 2.37a1.532 1.532 0 012.286.948c.38 1.56 2.6 1.56 2.98 0a1.532 1.532 0 012.286-.948c1.5.83 3.37-.58 2.6-2.37a1.532 1.532 0 01-.342-2.38c1.57-.37 1.57-2.6 0-2.98a1.532 1.532 0 01-.342-2.38c.77-1.79-1.05-3.2-2.6-2.37a1.532 1.532 0 01-2.286-.948zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"></path></svg>6. Thông số kỹ thuật</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <MultiSelectDropdown label="Độ phân giải" selectedValues={resolution} onChange={setResolution} placeholder="Chọn độ phân giải..." options={options.resolution} />
                        <MultiSelectDropdown label="Tỷ lệ khung hình" selectedValues={aspectRatio} onChange={setAspectRatio} placeholder="Chọn tỷ lệ..." options={options.aspectRatio} />
                        <MultiSelectDropdown label="Thời lượng (giây)" selectedValues={duration} onChange={setDuration} placeholder="Chọn thời lượng..." options={options.duration} />
                    </div>
                </div>

                {/* --- MODIFIED: ACTIONS & OUTPUT --- */}
                <div className="mt-10 space-y-4">
                    {/* MODIFIED: Button text updated */}
                    <button
                        onClick={handleSaveFormToFile}
                        className="w-full bg-gradient-to-r from-blue-500 to-sky-500 text-white font-bold py-3 px-6 rounded-lg shadow-lg hover:from-blue-600 hover:to-sky-600 transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300 text-lg"
                    >
                        Lưu lại Form sử dụng lần sau
                    </button>
                    
                    <button
                        onClick={generatePromptFromForm}
                        disabled={isLoading}
                        className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold py-4 px-6 rounded-lg shadow-lg hover:from-purple-700 hover:to-blue-700 transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-purple-300 disabled:opacity-50 disabled:cursor-not-allowed text-xl"
                    >
                        {isLoading ? (
                            <div className="flex items-center justify-center">
                                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                                Đang tạo Prompt từ Form...
                            </div>
                        ) : ('Tạo Prompt từ Form chi tiết')}
                    </button>
                </div>

                {error && (
                    <div className="mt-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg text-center">
                        <p className="font-bold">Thông báo:</p> <p>{error}</p>
                    </div>
                )}

                {/* Generated Prompt Section */}
                {generatedPrompt && (
                    <div className="mt-8 p-6 bg-gray-50 border border-gray-200 rounded-lg shadow-md">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4 text-center">Prompt được tạo</h2>

                        {/* English Prompt Display */}
                        <div className="mb-4">
                            <div className="bg-white p-4 rounded-lg border border-gray-300 overflow-x-auto">
                                <pre className="whitespace-pre-wrap text-gray-700 text-base leading-relaxed">{generatedPrompt}</pre>
                            </div>
                            <div className="flex justify-center items-center gap-4 mt-4">
                                <button onClick={() => copyToClipboard(generatedPrompt, 'Anh')} className="bg-blue-500 text-white font-bold py-2 px-4 rounded-lg shadow-md hover:bg-blue-600 transition">
                                    Copy prompt Tiếng Anh
                                </button>
                                <button onClick={handleTranslate} disabled={isTranslating} className="bg-purple-500 text-white font-bold py-2 px-4 rounded-lg shadow-md hover:bg-purple-600 transition disabled:opacity-50 disabled:cursor-not-allowed">
                                    {isTranslating ? (
                                        <div className="flex items-center justify-center">
                                            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                                            Đang dịch...
                                        </div>
                                    ) : 'Dịch sang Tiếng Việt'}
                                </button>
                            </div>
                        </div>

                        {/* Translated Vietnamese Prompt */}
                        {translatedVietnamesePrompt && (
                            <div className="mt-6 pt-6 border-t border-gray-300">
                                <h3 className="text-xl font-semibold text-gray-700 mb-2 text-center">Prompt Tiếng Việt</h3>
                                <div className="bg-white p-2 rounded-lg border border-gray-300">
                                    <textarea
                                        readOnly
                                        className="w-full h-32 bg-transparent border-none focus:ring-0 text-gray-700 text-base leading-relaxed resize-y p-2"
                                        value={translatedVietnamesePrompt}
                                    />
                                </div>
                                <div className="flex justify-center mt-4">
                                    <button onClick={() => copyToClipboard(translatedVietnamesePrompt, 'Việt')} className="bg-green-500 text-white font-bold py-2 px-4 rounded-lg shadow-md hover:bg-green-600 transition">
                                        Copy prompt tiếng Việt
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}

export default App;
